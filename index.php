<?php
/**
 * Banking System Installer - Main Dashboard
 * Modular installer system with authentication
 * 
 * <AUTHOR> Agent
 * @version 3.0
 * @date 2025-07-23
 */

session_start();

// Include authentication functions
require_once 'includes/auth.php';

// Simple authentication system
$is_authenticated = isset($_SESSION['installer_authenticated']) && $_SESSION['installer_authenticated'] === true;

// Load login configuration
function getLoginConfig() {
    $config_file = __DIR__ . '/includes/login-config.json';
    $defaultConfig = [
        'background_type' => 'gradient',
        'gradient_colors' => ['#667eea', '#764ba2', '#f093fb'],
        'background_image' => '',
        'blur_percentage' => 20,
        'captcha_enabled' => false,
        'captcha_type' => 'math',
        'security_notice_color' => '#ff0000'
    ];

    if (file_exists($config_file)) {
        $config = json_decode(file_get_contents($config_file), true);
        return array_merge($defaultConfig, $config ?: []);
    }

    return $defaultConfig;
}

// Generate math captcha with better randomization
function generateMathCaptcha() {
    // More varied number ranges for different operations
    $operations = ['+', '-', '*'];
    $operation = $operations[array_rand($operations)];

    switch ($operation) {
        case '+':
            // Addition: 1-15 + 1-15
            $num1 = rand(1, 15);
            $num2 = rand(1, 15);
            $answer = $num1 + $num2;
            break;
        case '-':
            // Subtraction: ensure positive result, larger range
            $num1 = rand(5, 20);
            $num2 = rand(1, $num1); // num2 always smaller than num1
            $answer = $num1 - $num2;
            break;
        case '*':
            // Multiplication: smaller numbers to keep answers reasonable
            $num1 = rand(2, 8);
            $num2 = rand(2, 8);
            $answer = $num1 * $num2;
            break;
    }

    return [
        'question' => "$num1 $operation $num2 = ?",
        'answer' => $answer,
        'operation' => $operation,
        'num1' => $num1,
        'num2' => $num2
    ];
}

// Handle login
if (isset($_POST['login'])) {
    $installer_password = getInstallerPassword();
    $login_config = getLoginConfig();

    // Check captcha if enabled
    $captcha_valid = true;
    if ($login_config['captcha_enabled']) {
        $captcha_answer = $_SESSION['captcha_answer'] ?? 0;
        $user_answer = intval($_POST['captcha_answer'] ?? 0);

        if ($user_answer !== $captcha_answer) {
            $captcha_valid = false;
            $message = "❌ Incorrect captcha answer!";
            $message_type = 'danger';
        }
    }

    if ($captcha_valid && $_POST['password'] === $installer_password) {
        $_SESSION['installer_authenticated'] = true;
        $_SESSION['installer_login_time'] = time();
        $is_authenticated = true;
        $message = "✅ Authentication successful!";
        $message_type = 'success';
    } elseif ($captcha_valid) {
        $message = "❌ Invalid password!";
        $message_type = 'danger';
    }
}

// Generate new captcha for display
$login_config = getLoginConfig();
if ($login_config['captcha_enabled']) {
    $captcha = generateMathCaptcha();
    $_SESSION['captcha_answer'] = $captcha['answer'];
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// Handle security logout
$security_logout_message = '';
if (isset($_GET['security_logout']) && isset($_SESSION['security_logout'])) {
    $logout_reason = $_SESSION['logout_reason'] ?? 'Security check failed';
    $security_logout_message = "🔒 Automatic logout: " . htmlspecialchars($logout_reason);

    // Clear the security logout session
    unset($_SESSION['security_logout']);
    unset($_SESSION['logout_reason']);
    unset($_SESSION['logout_time']);
}

// If not authenticated, show login form
if (!$is_authenticated) {
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System Installer - Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-primary: #fafafa;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f5f5f5;
            --text-primary: #1a1a1a;
            --text-secondary: #666666;
            --text-muted: #999999;
            --border-color: #e0e0e0;
            --accent-color: #2563eb;
            --shadow-lg: 0 8px 25px rgba(0,0,0,0.12);
        }

        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            <?php
            $login_config = getLoginConfig();
            if ($login_config['background_type'] === 'image' && !empty($login_config['background_image'])) {
                echo "background: url('backgrounds/{$login_config['background_image']}') center/cover no-repeat fixed;";
            } elseif ($login_config['background_type'] === 'lorem' && !empty($login_config['lorem_image'])) {
                // Generate lorem image URL
                $baseUrl = 'https://picsum.photos/1920/1080';
                $seeds = [
                    'nature-1920x1080' => '?random=nature',
                    'abstract-1920x1080' => '?random=abstract',
                    'city-1920x1080' => '?random=city',
                    'tech-1920x1080' => '?random=tech',
                    'space-1920x1080' => '?random=space',
                    'ocean-1920x1080' => '?random=ocean',
                    'mountain-1920x1080' => '?random=mountain',
                    'forest-1920x1080' => '?random=forest'
                ];
                $loremUrl = $baseUrl . ($seeds[$login_config['lorem_image']] ?? '?random=1');
                echo "background: url('$loremUrl') center/cover no-repeat fixed;";
            } else {
                $colors = $login_config['gradient_colors'];
                echo "background: linear-gradient(135deg, {$colors[0]} 0%, {$colors[1]} 50%, {$colors[2]} 100%) fixed;";
            }
            ?>
            font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            width: 100vw;
            overflow-x: hidden;
        }

        /* Removed body::before overlay that was overriding custom backgrounds */

        /* Removed backgroundShift animation keyframes */

        .login-container {
            max-width: 500px;
            width: 90%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
        }

        .card {
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(<?php echo $login_config['blur_percentage']; ?>px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
        }

        .card-body {
            padding: 3rem;
        }

        h2 {
            font-family: "JetBrains Mono", monospace;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.95);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 0.5rem;
        }

        .text-muted {
            color: rgba(255, 255, 255, 0.7) !important;
            font-weight: 400;
        }

        .form-control {
            font-family: "JetBrains Mono", monospace;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            color: rgba(255, 255, 255, 0.95);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-control:focus {
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
            outline: none;
            background: rgba(255, 255, 255, 0.3);
        }

        .form-label {
            font-family: "JetBrains Mono", monospace;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 0.75rem;
        }

        .btn {
            font-family: "JetBrains Mono", monospace;
            font-weight: 500;
            border-radius: 12px;
            padding: 1rem 2rem;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
        }

        .alert {
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            font-family: "JetBrains Mono", monospace;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            margin-top: 1.5rem;
        }

        .alert-warning {
            color: rgba(255, 255, 255, 0.9);
            border-color: rgba(255, 193, 7, 0.3);
        }

        .alert-success {
            color: rgba(255, 255, 255, 0.9);
            border-color: rgba(40, 167, 69, 0.3);
        }

        .alert-danger {
            color: rgba(255, 255, 255, 0.9);
            border-color: rgba(220, 53, 69, 0.3);
        }

        small {
            font-family: "JetBrains Mono", monospace;
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.8);
        }

        /* Additional glassmorphism effects */
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 35px 60px rgba(0, 0, 0, 0.15);
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .login-container {
                max-width: 95%;
            }

            .card-body {
                padding: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="card">
            <div class="card-body">
                    <div class="text-center mb-4">
                        <h2>🏦 System Installer</h2>
                        <p class="text-muted">Secure Access Required</p>
                    </div>
                    
                    <?php if (isset($message)): ?>
                        <div class="alert alert-<?php echo $message_type; ?>"><?php echo $message; ?></div>
                    <?php endif; ?>

                    <?php if (!empty($security_logout_message)): ?>
                        <div class="alert alert-warning">
                            <?php echo $security_logout_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="post">
                        <div class="mb-3">
                            <label for="password" class="form-label">Installer Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>

                        <?php if ($login_config['captcha_enabled'] && isset($captcha)): ?>
                        <div class="mb-3">
                            <label for="captcha_answer" class="form-label">Security Verification</label>
                            <div class="alert alert-info small mb-2">
                                <strong>Math Question:</strong> <?php echo htmlspecialchars($captcha['question']); ?>
                            </div>
                            <input type="number" class="form-control" id="captcha_answer" name="captcha_answer"
                                   placeholder="Enter your answer" required>
                        </div>
                        <?php endif; ?>

                        <button type="submit" name="login" class="btn btn-primary w-100">🔓 Access Installer</button>
                    </form>
                    
                    <div class="alert alert-warning mt-4">
                        <small style="color: <?php echo htmlspecialchars($login_config['security_notice_color']); ?>;">
                            <strong>⚠️ Security Notice:</strong> Delete this installer folder after use!
                        </small>
                    </div>
                </div>
            </div>
    </div>
</body>
</html>
<?php
    exit;
}

// Main dashboard for authenticated users
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking System Installer - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --bg-primary: #fafafa;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f5f5f5;
            --text-primary: #1a1a1a;
            --text-secondary: #666666;
            --text-muted: #999999;
            --border-color: #e0e0e0;
            --accent-color: #2563eb;
            --shadow-md: 0 4px 12px rgba(0,0,0,0.08);
            --shadow-lg: 0 8px 25px rgba(0,0,0,0.12);
        }

        body {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
            font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
            color: var(--text-primary);
        }

        .navbar {
            background: var(--bg-secondary) !important;
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .navbar-brand {
            font-family: "JetBrains Mono", monospace;
            font-weight: 600;
            color: var(--text-primary) !important;
        }

        .nav-link {
            font-family: "JetBrains Mono", monospace;
            font-weight: 400;
            color: var(--text-secondary) !important;
            transition: color 0.2s ease;
        }

        .nav-link:hover {
            color: var(--accent-color) !important;
        }

        .nav-item-group {
            align-items: center;
        }

        .nav-icon-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: rgba(255, 255, 255, 0.8) !important;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-family: "JetBrains Mono", monospace;
            font-size: 0.85rem;
        }

        .nav-icon-link:hover {
            color: #fff !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            text-decoration: none;
        }

        .nav-icon-link i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-icon-link span {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .dashboard-card {
            transition: all 0.2s ease;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            background: #ffffff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            height: 100%;
            cursor: pointer;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-color: #dee2e6;
        }

        .dashboard-card .card-body {
            padding: 1.5rem;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            height: 100%;
        }

        .dashboard-card .feature-icon {
            flex-shrink: 0;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 1.5rem;
        }

        .dashboard-card .card-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            min-height: 100%;
        }

        .dashboard-card .card-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #212529;
            line-height: 1.2;
        }

        .dashboard-card .card-text {
            font-size: 0.875rem;
            line-height: 1.4;
            color: #6c757d;
            margin-bottom: 0;
            flex-grow: 1;
        }

        /* Icon colors for different categories */
        .icon-database { background: #e3f2fd; color: #1976d2; }
        .icon-edit { background: #e8f5e8; color: #388e3c; }
        .icon-email { background: #fff3e0; color: #f57c00; }
        .icon-media { background: #f3e5f5; color: #7b1fa2; }
        .icon-dimensions { background: #e0f2f1; color: #00796b; }
        .icon-user { background: #e8f5e8; color: #2e7d32; }
        .icon-config { background: #fff3e0; color: #ef6c00; }
        .icon-db-settings { background: #e3f2fd; color: #1565c0; }
        .icon-cache { background: #f3e5f5; color: #7b1fa2; }
        .icon-table-manager { background: #e8f5e8; color: #388e3c; }
        .icon-import { background: #e1f5fe; color: #0277bd; }
        .icon-archive { background: #fff8e1; color: #f9a825; }
        .icon-mobile { background: #e8eaf6; color: #3f51b5; }
        .icon-test { background: #f1f8e9; color: #689f38; }

        /* Section headers */
        .section-header {
            font-size: 1.25rem;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 1.5rem;
            padding: 0;
            border: none;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .dashboard-card .card-body {
                padding: 1.25rem;
                gap: 0.75rem;
            }

            .dashboard-card .feature-icon {
                width: 40px;
                height: 40px;
                font-size: 1.25rem;
            }

            .dashboard-card .card-title {
                font-size: 0.95rem;
            }

            .dashboard-card .card-text {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 576px) {
            .dashboard-card .card-body {
                padding: 1rem;
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .dashboard-card .feature-icon {
                align-self: center;
            }

            .dashboard-card .card-content {
                text-align: center;
            }
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow-md);
        }

        .card-header {
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            border-radius: 12px 12px 0 0 !important;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: "JetBrains Mono", monospace;
            font-weight: 600;
            color: var(--text-primary);
        }

        .btn {
            font-family: "JetBrains Mono", monospace;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: var(--accent-color);
            border: none;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #059669;
            border: none;
        }

        .btn-warning {
            background: #d97706;
            border: none;
        }

        .btn-info {
            background: #0891b2;
            border: none;
        }

        .btn-purple {
            background: #8b5cf6;
            border: none;
            color: white;
        }

        .btn-purple:hover {
            background: #7c3aed;
            transform: translateY(-1px);
        }

        .btn-orange {
            background: #f97316;
            border: none;
            color: white;
        }

        .btn-orange:hover {
            background: #ea580c;
            transform: translateY(-1px);
        }

        .btn-teal {
            background: #14b8a6;
            border: none;
            color: white;
        }

        .btn-teal:hover {
            background: #0d9488;
            transform: translateY(-1px);
        }

        .text-purple {
            color: #8b5cf6 !important;
        }

        .text-orange {
            color: #f97316 !important;
        }

        .text-teal {
            color: #14b8a6 !important;
        }

        .badge {
            font-family: "JetBrains Mono", monospace;
            font-weight: 500;
            border-radius: 6px;
        }

        .alert {
            border: 1px solid;
            border-radius: 8px;
            font-family: "JetBrains Mono", monospace;
        }

        .alert-info {
            background: #eff6ff;
            color: #1e40af;
            border-color: #bfdbfe;
        }

        .alert-danger {
            background: #fef2f2;
            color: #991b1b;
            border-color: #fecaca;
        }

        small {
            font-family: "JetBrains Mono", monospace;
            font-size: 0.85rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">🏦 Banking System Installer</a>

            <!-- Advanced Module Icons -->
            <div class="navbar-nav mx-auto">
                <div class="nav-item-group d-flex gap-3">
                    <a class="nav-icon-link" href="user-management.php" title="User Management">
                        <i class="fas fa-user-shield"></i>
                        <span>Users</span>
                    </a>
                    <a class="nav-icon-link" href="config-settings.php" title="Configuration Settings">
                        <i class="fas fa-cogs"></i>
                        <span>Config</span>
                    </a>
                    <a class="nav-icon-link" href="database-settings.php" title="Database Settings">
                        <i class="fas fa-server"></i>
                        <span>DB Settings</span>
                    </a>
                </div>
            </div>

            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="cache-manager.php" title="Cache Manager">🧹 Cache</a>
                <a class="nav-link" href="?logout=1">🚪 Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    <h5>🎯 Banking System Installer Dashboard</h5>
                    <p class="mb-0">Choose the installation or configuration task you want to perform. Each module is designed for specific system setup needs.</p>
                </div>
            </div>
        </div>

        <!-- Core Configuration Tools -->
        <div class="row g-3 mb-4">
            <div class="col-12">
                <h2 class="section-header">Core Configuration Tools</h2>
            </div>

            <!-- Database Setup -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='database.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-database">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Database Setup</h5>
                            <p class="card-text">Configure database credentials across all system files</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Text Replacement -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='text-replacement.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-edit">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Text Replacement</h5>
                            <p class="card-text">Find and replace text across all system files for rebranding</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Configuration -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='email-config.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-email">
                            <i class="fas fa-circle"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Email Configuration</h5>
                            <p class="card-text">Update SMTP settings and email configurations system-wide</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Media Replacement -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='media-replacement.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-media">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Media Replacement</h5>
                            <p class="card-text">Replace logos, images, and videos throughout the system</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Background Controller -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='login-background-controller.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-config">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Login Background Controller</h5>
                            <p class="card-text">Customize login page background, captcha, and security settings</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logo Dimensions -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='logo-dimension-controller.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-dimensions">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Logo Dimensions</h5>
                            <p class="card-text">Control logo width and height across all system files</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Management -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='user-management.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-user">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">User Management</h5>
                            <p class="card-text">Manage system users and access permissions</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configuration Settings -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='config-settings.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-config">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Configuration</h5>
                            <p class="card-text">System configuration and general settings management</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Settings -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='database-settings.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-db-settings">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Database Settings</h5>
                            <p class="card-text">Advanced database configuration and optimization settings</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cache Management -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='cache-manager.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-cache">
                            <i class="fas fa-broom"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Cache Management</h5>
                            <p class="card-text">Clear browser cache, server cache, and force refresh media files</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table Data Manager -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='table-data-manager.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-table-manager">
                            <i class="fas fa-table"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Table Data Manager</h5>
                            <p class="card-text">Selective table data management - clear, backup, and restore specific tables</p>
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <!-- Advanced Tools -->
        <div class="row g-3 mb-4">
            <div class="col-12">
                <h2 class="section-header">Advanced Tools</h2>
            </div>

            <!-- SQL Import -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='sql-import.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-import">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">SQL Import</h5>
                            <p class="card-text">Import SQL files directly from installer to database without phpMyAdmin</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ZIP Extraction -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='zip-extraction.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-archive">
                            <i class="fas fa-cube"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">ZIP Extraction</h5>
                            <p class="card-text">Extract banking.zip to root folder with permission verification</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Portable Installer -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='create-portable-installer.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-mobile">
                            <i class="fas fa-th"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Portable Installer</h5>
                            <p class="card-text">Copy installer to root directory for easier access and deployment</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Tools -->
            <div class="col-md-6 col-xl-4">
                <div class="card dashboard-card" onclick="window.location.href='test-portable-installer.php'">
                    <div class="card-body">
                        <div class="feature-icon icon-test">
                            <i class="fas fa-pencil-alt"></i>
                        </div>
                        <div class="card-content">
                            <h5 class="card-title">Test Tools</h5>
                            <p class="card-text">Test installer functionality and path detection across locations</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- System Information -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="text-primary mb-0">System Information</h5>
                    </div>
                    <div class="card-body" style="background-color: #f8f9fa;">
                        <div class="row g-4">
                            <!-- First Row -->
                            <div class="col-md-4">
                                <div class="d-flex justify-content-between align-items-center p-3 bg-white rounded border">
                                    <span class="text-muted">PHP Version:</span>
                                    <span class="text-primary fw-bold"><?php echo PHP_VERSION; ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex justify-content-between align-items-center p-3 bg-white rounded border">
                                    <span class="text-muted">Server Software:</span>
                                    <span class="text-primary fw-bold"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'LiteSpeed'; ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="p-3 bg-white rounded border">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="text-muted">Document Root:</span>
                                    </div>
                                    <div class="text-primary fw-bold" style="font-size: 0.75rem; line-height: 1.3; word-break: break-all; font-family: 'Courier New', monospace;">
                                        <?php
                                        $docRoot = $_SERVER['DOCUMENT_ROOT'] ?? '/home/<USER>/domains/tradingpilotpro.com/public_html';
                                        echo htmlspecialchars($docRoot);
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Second Row -->
                            <div class="col-md-4">
                                <div class="d-flex justify-content-between align-items-center p-3 bg-white rounded border">
                                    <span class="text-muted">Current Time:</span>
                                    <span class="text-primary fw-bold"><?php echo date('d/m/Y H:i:s'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Warning -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-danger">
                    <h6>🔒 Security Warning</h6>
                    <ul class="mb-0">
                        <li><strong>Delete this installer folder immediately after use!</strong></li>
                        <li>This tool can modify critical system files</li>
                        <li>Always backup your system before making changes</li>
                        <li>Only use this tool during initial setup or maintenance</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
