<?php
/**
 * Text Replacement Module
 * Advanced text find and replace across the banking system
 */

require_once 'includes/auth.php';
requireAuth();

class TextReplacer {

    private $scan_directories;
    private $path_manager;

    public function __construct() {
        $this->path_manager = getPathManager();
        $this->scan_directories = $this->path_manager->getScanDirectories();
    }
    
    private $file_extensions = [
        'php', 'html', 'css', 'js', 'sql', 'txt', 'md'
    ];
    
    private $exclude_files = [
        'installer',
        '.backup.',
        '.git',
        'node_modules',
        'vendor'
    ];
    
    /**
     * Scan all files for text replacement
     */
    public function scanFiles() {
        $files = [];
        
        foreach ($this->scan_directories as $dir) {
            if (is_dir($dir)) {
                $files = array_merge($files, $this->scanDirectory($dir));
            }
        }
        
        return $files;
    }
    
    /**
     * Recursively scan directory
     */
    private function scanDirectory($dir) {
        $files = [];
        
        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $filepath = $file->getPathname();
                    $extension = strtolower($file->getExtension());
                    
                    if (in_array($extension, $this->file_extensions) && !$this->shouldExcludeFile($filepath)) {
                        $files[] = [
                            'path' => $filepath,
                            'name' => $file->getFilename(),
                            'size' => $file->getSize(),
                            'extension' => $extension,
                            'modified' => date('Y-m-d H:i:s', $file->getMTime()),
                            'writable' => is_writable($filepath)
                        ];
                    }
                }
            }
        } catch (Exception $e) {
            // Skip directories that can't be read
        }
        
        return $files;
    }
    
    /**
     * Check if file should be excluded
     */
    private function shouldExcludeFile($filepath) {
        foreach ($this->exclude_files as $exclude) {
            if (strpos($filepath, $exclude) !== false) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Preview text replacement
     */
    public function previewReplace($searchText, $replaceText, $caseSensitive = true, $files = null) {
        if ($files === null) {
            $files = $this->scanFiles();
        }
        
        $previewResults = [];
        $totalMatches = 0;
        
        foreach ($files as $file) {
            $filepath = $file['path'];
            if (!file_exists($filepath) || !is_readable($filepath)) {
                continue;
            }
            
            $content = file_get_contents($filepath);
            $matches = [];
            
            if ($caseSensitive) {
                $matchCount = preg_match_all('/' . preg_quote($searchText, '/') . '/', $content, $matches, PREG_OFFSET_CAPTURE);
            } else {
                $matchCount = preg_match_all('/' . preg_quote($searchText, '/') . '/i', $content, $matches, PREG_OFFSET_CAPTURE);
            }
            
            if ($matchCount > 0) {
                $fileMatches = [];
                foreach ($matches[0] as $match) {
                    $lineNumber = substr_count(substr($content, 0, $match[1]), "\n") + 1;
                    $lineStart = strrpos(substr($content, 0, $match[1]), "\n");
                    $lineStart = $lineStart === false ? 0 : $lineStart + 1;
                    $lineEnd = strpos($content, "\n", $match[1]);
                    $lineEnd = $lineEnd === false ? strlen($content) : $lineEnd;
                    $lineContent = substr($content, $lineStart, $lineEnd - $lineStart);
                    
                    $fileMatches[] = [
                        'line' => $lineNumber,
                        'content' => trim($lineContent),
                        'position' => $match[1]
                    ];
                }
                
                $previewResults[] = [
                    'file' => $file,
                    'matches' => $fileMatches,
                    'match_count' => $matchCount
                ];
                
                $totalMatches += $matchCount;
            }
        }
        
        return [
            'results' => $previewResults,
            'total_matches' => $totalMatches,
            'total_files' => count($previewResults)
        ];
    }
    
    /**
     * Execute text replacement
     */
    public function replaceText($searchText, $replaceText, $caseSensitive = true, $files = null) {
        if ($files === null) {
            $files = $this->scanFiles();
        }
        
        $results = [];
        $totalReplacements = 0;
        $backupTimestamp = date('Y-m-d-H-i-s');
        
        foreach ($files as $file) {
            $filepath = $file['path'];
            if (!file_exists($filepath) || !is_writable($filepath)) {
                continue;
            }
            
            $content = file_get_contents($filepath);
            $originalContent = $content;
            
            if ($caseSensitive) {
                $newContent = str_replace($searchText, $replaceText, $content, $count);
            } else {
                $newContent = str_ireplace($searchText, $replaceText, $content, $count);
            }
            
            if ($count > 0) {
                // Create backup
                $backupPath = createBackup($filepath);
                
                // Write new content
                file_put_contents($filepath, $newContent);
                
                $results[] = [
                    'file' => $file,
                    'replacements' => $count,
                    'backup' => $backupPath,
                    'success' => true
                ];
                
                $totalReplacements += $count;
            }
        }
        
        return [
            'results' => $results,
            'total_replacements' => $totalReplacements,
            'total_files' => count($results),
            'backup_timestamp' => $backupTimestamp
        ];
    }
}

// Initialize text replacer
$textReplacer = new TextReplacer();

// Handle form submissions
$message = '';
$messageType = '';
$previewResults = [];
$replaceResults = [];
$debugInfo = [];

if ($_POST) {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'scan_files') {
            $scannedFiles = $textReplacer->scanFiles();
            $debugInfo = [
                'total_files' => count($scannedFiles),
                'files' => array_slice($scannedFiles, 0, 10) // Show first 10 files
            ];
            $message = "🔍 File scan completed: Found " . count($scannedFiles) . " files to scan.";
            $messageType = 'info';
        }
        elseif ($_POST['action'] === 'preview') {
            $searchText = $_POST['search_text'] ?? '';
            $replaceText = $_POST['replace_text'] ?? '';
            $caseSensitive = isset($_POST['case_sensitive']);
            
            if (!empty($searchText)) {
                $previewResults = $textReplacer->previewReplace($searchText, $replaceText, $caseSensitive);
                $message = "🔍 Preview completed: Found {$previewResults['total_matches']} matches in {$previewResults['total_files']} files.";
                $messageType = 'info';
            } else {
                $message = "⚠️ Please enter text to search for.";
                $messageType = 'warning';
            }
        }
        elseif ($_POST['action'] === 'execute') {
            $searchText = $_POST['search_text'] ?? '';
            $replaceText = $_POST['replace_text'] ?? '';
            $caseSensitive = isset($_POST['case_sensitive']);
            
            if (!empty($searchText)) {
                $replaceResults = $textReplacer->replaceText($searchText, $replaceText, $caseSensitive);
                $message = "✅ Text replacement completed! Made {$replaceResults['total_replacements']} replacements in {$replaceResults['total_files']} files.";
                $messageType = 'success';
                
                logSecurityEvent('Text replacement completed via installer', 'INFO', [
                    'search_text' => $searchText,
                    'replace_text' => $replaceText,
                    'total_replacements' => $replaceResults['total_replacements'],
                    'files_modified' => $replaceResults['total_files']
                ]);
            } else {
                $message = "⚠️ Please enter text to search for.";
                $messageType = 'warning';
            }
        }
    }
}

// Display the page
echo getPageHeader('Text Replacement', 'Find and replace text across all system files for comprehensive rebranding');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📝 Advanced Text Find & Replace</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Replace text across all PHP, HTML, CSS, JS, and SQL files in the banking system. Perfect for rebranding operations.</p>
                
                <!-- Quick Actions -->
                <div class="mb-3">
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="scan_files">
                        <button type="submit" class="btn btn-outline-info btn-sm">🔍 Scan Files</button>
                    </form>
                </div>

                <!-- Text Replacement Form -->
                <form method="post">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="search_text" class="form-label">Search Text</label>
                                <input type="text" class="form-control" id="search_text" name="search_text"
                                       placeholder="e.g., Seabridge Credit Bank" value="<?php echo htmlspecialchars($_POST['search_text'] ?? ''); ?>" required>
                                <div class="form-text">Text to find and replace</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="replace_text" class="form-label">Replace With</label>
                                <input type="text" class="form-control" id="replace_text" name="replace_text"
                                       placeholder="e.g., Mentor Credit Bank" value="<?php echo htmlspecialchars($_POST['replace_text'] ?? ''); ?>" required>
                                <div class="form-text">New text to replace with</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="case_sensitive" name="case_sensitive"
                                   <?php echo isset($_POST['case_sensitive']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="case_sensitive">
                                Case Sensitive Matching
                            </label>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" name="action" value="preview" class="btn btn-info">
                            🔍 Preview Changes
                        </button>
                        <button type="submit" name="action" value="execute" class="btn btn-warning"
                                onclick="return confirm('This will modify files across the entire banking system. Backups will be created. Continue?')">
                            🔄 Execute Replacement
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Debug Information -->
<?php if (!empty($debugInfo)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔍 File Scan Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>Files Found:</strong> <?php echo $debugInfo['total_files']; ?> files available for text replacement
                </div>

                <div class="result-box">
                    <h6>Sample Files (first 10):</h6>
                    <?php foreach ($debugInfo['files'] as $file): ?>
                        <div class="file-item">
                            <strong>📄 <?php echo htmlspecialchars($file['name']); ?></strong>
                            <span class="badge bg-secondary"><?php echo $file['extension']; ?></span>
                            <span class="badge bg-info"><?php echo number_format($file['size']/1024, 1); ?>KB</span>
                            <?php if (!$file['writable']): ?>
                                <span class="badge bg-warning">Read-only</span>
                            <?php endif; ?>
                            <br>
                            <small class="text-muted"><?php echo htmlspecialchars($file['path']); ?></small>
                        </div>
                    <?php endforeach; ?>

                    <?php if ($debugInfo['total_files'] > 10): ?>
                        <small class="text-muted">... and <?php echo $debugInfo['total_files'] - 10; ?> more files</small>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Preview Results -->
<?php if (!empty($previewResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔍 Preview Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    Found <strong><?php echo $previewResults['total_matches']; ?> matches</strong>
                    in <strong><?php echo $previewResults['total_files']; ?> files</strong>
                </div>

                <div class="result-box">
                    <?php foreach ($previewResults['results'] as $result): ?>
                        <div class="file-item">
                            <h6>
                                📁 <?php echo htmlspecialchars($result['file']['name']); ?>
                                <span class="badge bg-primary"><?php echo $result['match_count']; ?> matches</span>
                            </h6>
                            <small class="text-muted"><?php echo htmlspecialchars($result['file']['path']); ?></small>

                            <?php foreach (array_slice($result['matches'], 0, 5) as $match): ?>
                                <div class="mt-2 p-2 bg-light border-start border-warning border-3">
                                    <small>Line <?php echo $match['line']; ?>:</small><br>
                                    <?php
                                    $content = htmlspecialchars($match['content']);
                                    $search = htmlspecialchars($_POST['search_text'] ?? '');
                                    $replace = htmlspecialchars($_POST['replace_text'] ?? '');

                                    if (isset($_POST['case_sensitive'])) {
                                        $highlighted = str_replace($search, '<span class="match-highlight">' . $search . '</span>', $content);
                                    } else {
                                        $highlighted = str_ireplace($search, '<span class="match-highlight">' . $search . '</span>', $content);
                                    }
                                    echo $highlighted;
                                    ?>
                                </div>
                            <?php endforeach; ?>

                            <?php if (count($result['matches']) > 5): ?>
                                <small class="text-muted">... and <?php echo count($result['matches']) - 5; ?> more matches</small>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Replacement Results -->
<?php if (!empty($replaceResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>✅ Replacement Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    Made <strong><?php echo $replaceResults['total_replacements']; ?> replacements</strong>
                    in <strong><?php echo $replaceResults['total_files']; ?> files</strong>
                </div>

                <div class="result-box">
                    <?php foreach ($replaceResults['results'] as $result): ?>
                        <div class="file-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>📁 <?php echo htmlspecialchars($result['file']['name']); ?></strong><br>
                                    <small class="text-muted"><?php echo htmlspecialchars($result['file']['path']); ?></small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success"><?php echo $result['replacements']; ?> replacements</span><br>
                                    <?php if ($result['backup']): ?>
                                        <small class="text-muted">Backup: <?php echo basename($result['backup']); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="alert alert-info mt-3">
                    <strong>💡 Next Steps:</strong>
                    <ul class="mb-0">
                        <li>Test the system to ensure all changes work correctly</li>
                        <li>Check that no functionality was broken by the text replacement</li>
                        <li>Backup files are available for rollback if needed</li>
                        <li>Consider clearing any caches that might contain old text</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php echo getPageFooter(); ?>

<?php include 'includes/footer.php'; ?>
