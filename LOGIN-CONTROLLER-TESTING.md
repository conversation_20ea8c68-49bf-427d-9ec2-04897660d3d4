# Login Background Controller - Testing Guide

## Issues Fixed

### 1. **Background Changes Not Applying**
- **Problem**: Background changes weren't showing on login page
- **Solution**: Login page now dynamically reads `includes/login-config.json`
- **Test**: Change background in controller, then logout and check login page

### 2. **Background Preview Not Working**
- **Problem**: Preview showing broken images
- **Solution**: Fixed CSS generation and added cache-busting
- **Test**: Preview should update immediately when changing settings

### 3. **Math Captcha Not Showing**
- **Problem**: Captcha wasn't appearing on login page
- **Solution**: Added captcha toggle functionality
- **Test**: Enable captcha in controller, then check login page

### 4. **Math Randomization Improved**
- **Problem**: Limited math variety
- **Solution**: Better number ranges and operations
- **Test**: Refresh login page multiple times to see variety

## How to Test Locally

### Step 1: Setup
```bash
# Copy installer to your local server
cp -r banking/mod/installer/ /your/web/root/installer/

# Or use PHP built-in server
cd banking/mod/installer
php -S localhost:8000
```

### Step 2: Login
- Navigate to `http://localhost:8000/` (or your local path)
- Password: `BankInstaller2025!`

### Step 3: Test Background Controller
1. Go to "Login Background Controller" from dashboard
2. Click "Test Configuration" to see current settings
3. Try these tests:

#### Test A: Gradient Background
1. Select "Gradient Background"
2. Change colors using color pickers
3. Adjust blur slider
4. Click "Save Configuration"
5. Logout and check login page

#### Test B: Image Background
1. Upload a background image (JPG/PNG)
2. Select "Image Background"
3. Choose uploaded image
4. Adjust blur percentage
5. Save and test login page

#### Test C: Math Captcha
1. Enable "Math Captcha" toggle
2. Save configuration
3. Logout and verify math question appears
4. Try different math operations (refresh page)

#### Test D: Security Notice Color
1. Change security notice color to red (or any color)
2. Save configuration
3. Check login page for colored text

## Expected Math Captcha Variety

The improved math system now generates:

### Addition (1-15 + 1-15)
- Examples: `7 + 12 = ?`, `3 + 8 = ?`, `15 + 4 = ?`

### Subtraction (5-20 - 1-smaller)
- Examples: `18 - 7 = ?`, `12 - 5 = ?`, `20 - 3 = ?`

### Multiplication (2-8 × 2-8)
- Examples: `6 × 4 = ?`, `3 × 7 = ?`, `8 × 5 = ?`

## File Structure

```
installer/
├── login-background-controller.php    # Main controller
├── test-login-config.php             # Debug/test page
├── index.php                         # Login page (modified)
├── includes/
│   ├── login-config.json             # Configuration file
│   └── auth.php                      # Authentication
└── backgrounds/                      # Uploaded images
    └── (your uploaded images)
```

## Configuration File Format

```json
{
    "background_type": "gradient|image",
    "gradient_colors": ["#color1", "#color2", "#color3"],
    "background_image": "filename.jpg",
    "blur_percentage": 20,
    "captcha_enabled": true,
    "captcha_type": "math",
    "security_notice_color": "#ff0000"
}
```

## Troubleshooting

### Background Not Changing
1. Check if `includes/login-config.json` exists
2. Verify file permissions (should be writable)
3. Clear browser cache
4. Check browser console for errors

### Images Not Showing
1. Verify `backgrounds/` directory exists
2. Check uploaded image file permissions
3. Ensure image file extensions are supported (jpg, png, gif, webp)

### Captcha Not Working
1. Verify `captcha_enabled` is `true` in config
2. Check if session is working properly
3. Clear browser cache and cookies

### Preview Not Updating
1. Hard refresh browser (Ctrl+F5)
2. Check if configuration was saved successfully
3. Verify CSS is loading properly

## Security Notes

- **Delete installer folder after use!**
- Use strong passwords
- Enable HTTPS in production
- Limit file upload sizes
- Validate all user inputs

## Testing Checklist

- [ ] Login with correct password
- [ ] Access login background controller
- [ ] Change gradient colors and see preview
- [ ] Upload background image
- [ ] Switch between gradient and image
- [ ] Adjust blur percentage
- [ ] Enable/disable math captcha
- [ ] Change security notice color
- [ ] Save configuration successfully
- [ ] Logout and verify login page changes
- [ ] Test math captcha functionality
- [ ] Verify different math operations appear
- [ ] Check security notice color change

## Success Indicators

✅ **Background Controller Working**: Preview updates when changing settings  
✅ **Login Page Dynamic**: Changes apply immediately after saving  
✅ **Math Captcha Active**: Questions appear when enabled  
✅ **Math Variety**: Different operations and number ranges  
✅ **Security Notice**: Text color changes as configured  
✅ **File Uploads**: Images upload and display correctly  

The system is now fully functional for offline testing!
