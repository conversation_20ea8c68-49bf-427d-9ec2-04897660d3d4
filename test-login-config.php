<?php
/**
 * Test Login Configuration
 * Debug page to check login configuration
 */

require_once 'includes/auth.php';
requireAuth();

// Load login configuration
function getLoginConfig() {
    $config_file = __DIR__ . '/includes/login-config.json';
    $defaultConfig = [
        'background_type' => 'gradient',
        'gradient_colors' => ['#667eea', '#764ba2', '#f093fb'],
        'background_image' => '',
        'blur_percentage' => 20,
        'captcha_enabled' => false,
        'captcha_type' => 'math',
        'security_notice_color' => '#ff0000'
    ];
    
    if (file_exists($config_file)) {
        $config = json_decode(file_get_contents($config_file), true);
        return array_merge($defaultConfig, $config ?: []);
    }
    
    return $defaultConfig;
}

$config = getLoginConfig();

echo getPageHeader('Test Login Configuration', 'Debug login configuration settings');
?>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>🔧 Current Configuration</h5>
            </div>
            <div class="card-body">
                <pre><?php echo htmlspecialchars(json_encode($config, JSON_PRETTY_PRINT)); ?></pre>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>👁️ Background Preview</h5>
            </div>
            <div class="card-body">
                <div style="
                    height: 200px; 
                    border-radius: 10px; 
                    position: relative;
                    overflow: hidden;
                    border: 1px solid #ddd;
                    <?php if ($config['background_type'] === 'image' && !empty($config['background_image'])): ?>
                        background: url('backgrounds/<?php echo htmlspecialchars($config['background_image']); ?>?v=<?php echo time(); ?>') center/cover no-repeat;
                    <?php else: ?>
                        background: linear-gradient(135deg, <?php echo htmlspecialchars($config['gradient_colors'][0]); ?> 0%, <?php echo htmlspecialchars($config['gradient_colors'][1]); ?> 50%, <?php echo htmlspecialchars($config['gradient_colors'][2]); ?> 100%);
                    <?php endif; ?>
                ">
                    <div style="
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        backdrop-filter: blur(<?php echo $config['blur_percentage']; ?>px);
                        background: rgba(255, 255, 255, 0.25);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        text-align: center;
                        font-family: 'JetBrains Mono', monospace;
                    ">
                        <div>
                            <h6>🏦 System Installer</h6>
                            <small>Login Preview</small>
                            <br><br>
                            <small style="color: <?php echo htmlspecialchars($config['security_notice_color']); ?>;">
                                <strong>⚠️ Security Notice:</strong> Delete this installer folder after use!
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📁 File Status</h5>
            </div>
            <div class="card-body">
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Configuration File
                        <?php if (file_exists(__DIR__ . '/includes/login-config.json')): ?>
                            <span class="badge bg-success">Exists</span>
                        <?php else: ?>
                            <span class="badge bg-warning">Missing</span>
                        <?php endif; ?>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Backgrounds Directory
                        <?php if (is_dir(__DIR__ . '/backgrounds/')): ?>
                            <span class="badge bg-success">Exists</span>
                        <?php else: ?>
                            <span class="badge bg-warning">Missing</span>
                        <?php endif; ?>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        Background Images
                        <?php 
                        $bg_dir = __DIR__ . '/backgrounds/';
                        $image_count = 0;
                        if (is_dir($bg_dir)) {
                            $files = scandir($bg_dir);
                            foreach ($files as $file) {
                                if ($file !== '.' && $file !== '..' && preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $file)) {
                                    $image_count++;
                                }
                            }
                        }
                        ?>
                        <span class="badge bg-info"><?php echo $image_count; ?> images</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="text-center">
            <a href="login-background-controller.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Back to Login Controller
            </a>
            <a href="index.php?logout=1" class="btn btn-secondary ms-2" target="_blank">
                <i class="fas fa-external-link-alt"></i> Test Login Page
            </a>
        </div>
    </div>
</div>

<?php echo getPageFooter(); ?>
