<?php
/**
 * Simple Email Configuration (Fallback)
 * Basic email config without PHPMailer dependency
 */

require_once 'includes/auth.php';
requireAuth();

/**
 * Simple SMTP connection test
 */
function simpleTestSMTP($config) {
    $host = $config['test_smtp_host'] ?? '';
    $port = intval($config['test_smtp_port'] ?? 587);
    
    if (empty($host)) {
        return [
            'success' => false,
            'message' => '❌ SMTP host is required'
        ];
    }
    
    // Test basic connection
    $socket = @fsockopen($host, $port, $errno, $errstr, 10);
    
    if (!$socket) {
        return [
            'success' => false,
            'message' => "❌ Cannot connect to SMTP server {$host}:{$port}. Error: {$errstr} ({$errno})"
        ];
    }

    // Read initial response
    $response = fgets($socket, 512);
    fclose($socket);
    
    if (substr($response, 0, 3) !== '220') {
        return [
            'success' => false,
            'message' => "❌ SMTP server did not respond correctly. Response: " . trim($response)
        ];
    }

    return [
        'success' => true,
        'message' => "✅ Basic SMTP connection successful! Connected to {$host}:{$port}. Note: This is a basic connection test only."
    ];
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_POST && isset($_POST['action']) && $_POST['action'] === 'test_smtp') {
    try {
        $testResult = simpleTestSMTP($_POST);
        $message = $testResult['message'];
        $messageType = $testResult['success'] ? 'success' : 'danger';
    } catch (Exception $e) {
        $message = "❌ SMTP test failed: " . htmlspecialchars($e->getMessage());
        $messageType = 'danger';
    }
}

echo getPageHeader('Simple Email Configuration', 'Basic email configuration and testing');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📧 Simple Email Configuration Test</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>ℹ️ Note:</strong> This is a simplified version of the email configuration tool. 
                    It only tests basic SMTP connectivity without sending actual emails.
                </div>

                <form method="post">
                    <input type="hidden" name="action" value="test_smtp">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_smtp_host" class="form-label">SMTP Host</label>
                                <input type="text" class="form-control" id="test_smtp_host" name="test_smtp_host" 
                                       placeholder="smtp.gmail.com" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_smtp_port" class="form-label">SMTP Port</label>
                                <input type="number" class="form-control" id="test_smtp_port" name="test_smtp_port" 
                                       value="587" required>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plug"></i> Test SMTP Connection
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔧 Troubleshooting</h5>
            </div>
            <div class="card-body">
                <h6>Common SMTP Settings:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Provider</th>
                                <th>SMTP Host</th>
                                <th>Port (TLS)</th>
                                <th>Port (SSL)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Gmail</td>
                                <td>smtp.gmail.com</td>
                                <td>587</td>
                                <td>465</td>
                            </tr>
                            <tr>
                                <td>Outlook/Hotmail</td>
                                <td>smtp-mail.outlook.com</td>
                                <td>587</td>
                                <td>465</td>
                            </tr>
                            <tr>
                                <td>Yahoo</td>
                                <td>smtp.mail.yahoo.com</td>
                                <td>587</td>
                                <td>465</td>
                            </tr>
                            <tr>
                                <td>SendGrid</td>
                                <td>smtp.sendgrid.net</td>
                                <td>587</td>
                                <td>465</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-warning mt-3">
                    <strong>⚠️ For Full Email Testing:</strong>
                    <p>To send actual test emails, you need to install PHPMailer:</p>
                    <code>composer require phpmailer/phpmailer</code>
                    <p class="mt-2">Then use the <a href="email-config.php">full email configuration tool</a>.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="email-config-debug.php" class="btn btn-info">
            <i class="fas fa-bug"></i> Debug Email Config
        </a>
        <a href="email-config.php" class="btn btn-warning">
            <i class="fas fa-envelope"></i> Try Full Email Config
        </a>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php echo getPageFooter(); ?>
