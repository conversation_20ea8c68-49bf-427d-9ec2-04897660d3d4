<?php
/**
 * Test Verifying Permission Integration
 * Verify that verifying-permission directory is included in installer modules
 */

require_once 'includes/auth.php';
requireAuth();

echo getPageHeader('Verifying Permission Integration Test', 'Testing integration of verifying-permission directory');

// Test Text Replacement
require_once 'text-replacement.php';
$textReplacer = new TextReplacer();

// Test Logo Dimension Controller
require_once 'logo-dimension-controller.php';
$logoController = new LogoDimensionController();

// Test Favicon Replacement
require_once 'favicon-replacement.php';
$faviconReplacer = new FaviconReplacer();

?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🧪 Verifying Permission Integration Test</h5>
            </div>
            <div class="card-body">
                
                <h6>📁 Directory Contents:</h6>
                <div class="alert alert-info">
                    <strong>verifying-permission directory contains:</strong>
                    <ul class="mb-0">
                        <li><code>index.html</code> - Contains site title: "Seabridge Credit Bank"</li>
                        <li><code>favicon.png</code> - Favicon that needs replacement</li>
                        <li><code>logodark.png</code> - Logo that may need dimension control</li>
                        <li><code>css/style.css</code> - May contain styling references</li>
                    </ul>
                </div>
                
                <h6 class="mt-4">🔍 Text Replacement Test:</h6>
                <?php
                $textFiles = $textReplacer->scanFiles();
                $verifyingPermissionFiles = array_filter($textFiles, function($file) {
                    return strpos($file['path'], 'verifying-permission') !== false;
                });
                ?>
                
                <div class="alert alert-<?php echo !empty($verifyingPermissionFiles) ? 'success' : 'danger'; ?>">
                    <strong><?php echo !empty($verifyingPermissionFiles) ? '✅' : '❌'; ?> Text Replacement Module:</strong><br>
                    Found <?php echo count($verifyingPermissionFiles); ?> files in verifying-permission directory
                    
                    <?php if (!empty($verifyingPermissionFiles)): ?>
                        <ul class="mt-2 mb-0">
                            <?php foreach ($verifyingPermissionFiles as $file): ?>
                                <li><code><?php echo htmlspecialchars($file['name']); ?></code> - <?php echo $file['extension']; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
                
                <h6 class="mt-4">📐 Logo Dimension Controller Test:</h6>
                <?php
                $logoResults = $logoController->scanLogoDimensions();
                $verifyingPermissionLogos = array_filter($logoResults, function($result) {
                    return strpos($result['file']['path'], 'verifying-permission') !== false;
                });
                ?>
                
                <div class="alert alert-<?php echo !empty($verifyingPermissionLogos) ? 'success' : 'info'; ?>">
                    <strong><?php echo !empty($verifyingPermissionLogos) ? '✅' : 'ℹ️'; ?> Logo Dimension Controller:</strong><br>
                    Found <?php echo count($verifyingPermissionLogos); ?> files with logo dimensions in verifying-permission directory
                    
                    <?php if (!empty($verifyingPermissionLogos)): ?>
                        <ul class="mt-2 mb-0">
                            <?php foreach ($verifyingPermissionLogos as $result): ?>
                                <li>
                                    <code><?php echo htmlspecialchars($result['file']['name']); ?></code> - 
                                    <?php echo count($result['matches']); ?> logo dimension patterns
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php else: ?>
                        <small>No logo dimension patterns found (this is normal if no logos have explicit dimensions)</small>
                    <?php endif; ?>
                </div>
                
                <h6 class="mt-4">⭐ Favicon Replacement Test:</h6>
                <?php
                $currentFavicons = $faviconReplacer->getCurrentFavicons();
                $verifyingPermissionFavicon = array_filter($currentFavicons, function($favicon) {
                    return strpos($favicon['path'], 'verifying-permission') !== false;
                });
                ?>
                
                <div class="alert alert-<?php echo !empty($verifyingPermissionFavicon) ? 'success' : 'warning'; ?>">
                    <strong><?php echo !empty($verifyingPermissionFavicon) ? '✅' : '⚠️'; ?> Favicon Replacement Module:</strong><br>
                    
                    <?php if (!empty($verifyingPermissionFavicon)): ?>
                        <?php $favicon = reset($verifyingPermissionFavicon); ?>
                        Verifying permission favicon: 
                        <span class="badge bg-<?php echo $favicon['exists'] ? 'success' : 'warning'; ?>">
                            <?php echo $favicon['exists'] ? 'Exists' : 'Missing'; ?>
                        </span>
                        
                        <?php if ($favicon['exists']): ?>
                            <br><small>
                                Size: <?php echo number_format($favicon['size']); ?> bytes, 
                                Dimensions: <?php echo $favicon['dimensions']; ?>
                            </small>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                
                <h6 class="mt-4">🎯 Site Title Test:</h6>
                <?php
                $indexHtmlPath = '../../verifying-permission/index.html';
                $siteTitle = '';
                if (file_exists($indexHtmlPath)) {
                    $content = file_get_contents($indexHtmlPath);
                    if (preg_match('/<title>(.*?)<\/title>/', $content, $matches)) {
                        $siteTitle = $matches[1];
                    }
                }
                ?>
                
                <div class="alert alert-<?php echo !empty($siteTitle) ? 'success' : 'warning'; ?>">
                    <strong><?php echo !empty($siteTitle) ? '✅' : '⚠️'; ?> Site Title Detection:</strong><br>
                    
                    <?php if (!empty($siteTitle)): ?>
                        Current title: <code><?php echo htmlspecialchars($siteTitle); ?></code><br>
                        <small>This can now be replaced using the Text Replacement tool</small>
                    <?php else: ?>
                        Could not detect site title in verifying-permission/index.html
                    <?php endif; ?>
                </div>
                
                <h6 class="mt-4">📋 Integration Summary:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Module</th>
                                <th>Status</th>
                                <th>Files Found</th>
                                <th>Action Available</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Text Replacement</td>
                                <td>
                                    <span class="badge bg-<?php echo !empty($verifyingPermissionFiles) ? 'success' : 'danger'; ?>">
                                        <?php echo !empty($verifyingPermissionFiles) ? 'Integrated' : 'Not Found'; ?>
                                    </span>
                                </td>
                                <td><?php echo count($verifyingPermissionFiles); ?></td>
                                <td>Replace "Seabridge Credit Bank" → "Northland Credit Union"</td>
                            </tr>
                            <tr>
                                <td>Logo Dimensions</td>
                                <td>
                                    <span class="badge bg-success">Integrated</span>
                                </td>
                                <td><?php echo count($verifyingPermissionLogos); ?></td>
                                <td>Control logo dimensions if present</td>
                            </tr>
                            <tr>
                                <td>Favicon Replacement</td>
                                <td>
                                    <span class="badge bg-success">Integrated</span>
                                </td>
                                <td>1</td>
                                <td>Replace favicon.png in verifying-permission</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🚀 Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h6>Replace Site Title</h6>
                                <p class="small text-muted">Change "Seabridge Credit Bank" to "Northland Credit Union"</p>
                                <a href="text-replacement.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-edit"></i> Text Replacement
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h6>Replace Favicon</h6>
                                <p class="small text-muted">Update favicon in all directories</p>
                                <a href="favicon-replacement.php" class="btn btn-success btn-sm">
                                    <i class="fas fa-star"></i> Favicon Replacement
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h6>Control Logo Size</h6>
                                <p class="small text-muted">Adjust logo dimensions if needed</p>
                                <a href="logo-dimension-controller.php" class="btn btn-warning btn-sm">
                                    <i class="fas fa-expand-arrows-alt"></i> Logo Dimensions
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php echo getPageFooter(); ?>
