<?php
/**
 * Configuration Settings Module
 * Manage reCAPTCHA keys, videos, and system configurations
 */

require_once 'includes/auth.php';
requireAuth();

class ConfigurationManager {
    
    private $target_files = [
        // Files that may contain reCAPTCHA
        '../../ps_access.php',
        '../../registration.php',
        '../../forget.php',
        '../../auth_2fa.php',
        '../../phone_verify.php',
        '../../reset-password.php',
        // Files that may contain video references
        '../../ps_access.php',
        '../../phone_verify.php',
        '../../auth_2fa.php',
        '../../reset-password.php',
        '../../registration.php'
    ];
    
    /**
     * Deep search for reCAPTCHA site keys across all files
     */
    public function detectRecaptchaKeys() {
        $found_keys = [];
        
        // Search in target files
        foreach ($this->target_files as $file) {
            if (file_exists($file) && is_readable($file)) {
                $content = file_get_contents($file);
                $keys = $this->extractRecaptchaKeys($content, $file);
                if (!empty($keys)) {
                    $found_keys[$file] = $keys;
                }
            }
        }
        
        // Deep search in all PHP files
        $additional_files = $this->findAllPhpFiles('../../');
        foreach ($additional_files as $file) {
            if (!in_array($file, $this->target_files) && is_readable($file)) {
                $content = file_get_contents($file);
                $keys = $this->extractRecaptchaKeys($content, $file);
                if (!empty($keys)) {
                    $found_keys[$file] = $keys;
                }
            }
        }
        
        return $found_keys;
    }
    
    /**
     * Extract reCAPTCHA keys from content
     */
    private function extractRecaptchaKeys($content, $file) {
        $keys = [];
        
        // Pattern for data-sitekey
        if (preg_match_all('/data-sitekey=["\']([^"\']+)["\']/', $content, $matches)) {
            foreach ($matches[1] as $key) {
                $keys[] = [
                    'type' => 'site_key',
                    'value' => $key,
                    'context' => 'data-sitekey attribute',
                    'file' => $file
                ];
            }
        }
        
        // Pattern for reCAPTCHA secret keys (in PHP)
        if (preg_match_all('/["\']secret["\'][\s]*=>[\s]*["\']([^"\']+)["\']/', $content, $matches)) {
            foreach ($matches[1] as $key) {
                $keys[] = [
                    'type' => 'secret_key',
                    'value' => $key,
                    'context' => 'secret key configuration',
                    'file' => $file
                ];
            }
        }
        
        // Pattern for reCAPTCHA API calls
        if (preg_match_all('/recaptcha.*?["\']([0-9A-Za-z_-]{40})["\']/', $content, $matches)) {
            foreach ($matches[1] as $key) {
                $keys[] = [
                    'type' => 'api_key',
                    'value' => $key,
                    'context' => 'API call',
                    'file' => $file
                ];
            }
        }
        
        return $keys;
    }
    
    /**
     * Deep search for video files
     */
    public function detectVideoFiles() {
        $found_videos = [];
        
        // Search in target files
        foreach ($this->target_files as $file) {
            if (file_exists($file) && is_readable($file)) {
                $content = file_get_contents($file);
                $videos = $this->extractVideoReferences($content, $file);
                if (!empty($videos)) {
                    $found_videos[$file] = $videos;
                }
            }
        }
        
        // Deep search in all files
        $additional_files = $this->findAllPhpFiles('../../');
        foreach ($additional_files as $file) {
            if (!in_array($file, $this->target_files) && is_readable($file)) {
                $content = file_get_contents($file);
                $videos = $this->extractVideoReferences($content, $file);
                if (!empty($videos)) {
                    $found_videos[$file] = $videos;
                }
            }
        }
        
        return $found_videos;
    }
    
    /**
     * Extract video file references from content
     */
    private function extractVideoReferences($content, $file) {
        $videos = [];
        
        // Pattern for video src attributes
        if (preg_match_all('/<source[^>]+src=["\']([^"\']*\.mp4[^"\']*)["\'][^>]*>/', $content, $matches)) {
            foreach ($matches[1] as $video_path) {
                $videos[] = [
                    'type' => 'video_source',
                    'path' => $video_path,
                    'context' => '<source> tag',
                    'file' => $file,
                    'exists' => file_exists('../../' . $video_path)
                ];
            }
        }
        
        // Pattern for video tags
        if (preg_match_all('/<video[^>]*>.*?<\/video>/s', $content, $matches)) {
            foreach ($matches[0] as $video_tag) {
                if (preg_match('/src=["\']([^"\']*\.mp4[^"\']*)["\']/', $video_tag, $src_match)) {
                    $videos[] = [
                        'type' => 'video_tag',
                        'path' => $src_match[1],
                        'context' => '<video> tag',
                        'file' => $file,
                        'exists' => file_exists('../../' . $src_match[1])
                    ];
                }
            }
        }
        
        // Pattern for any .mp4 references
        if (preg_match_all('/["\']([^"\']*\.mp4[^"\']*)["\']/', $content, $matches)) {
            foreach ($matches[1] as $video_path) {
                if (!in_array($video_path, array_column($videos, 'path'))) {
                    $videos[] = [
                        'type' => 'mp4_reference',
                        'path' => $video_path,
                        'context' => 'string reference',
                        'file' => $file,
                        'exists' => file_exists('../../' . $video_path)
                    ];
                }
            }
        }
        
        return $videos;
    }
    
    /**
     * Find all PHP files recursively
     */
    private function findAllPhpFiles($directory) {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }
        
        return $files;
    }
    
    /**
     * Replace reCAPTCHA site key
     */
    public function replaceRecaptchaKey($old_key, $new_key) {
        $results = [];
        $total_changes = 0;
        
        foreach ($this->target_files as $file) {
            if (file_exists($file) && is_readable($file) && is_writable($file)) {
                $content = file_get_contents($file);
                $original_content = $content;
                
                // Replace data-sitekey attributes
                $content = preg_replace(
                    '/data-sitekey=["\']' . preg_quote($old_key, '/') . '["\']/',
                    'data-sitekey="' . $new_key . '"',
                    $content,
                    -1,
                    $count
                );
                
                if ($count > 0) {
                    file_put_contents($file, $content);
                    $results[] = [
                        'file' => $file,
                        'changes' => $count,
                        'status' => 'success'
                    ];
                    $total_changes += $count;
                }
            }
        }
        
        return [
            'total_changes' => $total_changes,
            'files_modified' => $results
        ];
    }
    
    /**
     * Replace video file path
     */
    public function replaceVideoPath($old_path, $new_path) {
        $results = [];
        $total_changes = 0;
        
        foreach ($this->target_files as $file) {
            if (file_exists($file) && is_readable($file) && is_writable($file)) {
                $content = file_get_contents($file);
                $original_content = $content;
                
                // Replace video paths
                $content = str_replace($old_path, $new_path, $content, $count);
                
                if ($count > 0) {
                    file_put_contents($file, $content);
                    $results[] = [
                        'file' => $file,
                        'changes' => $count,
                        'status' => 'success'
                    ];
                    $total_changes += $count;
                }
            }
        }
        
        return [
            'total_changes' => $total_changes,
            'files_modified' => $results
        ];
    }
}

// Initialize configuration manager
$configManager = new ConfigurationManager();

// Handle form submissions
$message = '';
$messageType = '';

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'replace_recaptcha':
                $old_key = $_POST['old_recaptcha_key'] ?? '';
                $new_key = $_POST['new_recaptcha_key'] ?? '';
                
                if (empty($old_key) || empty($new_key)) {
                    $message = "❌ Both old and new reCAPTCHA keys are required.";
                    $messageType = 'danger';
                } else {
                    $results = $configManager->replaceRecaptchaKey($old_key, $new_key);
                    
                    if ($results['total_changes'] > 0) {
                        $message = "✅ Successfully replaced reCAPTCHA key in {$results['total_changes']} location(s) across " . count($results['files_modified']) . " file(s).";
                        $messageType = 'success';
                        
                        logSecurityEvent('reCAPTCHA key replaced', 'INFO', [
                            'old_key' => substr($old_key, 0, 10) . '...',
                            'new_key' => substr($new_key, 0, 10) . '...',
                            'changes' => $results['total_changes']
                        ]);
                    } else {
                        $message = "⚠️ No instances of the specified reCAPTCHA key were found.";
                        $messageType = 'warning';
                    }
                }
                break;
                
            case 'replace_video':
                $old_path = $_POST['old_video_path'] ?? '';
                $new_path = $_POST['new_video_path'] ?? '';
                
                if (empty($old_path) || empty($new_path)) {
                    $message = "❌ Both old and new video paths are required.";
                    $messageType = 'danger';
                } else {
                    $results = $configManager->replaceVideoPath($old_path, $new_path);
                    
                    if ($results['total_changes'] > 0) {
                        $message = "✅ Successfully replaced video path in {$results['total_changes']} location(s) across " . count($results['files_modified']) . " file(s).";
                        $messageType = 'success';
                        
                        logSecurityEvent('Video path replaced', 'INFO', [
                            'old_path' => $old_path,
                            'new_path' => $new_path,
                            'changes' => $results['total_changes']
                        ]);
                    } else {
                        $message = "⚠️ No instances of the specified video path were found.";
                        $messageType = 'warning';
                    }
                }
                break;
        }
    }
}

// Get current configurations
$recaptcha_keys = $configManager->detectRecaptchaKeys();
$video_files = $configManager->detectVideoFiles();

// Extract the most common site key for auto-population
$current_site_key = '';
$site_key_counts = [];

foreach ($recaptcha_keys as $file => $keys) {
    foreach ($keys as $key) {
        if ($key['type'] === 'site_key') {
            $site_key_counts[$key['value']] = ($site_key_counts[$key['value']] ?? 0) + 1;
        }
    }
}

// Get the most frequently used site key
if (!empty($site_key_counts)) {
    $current_site_key = array_keys($site_key_counts, max($site_key_counts))[0];
}

// Display the page
echo getPageHeader('Configuration Settings', 'Manage reCAPTCHA keys, videos, and system configurations');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>⚙️ Configuration Settings Manager</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Manage system configurations including reCAPTCHA keys and video files. This tool performs deep searches across all system files to detect and replace configuration values.</p>
            </div>
        </div>
    </div>
</div>

<!-- reCAPTCHA Management -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔐 reCAPTCHA Key Management</h5>
            </div>
            <div class="card-body">
                <!-- Current reCAPTCHA Keys -->
                <h6>Current reCAPTCHA Keys Detected:</h6>
                <?php if (empty($recaptcha_keys)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No reCAPTCHA keys detected in the system.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>File</th>
                                    <th>Type</th>
                                    <th>Key</th>
                                    <th>Context</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recaptcha_keys as $file => $keys): ?>
                                    <?php foreach ($keys as $key): ?>
                                        <tr <?php echo ($key['type'] === 'site_key' && $key['value'] === $current_site_key) ? 'class="table-success"' : ''; ?>>
                                            <td><code><?php echo htmlspecialchars(basename($file)); ?></code></td>
                                            <td>
                                                <span class="badge bg-<?php echo $key['type'] === 'site_key' ? 'primary' : ($key['type'] === 'secret_key' ? 'warning' : 'info'); ?>">
                                                    <?php echo htmlspecialchars($key['type']); ?>
                                                </span>
                                                <?php if ($key['type'] === 'site_key' && $key['value'] === $current_site_key): ?>
                                                    <span class="badge bg-success ms-1">CURRENT</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <code><?php echo htmlspecialchars($key['value']); ?></code>
                                                <?php if ($key['type'] === 'site_key' && isset($site_key_counts[$key['value']])): ?>
                                                    <small class="text-muted">(used <?php echo $site_key_counts[$key['value']]; ?> time<?php echo $site_key_counts[$key['value']] > 1 ? 's' : ''; ?>)</small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($key['context']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- reCAPTCHA Replacement Form -->
                <hr>
                <h6>Replace reCAPTCHA Site Key:</h6>
                <form method="POST" class="mt-3">
                    <input type="hidden" name="action" value="replace_recaptcha">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="old_recaptcha_key" class="form-label">Current reCAPTCHA Site Key</label>
                                <input type="text" class="form-control" id="old_recaptcha_key" name="old_recaptcha_key"
                                       value="<?php echo htmlspecialchars($current_site_key); ?>"
                                       placeholder="<?php echo !empty($current_site_key) ? 'Auto-detected: ' . htmlspecialchars($current_site_key) : '6LdIvD4qAAAAAB7v7h_fQKtpV6PQRr9KiIVF_TqL'; ?>"
                                       <?php echo !empty($current_site_key) ? 'readonly' : 'required'; ?>>
                                <div class="form-text">
                                    <?php if (!empty($current_site_key)): ?>
                                        ✅ Auto-detected from system files (<?php echo count($site_key_counts); ?> unique key<?php echo count($site_key_counts) > 1 ? 's' : ''; ?> found)
                                        <?php if (count($site_key_counts) > 1): ?>
                                            <br><small class="text-warning">⚠️ Multiple site keys detected. Using most common one.</small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        ⚠️ No site key detected. Enter the current reCAPTCHA site key to replace
                                    <?php endif; ?>
                                </div>
                                <?php if (!empty($current_site_key)): ?>
                                    <button type="button" class="btn btn-outline-secondary btn-sm mt-2"
                                            onclick="document.getElementById('old_recaptcha_key').readOnly = false; document.getElementById('old_recaptcha_key').focus(); this.style.display='none';">
                                        ✏️ Edit Manually
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_recaptcha_key" class="form-label">New reCAPTCHA Site Key</label>
                                <input type="text" class="form-control" id="new_recaptcha_key" name="new_recaptcha_key"
                                       placeholder="6LcXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX" required>
                                <div class="form-text">Enter the new reCAPTCHA site key</div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-key"></i> Replace reCAPTCHA Key
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>



<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<?php echo getPageFooter(); ?>

<?php include 'includes/footer.php'; ?>
