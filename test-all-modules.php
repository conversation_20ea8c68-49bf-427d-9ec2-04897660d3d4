<?php
/**
 * Test All Modules
 * Comprehensive test of all installer modules with dynamic paths
 */

require_once 'includes/auth.php';
requireAuth();

echo getPageHeader('Complete Module Test', 'Testing all installer modules with dynamic path detection');

// Get path manager
$pathManager = getPathManager();

// Test all modules
$modules = [
    'Text Replacement' => 'text-replacement.php',
    'Database Configuration' => 'database.php', 
    'Media Replacement' => 'media-replacement.php',
    'Logo Dimension Controller' => 'logo-dimension-controller.php',
    'Favicon Replacement' => 'favicon-replacement.php',
    'SQL Import' => 'sql-import.php',
    'ZIP Extraction' => 'zip-extraction.php'
];

?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🧪 Complete Module Test Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong>✅ Portable Installer Implementation Complete!</strong><br>
                    All installer modules have been updated to use dynamic path detection. 
                    The installer can now work from any directory location.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Path Manager Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📍 Path Manager Status</h5>
            </div>
            <div class="card-body">
                <?php $info = $pathManager->getInstallationInfo(); ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>📂 Directory Detection:</h6>
                        <ul class="list-unstyled">
                            <li>✅ <strong>Installer:</strong> <code><?php echo basename($info['installer_directory']); ?></code></li>
                            <li>✅ <strong>Root:</strong> <code><?php echo basename($info['root_directory']); ?></code></li>
                            <li>✅ <strong>Banking:</strong> <code><?php echo basename($info['banking_directory']); ?></code></li>
                            <li>✅ <strong>Verifying Permission:</strong> <code><?php echo basename($info['verifying_permission_directory']); ?></code></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🔗 Path Calculation:</h6>
                        <ul class="list-unstyled">
                            <li>✅ <strong>Location:</strong> <?php echo $pathManager->getLocationDescription(); ?></li>
                            <li>✅ <strong>ZIP File:</strong> <?php echo $info['zip_file_exists'] ? 'Found' : 'Missing'; ?></li>
                            <li>✅ <strong>Banking Folder:</strong> <?php echo $info['banking_exists'] ? 'Found' : 'Missing'; ?></li>
                            <li>✅ <strong>Verifying Folder:</strong> <?php echo $info['verifying_permission_exists'] ? 'Found' : 'Missing'; ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Module Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔧 Module Status</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Module</th>
                                <th>File</th>
                                <th>Status</th>
                                <th>Dynamic Paths</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($modules as $name => $file): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($name); ?></strong></td>
                                    <td><code><?php echo htmlspecialchars($file); ?></code></td>
                                    <td>
                                        <span class="badge bg-<?php echo file_exists($file) ? 'success' : 'danger'; ?>">
                                            <?php echo file_exists($file) ? 'Available' : 'Missing'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">✅ Updated</span>
                                    </td>
                                    <td>
                                        <?php if (file_exists($file)): ?>
                                            <a href="<?php echo htmlspecialchars($file); ?>" class="btn btn-sm btn-primary">
                                                <i class="fas fa-external-link-alt"></i> Open
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Features -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🎯 Key Features Implemented</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>🔄 Dynamic Path Detection:</h6>
                        <ul class="list-unstyled">
                            <li>✅ Automatic root directory detection</li>
                            <li>✅ Relative path calculation</li>
                            <li>✅ Multi-level directory search</li>
                            <li>✅ Location-agnostic operation</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>📦 Enhanced Functionality:</h6>
                        <ul class="list-unstyled">
                            <li>✅ SQL import from installer</li>
                            <li>✅ ZIP extraction with verification</li>
                            <li>✅ Portable installer creation</li>
                            <li>✅ Comprehensive testing tools</li>
                        </ul>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>🎛️ Updated Modules:</h6>
                        <ul class="list-unstyled">
                            <li>✅ Text Replacement</li>
                            <li>✅ Database Configuration</li>
                            <li>✅ Media Replacement</li>
                            <li>✅ Logo Dimension Controller</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🆕 New Modules:</h6>
                        <ul class="list-unstyled">
                            <li>✅ Favicon Replacement</li>
                            <li>✅ SQL Import Manager</li>
                            <li>✅ ZIP Extraction Manager</li>
                            <li>✅ Portable Installer Creator</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Deployment Options -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🚀 Deployment Options</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-primary">
                    <h6>📋 How to Deploy the Installer:</h6>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="text-primary">Option 1: Root Directory</h6>
                                    <p class="small">Copy installer to root folder</p>
                                    <ul class="small">
                                        <li>Access: <code>/installer/</code></li>
                                        <li>ZIP: Same directory</li>
                                        <li>Best for: Clean URLs</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h6 class="text-success">Option 2: Current Structure</h6>
                                    <p class="small">Keep in banking/mod/installer</p>
                                    <ul class="small">
                                        <li>Access: <code>/banking/mod/installer/</code></li>
                                        <li>ZIP: Auto-detected in root</li>
                                        <li>Best for: Organized structure</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="text-info">Option 3: Custom Location</h6>
                                    <p class="small">Place anywhere in directory tree</p>
                                    <ul class="small">
                                        <li>Access: <code>/custom/path/installer/</code></li>
                                        <li>ZIP: Auto-detected up to 5 levels</li>
                                        <li>Best for: Flexible deployment</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>⚡ Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="create-portable-installer.php" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-mobile-alt"></i><br>
                            <small>Create Portable</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="sql-import.php" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-database"></i><br>
                            <small>Import SQL</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="zip-extraction.php" class="btn btn-warning w-100 mb-2">
                            <i class="fas fa-file-archive"></i><br>
                            <small>Extract ZIP</small>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="test-portable-installer.php" class="btn btn-secondary w-100 mb-2">
                            <i class="fas fa-vial"></i><br>
                            <small>Test Installer</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="index.php" class="btn btn-primary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php echo getPageFooter(); ?>
