<?php
/**
 * Create Portable Installer
 * Helper script to copy installer to root directory for portable deployment
 */

require_once 'includes/auth.php';
requireAuth();

class PortableInstallerCreator {
    
    private $path_manager;
    private $installer_dir;
    private $root_dir;
    
    public function __construct() {
        $this->path_manager = getPathManager();
        $this->installer_dir = $this->path_manager->getInstallerDir();
        $this->root_dir = $this->path_manager->getRootDir();
    }
    
    /**
     * Check if portable installer can be created
     */
    public function canCreatePortable() {
        $root_installer_path = $this->root_dir . '/installer';
        
        return [
            'installer_exists' => is_dir($this->installer_dir),
            'root_writable' => is_writable($this->root_dir),
            'target_exists' => is_dir($root_installer_path),
            'target_path' => $root_installer_path,
            'source_path' => $this->installer_dir,
            'is_already_portable' => $this->path_manager->isInstallerInRoot()
        ];
    }
    
    /**
     * Create portable installer by copying to root
     */
    public function createPortable($overwrite = false) {
        $check = $this->canCreatePortable();
        
        if (!$check['installer_exists']) {
            return [
                'success' => false,
                'message' => 'Source installer directory not found'
            ];
        }
        
        if (!$check['root_writable']) {
            return [
                'success' => false,
                'message' => 'Root directory is not writable'
            ];
        }
        
        if ($check['is_already_portable']) {
            return [
                'success' => false,
                'message' => 'Installer is already in root directory'
            ];
        }
        
        if ($check['target_exists'] && !$overwrite) {
            return [
                'success' => false,
                'message' => 'Target installer directory already exists. Use overwrite option to replace it.',
                'target_exists' => true
            ];
        }
        
        try {
            // Create backup if target exists
            $backup_path = null;
            if ($check['target_exists']) {
                $backup_path = $check['target_path'] . '.backup.' . date('Y-m-d-H-i-s');
                if (!rename($check['target_path'], $backup_path)) {
                    return [
                        'success' => false,
                        'message' => 'Failed to create backup of existing installer'
                    ];
                }
            }
            
            // Copy installer directory
            $copied = $this->copyDirectory($check['source_path'], $check['target_path']);
            
            if (!$copied) {
                // Restore backup if copy failed
                if ($backup_path && is_dir($backup_path)) {
                    rename($backup_path, $check['target_path']);
                }
                
                return [
                    'success' => false,
                    'message' => 'Failed to copy installer directory'
                ];
            }
            
            return [
                'success' => true,
                'message' => 'Portable installer created successfully!',
                'target_path' => $check['target_path'],
                'backup_created' => $backup_path,
                'files_copied' => $this->countFiles($check['target_path'])
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error creating portable installer: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Recursively copy directory
     */
    private function copyDirectory($source, $destination) {
        if (!is_dir($source)) {
            return false;
        }
        
        if (!mkdir($destination, 0755, true)) {
            return false;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $target_path = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                if (!mkdir($target_path, 0755, true)) {
                    return false;
                }
            } else {
                if (!copy($item, $target_path)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Count files in directory
     */
    private function countFiles($dir) {
        $count = 0;
        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $count++;
                }
            }
        } catch (Exception $e) {
            // Ignore errors
        }
        
        return $count;
    }
}

// Initialize creator
$creator = new PortableInstallerCreator();

// Handle form submissions
$message = '';
$messageType = '';
$createResults = [];

if ($_POST) {
    if (isset($_POST['action']) && $_POST['action'] === 'create_portable') {
        $overwrite = isset($_POST['overwrite']) && $_POST['overwrite'] === '1';
        $createResults = $creator->createPortable($overwrite);
        
        if ($createResults['success']) {
            $message = "✅ " . $createResults['message'];
            $messageType = 'success';
            
            logSecurityEvent('Portable installer created', 'INFO', [
                'target_path' => $createResults['target_path'],
                'files_copied' => $createResults['files_copied'],
                'backup_created' => !empty($createResults['backup_created'])
            ]);
        } else {
            $message = "❌ " . $createResults['message'];
            $messageType = 'danger';
        }
    }
}

$canCreate = $creator->canCreatePortable();

echo getPageHeader('Create Portable Installer', 'Copy installer to root directory for portable deployment');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📦 Create Portable Installer</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>ℹ️ About Portable Installer:</strong><br>
                    This tool copies the installer to your root directory, making it accessible as 
                    <code>yoursite.com/installer/</code> instead of <code>yoursite.com/banking/mod/installer/</code>.
                    This is useful for easier access and cleaner URLs.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Current Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📊 Current Status</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <tr>
                            <td><strong>Source Installer:</strong></td>
                            <td><code><?php echo htmlspecialchars($canCreate['source_path']); ?></code></td>
                            <td>
                                <span class="badge bg-<?php echo $canCreate['installer_exists'] ? 'success' : 'danger'; ?>">
                                    <?php echo $canCreate['installer_exists'] ? 'Found' : 'Missing'; ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Target Location:</strong></td>
                            <td><code><?php echo htmlspecialchars($canCreate['target_path']); ?></code></td>
                            <td>
                                <span class="badge bg-<?php echo $canCreate['target_exists'] ? 'warning' : 'secondary'; ?>">
                                    <?php echo $canCreate['target_exists'] ? 'Already Exists' : 'Available'; ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Root Directory Writable:</strong></td>
                            <td>Write permissions required</td>
                            <td>
                                <span class="badge bg-<?php echo $canCreate['root_writable'] ? 'success' : 'danger'; ?>">
                                    <?php echo $canCreate['root_writable'] ? 'Yes' : 'No'; ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Already Portable:</strong></td>
                            <td>Installer location status</td>
                            <td>
                                <span class="badge bg-<?php echo $canCreate['is_already_portable'] ? 'success' : 'info'; ?>">
                                    <?php echo $canCreate['is_already_portable'] ? 'Yes' : 'No'; ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Portable Form -->
<?php if ($canCreate['installer_exists'] && $canCreate['root_writable'] && !$canCreate['is_already_portable']): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🚀 Create Portable Installer</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="create_portable">
                    
                    <?php if ($canCreate['target_exists']): ?>
                        <div class="alert alert-warning">
                            <strong>⚠️ Target Directory Exists</strong><br>
                            The installer directory already exists in the root. Check the option below to overwrite it.
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="overwrite" name="overwrite" value="1">
                                <label class="form-check-label" for="overwrite">
                                    <strong>Overwrite existing installer</strong><br>
                                    <small class="text-muted">The existing installer will be backed up before replacement</small>
                                </label>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <button type="submit" class="btn btn-primary" 
                            onclick="return confirm('This will copy the installer to the root directory. Continue?')">
                        <i class="fas fa-copy"></i> Create Portable Installer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>⚠️ Cannot Create Portable Installer</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <strong>Issues preventing portable installer creation:</strong>
                    <ul class="mb-0">
                        <?php if (!$canCreate['installer_exists']): ?>
                            <li>Source installer directory not found</li>
                        <?php endif; ?>
                        <?php if (!$canCreate['root_writable']): ?>
                            <li>Root directory is not writable</li>
                        <?php endif; ?>
                        <?php if ($canCreate['is_already_portable']): ?>
                            <li>Installer is already in root directory (already portable)</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Creation Results -->
<?php if (!empty($createResults) && $createResults['success']): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>✅ Portable Installer Created</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong><?php echo htmlspecialchars($createResults['message']); ?></strong>
                </div>
                
                <div class="table-responsive">
                    <table class="table">
                        <tr>
                            <td><strong>Target Path:</strong></td>
                            <td><code><?php echo htmlspecialchars($createResults['target_path']); ?></code></td>
                        </tr>
                        <tr>
                            <td><strong>Files Copied:</strong></td>
                            <td><?php echo $createResults['files_copied']; ?> files</td>
                        </tr>
                        <?php if (!empty($createResults['backup_created'])): ?>
                        <tr>
                            <td><strong>Backup Created:</strong></td>
                            <td><code><?php echo htmlspecialchars(basename($createResults['backup_created'])); ?></code></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
                
                <div class="alert alert-info">
                    <strong>🎉 Success!</strong><br>
                    You can now access the installer at: <code>yoursite.com/installer/</code><br>
                    The installer will automatically detect that it's in the root directory and adjust all paths accordingly.
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="test-portable-installer.php" class="btn btn-info">
            <i class="fas fa-test-tube"></i> Test Portable Installer
        </a>
        <a href="index.php" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php echo getPageFooter(); ?>
