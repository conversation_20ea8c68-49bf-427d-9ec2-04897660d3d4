<?php
/**
 * ZIP Extraction Module
 * Extract banking.zip to root folder with permission verification
 */

require_once 'includes/auth.php';
requireAuth();

class ZipExtractor {

    private $zip_file;
    private $extract_path;
    private $expected_folders = ['banking', 'verifying-permission'];
    private $path_manager;

    public function __construct() {
        $this->path_manager = getPathManager();
        $this->zip_file = $this->path_manager->getZipFilePath();
        $this->extract_path = $this->path_manager->getZipExtractionPath();
    }
    
    /**
     * Check if ZIP file exists
     */
    public function checkZipFile() {
        return [
            'exists' => file_exists($this->zip_file),
            'readable' => file_exists($this->zip_file) && is_readable($this->zip_file),
            'size' => file_exists($this->zip_file) ? filesize($this->zip_file) : 0,
            'path' => $this->zip_file,
            'modified' => file_exists($this->zip_file) ? date('Y-m-d H:i:s', filemtime($this->zip_file)) : 'N/A'
        ];
    }
    
    /**
     * Check current folder structure
     */
    public function checkCurrentStructure() {
        $structure = [];
        
        foreach ($this->expected_folders as $folder) {
            $folder_path = $this->extract_path . $folder;
            $structure[$folder] = [
                'exists' => is_dir($folder_path),
                'writable' => is_dir($folder_path) && is_writable($folder_path),
                'path' => $folder_path,
                'file_count' => is_dir($folder_path) ? $this->countFiles($folder_path) : 0
            ];
        }
        
        return $structure;
    }
    
    /**
     * Count files in directory recursively
     */
    private function countFiles($dir) {
        $count = 0;
        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $count++;
                }
            }
        } catch (Exception $e) {
            // Directory not accessible
        }
        
        return $count;
    }
    
    /**
     * Check extraction permissions
     */
    public function checkPermissions() {
        $permissions = [
            'extract_path_writable' => is_writable($this->extract_path),
            'extract_path_readable' => is_readable($this->extract_path),
            'zip_extension_loaded' => extension_loaded('zip'),
            'php_version_ok' => version_compare(PHP_VERSION, '5.2.0', '>=')
        ];
        
        $permissions['all_ok'] = array_reduce($permissions, function($carry, $item) {
            return $carry && $item;
        }, true);
        
        return $permissions;
    }
    
    /**
     * Preview ZIP contents
     */
    public function previewZipContents() {
        if (!file_exists($this->zip_file)) {
            return [
                'success' => false,
                'message' => 'ZIP file not found'
            ];
        }
        
        if (!extension_loaded('zip')) {
            return [
                'success' => false,
                'message' => 'ZIP extension not loaded'
            ];
        }
        
        $zip = new ZipArchive();
        $result = $zip->open($this->zip_file);
        
        if ($result !== TRUE) {
            return [
                'success' => false,
                'message' => 'Failed to open ZIP file: ' . $this->getZipError($result)
            ];
        }
        
        $contents = [];
        for ($i = 0; $i < $zip->numFiles; $i++) {
            $stat = $zip->statIndex($i);
            $contents[] = [
                'name' => $stat['name'],
                'size' => $stat['size'],
                'compressed_size' => $stat['comp_size'],
                'is_dir' => substr($stat['name'], -1) === '/',
                'modified' => date('Y-m-d H:i:s', $stat['mtime'])
            ];
        }
        
        $zip->close();
        
        return [
            'success' => true,
            'total_files' => count($contents),
            'contents' => $contents
        ];
    }
    
    /**
     * Extract ZIP file
     */
    public function extractZip($overwrite = false) {
        // Check prerequisites
        $permissions = $this->checkPermissions();
        if (!$permissions['all_ok']) {
            return [
                'success' => false,
                'message' => 'Permission check failed. Please check system requirements.',
                'permissions' => $permissions
            ];
        }
        
        if (!file_exists($this->zip_file)) {
            return [
                'success' => false,
                'message' => 'ZIP file not found: ' . $this->zip_file
            ];
        }
        
        // Check if folders already exist
        if (!$overwrite) {
            $structure = $this->checkCurrentStructure();
            $existing_folders = array_filter($structure, function($folder) {
                return $folder['exists'];
            });
            
            if (!empty($existing_folders)) {
                return [
                    'success' => false,
                    'message' => 'Target folders already exist. Use overwrite option to replace them.',
                    'existing_folders' => array_keys($existing_folders)
                ];
            }
        }
        
        // Create backup if overwriting
        $backups = [];
        if ($overwrite) {
            $backups = $this->createBackups();
        }
        
        // Extract ZIP
        $zip = new ZipArchive();
        $result = $zip->open($this->zip_file);
        
        if ($result !== TRUE) {
            return [
                'success' => false,
                'message' => 'Failed to open ZIP file: ' . $this->getZipError($result)
            ];
        }
        
        $extracted = $zip->extractTo($this->extract_path);
        $zip->close();
        
        if (!$extracted) {
            return [
                'success' => false,
                'message' => 'Failed to extract ZIP file'
            ];
        }
        
        // Verify extraction
        $verification = $this->verifyExtraction();
        
        return [
            'success' => true,
            'message' => 'ZIP file extracted successfully!',
            'backups_created' => $backups,
            'verification' => $verification
        ];
    }
    
    /**
     * Create backups of existing folders
     */
    private function createBackups() {
        $backups = [];
        $timestamp = date('Y-m-d-H-i-s');
        
        foreach ($this->expected_folders as $folder) {
            $folder_path = $this->extract_path . $folder;
            if (is_dir($folder_path)) {
                $backup_path = $folder_path . '.backup.' . $timestamp;
                if (rename($folder_path, $backup_path)) {
                    $backups[] = $backup_path;
                }
            }
        }
        
        return $backups;
    }
    
    /**
     * Verify extraction results
     */
    private function verifyExtraction() {
        $structure = $this->checkCurrentStructure();
        $verification = [
            'folders_created' => 0,
            'total_files' => 0,
            'issues' => []
        ];
        
        foreach ($structure as $folder_name => $folder_info) {
            if ($folder_info['exists']) {
                $verification['folders_created']++;
                $verification['total_files'] += $folder_info['file_count'];
            } else {
                $verification['issues'][] = "Folder '{$folder_name}' was not created";
            }
            
            if (!$folder_info['writable']) {
                $verification['issues'][] = "Folder '{$folder_name}' is not writable";
            }
        }
        
        return $verification;
    }
    
    /**
     * Get ZIP error message
     */
    private function getZipError($code) {
        $errors = [
            ZipArchive::ER_OK => 'No error',
            ZipArchive::ER_MULTIDISK => 'Multi-disk zip archives not supported',
            ZipArchive::ER_RENAME => 'Renaming temporary file failed',
            ZipArchive::ER_CLOSE => 'Closing zip archive failed',
            ZipArchive::ER_SEEK => 'Seek error',
            ZipArchive::ER_READ => 'Read error',
            ZipArchive::ER_WRITE => 'Write error',
            ZipArchive::ER_CRC => 'CRC error',
            ZipArchive::ER_ZIPCLOSED => 'Containing zip archive was closed',
            ZipArchive::ER_NOENT => 'No such file',
            ZipArchive::ER_EXISTS => 'File already exists',
            ZipArchive::ER_OPEN => 'Can\'t open file',
            ZipArchive::ER_TMPOPEN => 'Failure to create temporary file',
            ZipArchive::ER_ZLIB => 'Zlib error',
            ZipArchive::ER_MEMORY => 'Memory allocation failure',
            ZipArchive::ER_CHANGED => 'Entry has been changed',
            ZipArchive::ER_COMPNOTSUPP => 'Compression method not supported',
            ZipArchive::ER_EOF => 'Premature EOF',
            ZipArchive::ER_INVAL => 'Invalid argument',
            ZipArchive::ER_NOZIP => 'Not a zip archive',
            ZipArchive::ER_INTERNAL => 'Internal error',
            ZipArchive::ER_INCONS => 'Zip archive inconsistent',
            ZipArchive::ER_REMOVE => 'Can\'t remove file',
            ZipArchive::ER_DELETED => 'Entry has been deleted'
        ];
        
        return isset($errors[$code]) ? $errors[$code] : 'Unknown error';
    }
}

// Initialize extractor
$zipExtractor = new ZipExtractor();

// Handle form submissions
$message = '';
$messageType = '';
$extractResults = [];
$zipPreview = [];

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'preview_zip':
                $zipPreview = $zipExtractor->previewZipContents();
                if ($zipPreview['success']) {
                    $message = "✅ ZIP preview loaded successfully. Found {$zipPreview['total_files']} files.";
                    $messageType = 'success';
                } else {
                    $message = "❌ " . $zipPreview['message'];
                    $messageType = 'danger';
                }
                break;
                
            case 'extract_zip':
                $overwrite = isset($_POST['overwrite']) && $_POST['overwrite'] === '1';
                $extractResults = $zipExtractor->extractZip($overwrite);
                
                if ($extractResults['success']) {
                    $message = "✅ " . $extractResults['message'];
                    $messageType = 'success';
                    
                    logSecurityEvent('ZIP file extracted via installer', 'INFO', [
                        'overwrite' => $overwrite,
                        'backups_created' => count($extractResults['backups_created'] ?? []),
                        'folders_created' => $extractResults['verification']['folders_created'] ?? 0
                    ]);
                } else {
                    $message = "❌ " . $extractResults['message'];
                    $messageType = 'danger';
                }
                break;
        }
    }
}

$zipInfo = $zipExtractor->checkZipFile();
$currentStructure = $zipExtractor->checkCurrentStructure();
$permissions = $zipExtractor->checkPermissions();

echo getPageHeader('ZIP Extraction Manager', 'Extract banking.zip to root folder with permission verification');
echo displayMessage($message, $messageType);
?>

<style>
/* Custom styles for better table layout */
.table-responsive {
    overflow-x: hidden !important;
}

.table th, .table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table th:first-child, .table td:first-child {
    white-space: normal;
    word-break: break-all;
    max-width: 200px;
}

/* Smooth scrolling for containers */
.scroll-container {
    scrollbar-width: thin;
    scrollbar-color: #ccc transparent;
}

.scroll-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.scroll-container::-webkit-scrollbar-track {
    background: transparent;
}

.scroll-container::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 3px;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
    background-color: #999;
}

/* Better code display */
code {
    font-size: 0.85rem;
    word-break: break-all;
    white-space: pre-wrap;
}

/* Compact badges */
.badge {
    font-size: 0.7rem;
    padding: 0.25em 0.5em;
}
</style>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📦 ZIP Extraction Manager</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>ℹ️ About ZIP Extraction:</strong><br>
                    This tool extracts banking.zip to the root folder, creating the banking and verifying-permission directories.
                    It includes permission verification and backup creation for safety.
                </div>

                <div class="alert alert-warning">
                    <strong>⚠️ Important:</strong><br>
                    • Existing folders will be backed up before extraction<br>
                    • Ensure sufficient disk space is available<br>
                    • Check permissions before proceeding
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Requirements -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔧 System Requirements</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Requirement</th>
                                <th>Status</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>ZIP Extension</td>
                                <td>
                                    <span class="badge bg-<?php echo $permissions['zip_extension_loaded'] ? 'success' : 'danger'; ?>">
                                        <?php echo $permissions['zip_extension_loaded'] ? 'Available' : 'Missing'; ?>
                                    </span>
                                </td>
                                <td><?php echo $permissions['zip_extension_loaded'] ? 'ZIP functions are available' : 'PHP ZIP extension is required'; ?></td>
                            </tr>
                            <tr>
                                <td>PHP Version</td>
                                <td>
                                    <span class="badge bg-<?php echo $permissions['php_version_ok'] ? 'success' : 'danger'; ?>">
                                        <?php echo $permissions['php_version_ok'] ? 'Compatible' : 'Incompatible'; ?>
                                    </span>
                                </td>
                                <td>Current: <?php echo PHP_VERSION; ?> (Required: 5.2.0+)</td>
                            </tr>
                            <tr>
                                <td>Extract Path Writable</td>
                                <td>
                                    <span class="badge bg-<?php echo $permissions['extract_path_writable'] ? 'success' : 'danger'; ?>">
                                        <?php echo $permissions['extract_path_writable'] ? 'Writable' : 'Not Writable'; ?>
                                    </span>
                                </td>
                                <td>Root directory write permissions</td>
                            </tr>
                            <tr>
                                <td>Extract Path Readable</td>
                                <td>
                                    <span class="badge bg-<?php echo $permissions['extract_path_readable'] ? 'success' : 'danger'; ?>">
                                        <?php echo $permissions['extract_path_readable'] ? 'Readable' : 'Not Readable'; ?>
                                    </span>
                                </td>
                                <td>Root directory read permissions</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <?php if (!$permissions['all_ok']): ?>
                    <div class="alert alert-danger">
                        <strong>❌ System requirements not met!</strong><br>
                        Please resolve the issues above before proceeding with extraction.
                    </div>
                <?php else: ?>
                    <div class="alert alert-success">
                        <strong>✅ All system requirements met!</strong><br>
                        You can proceed with ZIP extraction.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- ZIP File Information -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>📁 ZIP File Information</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>File Path:</strong></td>
                            <td>
                                <code style="word-break: break-all; white-space: pre-wrap;">
                                    <?php echo htmlspecialchars($zipInfo['path']); ?>
                                </code>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Exists:</strong></td>
                            <td>
                                <span class="badge bg-<?php echo $zipInfo['exists'] ? 'success' : 'danger'; ?>">
                                    <?php echo $zipInfo['exists'] ? 'Yes' : 'No'; ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Readable:</strong></td>
                            <td>
                                <span class="badge bg-<?php echo $zipInfo['readable'] ? 'success' : 'danger'; ?>">
                                    <?php echo $zipInfo['readable'] ? 'Yes' : 'No'; ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Size:</strong></td>
                            <td><?php echo number_format($zipInfo['size']); ?> bytes</td>
                        </tr>
                        <tr>
                            <td><strong>Modified:</strong></td>
                            <td><?php echo $zipInfo['modified']; ?></td>
                        </tr>
                    </table>
                </div>

                <?php if ($zipInfo['exists'] && $zipInfo['readable']): ?>
                    <form method="post">
                        <input type="hidden" name="action" value="preview_zip">
                        <button type="submit" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> Preview ZIP Contents
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>📂 Current Folder Structure</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Folder</th>
                                <th>Status</th>
                                <th>Files</th>
                                <th>Writable</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($currentStructure as $folder_name => $folder_info): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($folder_name); ?></code></td>
                                    <td>
                                        <span class="badge bg-<?php echo $folder_info['exists'] ? 'success' : 'secondary'; ?>">
                                            <?php echo $folder_info['exists'] ? 'Exists' : 'Missing'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo $folder_info['file_count']; ?> files</td>
                                    <td>
                                        <span class="badge bg-<?php echo $folder_info['writable'] ? 'success' : 'warning'; ?>">
                                            <?php echo $folder_info['writable'] ? 'Yes' : 'No'; ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ZIP Preview Results -->
<?php if (!empty($zipPreview) && $zipPreview['success']): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>👁️ ZIP Contents Preview</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>Total Files:</strong> <?php echo $zipPreview['total_files']; ?>
                </div>

                <div class="scroll-container" style="max-height: 400px; overflow-y: auto; overflow-x: hidden;">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th style="width: 40%;">Name</th>
                                <th style="width: 15%;">Type</th>
                                <th style="width: 15%;">Size</th>
                                <th style="width: 15%;">Compressed</th>
                                <th style="width: 15%;">Modified</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($zipPreview['contents'] as $item): ?>
                                <tr>
                                    <td style="word-break: break-all; max-width: 0;">
                                        <code style="font-size: 0.8rem;"><?php echo htmlspecialchars($item['name']); ?></code>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $item['is_dir'] ? 'primary' : 'secondary'; ?>" style="font-size: 0.7rem;">
                                            <?php echo $item['is_dir'] ? 'Dir' : 'File'; ?>
                                        </span>
                                    </td>
                                    <td style="font-size: 0.8rem;"><?php echo number_format($item['size']); ?></td>
                                    <td style="font-size: 0.8rem;"><?php echo number_format($item['compressed_size']); ?></td>
                                    <td style="font-size: 0.8rem;"><?php echo date('m/d H:i', strtotime($item['modified'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Extraction Controls -->
<?php if ($zipInfo['exists'] && $zipInfo['readable'] && $permissions['all_ok']): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🚀 Extract ZIP File</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="action" value="extract_zip">

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="overwrite" name="overwrite" value="1">
                            <label class="form-check-label" for="overwrite">
                                <strong>Overwrite existing folders</strong><br>
                                <small class="text-muted">If checked, existing folders will be backed up and replaced</small>
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary"
                            onclick="return confirm('Are you sure you want to extract the ZIP file? This will create/replace the banking and verifying-permission folders.')">
                        <i class="fas fa-download"></i> Extract ZIP File
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Extraction Results -->
<?php if (!empty($extractResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><?php echo $extractResults['success'] ? '✅' : '❌'; ?> Extraction Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-<?php echo $extractResults['success'] ? 'success' : 'danger'; ?>">
                    <strong><?php echo htmlspecialchars($extractResults['message']); ?></strong>
                </div>

                <?php if ($extractResults['success']): ?>
                    <!-- Verification Results -->
                    <?php if (isset($extractResults['verification'])): ?>
                        <h6>📊 Verification Results:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <h5 class="text-success"><?php echo $extractResults['verification']['folders_created']; ?></h5>
                                        <small>Folders Created</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <h5 class="text-info"><?php echo $extractResults['verification']['total_files']; ?></h5>
                                        <small>Total Files</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-<?php echo empty($extractResults['verification']['issues']) ? 'success' : 'warning'; ?>">
                                    <div class="card-body text-center">
                                        <h5 class="text-<?php echo empty($extractResults['verification']['issues']) ? 'success' : 'warning'; ?>">
                                            <?php echo count($extractResults['verification']['issues']); ?>
                                        </h5>
                                        <small>Issues</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if (!empty($extractResults['verification']['issues'])): ?>
                            <div class="alert alert-warning mt-3">
                                <h6>⚠️ Issues Found:</h6>
                                <ul class="mb-0">
                                    <?php foreach ($extractResults['verification']['issues'] as $issue): ?>
                                        <li><?php echo htmlspecialchars($issue); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Backup Information -->
                    <?php if (!empty($extractResults['backups_created'])): ?>
                        <div class="alert alert-info mt-3">
                            <h6>💾 Backups Created:</h6>
                            <ul class="mb-0">
                                <?php foreach ($extractResults['backups_created'] as $backup): ?>
                                    <li><code><?php echo htmlspecialchars($backup); ?></code></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                <?php else: ?>
                    <!-- Error Information -->
                    <?php if (isset($extractResults['existing_folders'])): ?>
                        <div class="alert alert-warning">
                            <h6>📁 Existing Folders Found:</h6>
                            <ul class="mb-0">
                                <?php foreach ($extractResults['existing_folders'] as $folder): ?>
                                    <li><code><?php echo htmlspecialchars($folder); ?></code></li>
                                <?php endforeach; ?>
                            </ul>
                            <p class="mt-2 mb-0">Use the "Overwrite existing folders" option to replace them.</p>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($extractResults['permissions'])): ?>
                        <div class="alert alert-danger">
                            <h6>🔒 Permission Issues:</h6>
                            <ul class="mb-0">
                                <?php foreach ($extractResults['permissions'] as $perm => $status): ?>
                                    <?php if (!$status): ?>
                                        <li><?php echo ucfirst(str_replace('_', ' ', $perm)); ?>: Failed</li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php echo getPageFooter(); ?>
