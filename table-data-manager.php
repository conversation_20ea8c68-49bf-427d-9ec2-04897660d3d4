<?php
/**
 * Table Data Manager
 * Selective table data management - clear, backup, and restore specific tables
 */

require_once 'includes/auth.php';
requireAuth();

class TableDataManager {
    
    private $db_connection;
    private $path_manager;
    
    // Tables that can be managed
    private $manageable_tables = [
        'alerts' => [
            'name' => 'Alerts',
            'description' => 'User transaction alerts and notifications',
            'primary_key' => 'id',
            'sample_columns' => ['cs_uname', 'amount', 'sender_name', 'type', 'date']
        ],
        'temp_transfer' => [
            'name' => 'Temporary Transfers', 
            'description' => 'Pending transfer requests and drafts',
            'primary_key' => 'id',
            'sample_columns' => ['email', 'amount', 'acc_name', 'bk_name', 'date']
        ],
        'transfer' => [
            'name' => 'Completed Transfers',
            'description' => 'Completed transfer transactions',
            'primary_key' => 'id', 
            'sample_columns' => ['email', 'amount', 'acc_name', 'bk_name', 'date']
        ],
        'crypto' => [
            'name' => 'Crypto Transactions',
            'description' => 'Cryptocurrency transaction records',
            'primary_key' => 'id',
            'sample_columns' => ['cs_id', 'email', 'amt', 'date', 'status']
        ]
    ];
    
    public function __construct() {
        $this->path_manager = getPathManager();
    }
    
    /**
     * Get database connection
     */
    private function getDatabaseConnection() {
        if ($this->db_connection !== null) {
            return $this->db_connection;
        }
        
        $connectdb_path = $this->path_manager->getDatabaseConnectionFile();
        if (!file_exists($connectdb_path)) {
            throw new Exception('Database connection file not found');
        }
        
        $content = file_get_contents($connectdb_path);
        
        // Extract credentials (simplified version)
        preg_match('/host["\']?\s*=\s*["\']([^"\']+)["\']/', $content, $host_match);
        preg_match('/db_name["\']?\s*=\s*["\']([^"\']+)["\']/', $content, $db_match);
        preg_match('/username["\']?\s*=\s*["\']([^"\']+)["\']/', $content, $user_match);
        preg_match('/password["\']?\s*=\s*["\']([^"\']+)["\']/', $content, $pass_match);
        
        $host = $host_match[1] ?? 'localhost';
        $dbname = $db_match[1] ?? '';
        $username = $user_match[1] ?? '';
        $password = $pass_match[1] ?? '';
        
        $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
        $this->db_connection = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        return $this->db_connection;
    }
    
    /**
     * Get table information and record counts
     */
    public function getTableInfo($table_name = null) {
        try {
            $pdo = $this->getDatabaseConnection();
            $results = [];
            
            $tables_to_check = $table_name ? [$table_name] : array_keys($this->manageable_tables);
            
            foreach ($tables_to_check as $table) {
                if (!isset($this->manageable_tables[$table])) continue;
                
                $info = $this->manageable_tables[$table];
                
                // Check if table exists
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?");
                $stmt->execute([$table]);
                $table_exists = $stmt->fetchColumn() > 0;
                
                if ($table_exists) {
                    // Get record count
                    $count_stmt = $pdo->query("SELECT COUNT(*) FROM `{$table}`");
                    $record_count = $count_stmt->fetchColumn();
                    
                    // Get sample data
                    $sample_stmt = $pdo->query("SELECT * FROM `{$table}` LIMIT 3");
                    $sample_data = $sample_stmt->fetchAll();
                    
                    // Get table size
                    $size_stmt = $pdo->prepare("
                        SELECT 
                            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                        FROM information_schema.tables 
                        WHERE table_schema = DATABASE() AND table_name = ?
                    ");
                    $size_stmt->execute([$table]);
                    $size_info = $size_stmt->fetch();
                    
                    $results[$table] = [
                        'info' => $info,
                        'exists' => true,
                        'record_count' => $record_count,
                        'size_mb' => $size_info['size_mb'] ?? 0,
                        'sample_data' => $sample_data
                    ];
                } else {
                    $results[$table] = [
                        'info' => $info,
                        'exists' => false,
                        'record_count' => 0,
                        'size_mb' => 0,
                        'sample_data' => []
                    ];
                }
            }
            
            return [
                'success' => true,
                'tables' => $results
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to get table info: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Clear table data with optional backup
     */
    public function clearTableData($table_name, $create_backup = true) {
        try {
            if (!isset($this->manageable_tables[$table_name])) {
                throw new Exception('Table not manageable: ' . $table_name);
            }
            
            $pdo = $this->getDatabaseConnection();
            
            // Check if table exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?");
            $stmt->execute([$table_name]);
            $table_exists = $stmt->fetchColumn() > 0;
            
            if (!$table_exists) {
                return [
                    'success' => false,
                    'message' => "Table '{$table_name}' does not exist"
                ];
            }
            
            // Get current record count
            $count_stmt = $pdo->query("SELECT COUNT(*) FROM `{$table_name}`");
            $record_count = $count_stmt->fetchColumn();
            
            if ($record_count == 0) {
                return [
                    'success' => true,
                    'message' => "Table '{$table_name}' is already empty",
                    'records_cleared' => 0,
                    'backup_created' => false
                ];
            }
            
            $backup_file = null;
            
            // Create backup if requested
            if ($create_backup) {
                $backup_result = $this->createTableBackup($table_name);
                if ($backup_result['success']) {
                    $backup_file = $backup_result['backup_file'];
                }
            }
            
            // Clear the table
            $pdo->exec("TRUNCATE TABLE `{$table_name}`");
            
            return [
                'success' => true,
                'message' => "Table '{$table_name}' cleared successfully",
                'records_cleared' => $record_count,
                'backup_created' => $create_backup,
                'backup_file' => $backup_file
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to clear table: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Create backup of table data
     */
    private function createTableBackup($table_name) {
        try {
            $pdo = $this->getDatabaseConnection();
            
            // Create backup directory if it doesn't exist
            $backup_dir = $this->path_manager->getInstallerDir() . '/backups/';
            if (!is_dir($backup_dir)) {
                mkdir($backup_dir, 0755, true);
            }
            
            // Generate backup filename
            $timestamp = date('Y-m-d_H-i-s');
            $backup_filename = "{$table_name}_backup_{$timestamp}.sql";
            $backup_filepath = $backup_dir . $backup_filename;
            
            // Get table structure
            $create_stmt = $pdo->query("SHOW CREATE TABLE `{$table_name}`");
            $create_info = $create_stmt->fetch();
            
            // Get table data
            $data_stmt = $pdo->query("SELECT * FROM `{$table_name}`");
            $data = $data_stmt->fetchAll();
            
            // Build backup content
            $backup_content = "-- Table backup for {$table_name}\n";
            $backup_content .= "-- Created: " . date('Y-m-d H:i:s') . "\n\n";
            $backup_content .= $create_info['Create Table'] . ";\n\n";
            
            if (!empty($data)) {
                $backup_content .= "INSERT INTO `{$table_name}` VALUES\n";
                $values = [];
                foreach ($data as $row) {
                    $escaped_values = array_map(function($value) use ($pdo) {
                        return $pdo->quote($value);
                    }, array_values($row));
                    $values[] = '(' . implode(', ', $escaped_values) . ')';
                }
                $backup_content .= implode(",\n", $values) . ";\n";
            }
            
            // Write backup file
            file_put_contents($backup_filepath, $backup_content);
            
            return [
                'success' => true,
                'backup_file' => $backup_filename,
                'backup_path' => $backup_filepath
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Backup failed: ' . $e->getMessage()
            ];
        }
    }
}

// Initialize manager
$tableManager = new TableDataManager();

// Handle form submissions
$message = '';
$messageType = '';
$tableInfo = [];
$clearResults = [];

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'get_table_info':
                $tableInfo = $tableManager->getTableInfo();
                if ($tableInfo['success']) {
                    $message = "✅ Table information retrieved successfully.";
                    $messageType = 'success';
                } else {
                    $message = "❌ " . $tableInfo['message'];
                    $messageType = 'danger';
                }
                break;
                
            case 'clear_table':
                $table_name = $_POST['table_name'] ?? '';
                $create_backup = isset($_POST['create_backup']);
                
                if (!empty($table_name)) {
                    $clearResults = $tableManager->clearTableData($table_name, $create_backup);
                    $message = $clearResults['message'];
                    $messageType = $clearResults['success'] ? 'success' : 'danger';
                    
                    if ($clearResults['success']) {
                        logSecurityEvent('Table data cleared via installer', 'INFO', [
                            'table' => $table_name,
                            'records_cleared' => $clearResults['records_cleared'],
                            'backup_created' => $clearResults['backup_created']
                        ]);
                    }
                } else {
                    $message = '⚠️ Please select a table to clear.';
                    $messageType = 'warning';
                }
                break;
        }
    }
}

echo getPageHeader('Table Data Manager', 'Selective table data management - clear, backup, and restore specific tables');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🗂️ Table Data Manager</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>🎯 Purpose:</strong><br>
                    Manage large amounts of data in specific tables without affecting the overall database structure.
                    Perfect for clearing test data, demo records, or problematic entries before fresh imports.
                </div>

                <div class="alert alert-warning">
                    <strong>⚠️ Important:</strong><br>
                    • Always create backups before clearing data<br>
                    • This only affects DATA, not table structure<br>
                    • Cleared data cannot be recovered without backups
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Table Information -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>📊 Get Table Information</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Check current status and record counts for manageable tables.</p>

                <form method="post">
                    <input type="hidden" name="action" value="get_table_info">
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-info-circle"></i> Get Table Info
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>🧹 Clear Table Data</h5>
            </div>
            <div class="card-body">
                <form method="post" id="clearTableForm">
                    <input type="hidden" name="action" value="clear_table">

                    <div class="mb-3">
                        <label for="table_name" class="form-label">Select Table to Clear</label>
                        <select class="form-control" id="table_name" name="table_name" required>
                            <option value="">Choose table...</option>
                            <option value="alerts">Alerts (Transaction notifications)</option>
                            <option value="temp_transfer">Temporary Transfers (Pending transfers)</option>
                            <option value="transfer">Completed Transfers</option>
                            <option value="crypto">Crypto Transactions</option>
                        </select>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="create_backup" name="create_backup" checked>
                        <label class="form-check-label" for="create_backup">
                            <strong>Create Backup Before Clearing</strong><br>
                            <small class="text-muted">Recommended: Creates SQL backup file in /backups/ directory</small>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-danger" onclick="return confirmClearTable()">
                        <i class="fas fa-trash-alt"></i> Clear Table Data
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Table Information Results -->
<?php if (!empty($tableInfo) && $tableInfo['success']): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📋 Table Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($tableInfo['tables'] as $table_name => $table_data): ?>
                        <div class="col-md-6 col-xl-3 mb-4">
                            <div class="card border-<?php echo $table_data['exists'] ? 'success' : 'secondary'; ?>">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <?php echo htmlspecialchars($table_data['info']['name']); ?>
                                        <span class="badge bg-<?php echo $table_data['exists'] ? 'success' : 'secondary'; ?> ms-2">
                                            <?php echo $table_data['exists'] ? 'Exists' : 'Not Found'; ?>
                                        </span>
                                    </h6>

                                    <p class="card-text small text-muted">
                                        <?php echo htmlspecialchars($table_data['info']['description']); ?>
                                    </p>

                                    <?php if ($table_data['exists']): ?>
                                        <div class="mb-2">
                                            <strong>Records:</strong>
                                            <span class="badge bg-<?php echo $table_data['record_count'] > 1000 ? 'warning' : ($table_data['record_count'] > 0 ? 'info' : 'secondary'); ?>">
                                                <?php echo number_format($table_data['record_count']); ?>
                                            </span>
                                        </div>

                                        <div class="mb-2">
                                            <strong>Size:</strong> <?php echo $table_data['size_mb']; ?> MB
                                        </div>

                                        <?php if ($table_data['record_count'] > 0): ?>
                                            <div class="mt-3">
                                                <small class="text-muted">Sample data:</small>
                                                <div class="bg-light p-2 rounded small" style="max-height: 100px; overflow-y: auto;">
                                                    <?php foreach (array_slice($table_data['sample_data'], 0, 2) as $row): ?>
                                                        <div class="mb-1">
                                                            <?php
                                                            $sample_fields = array_slice($table_data['info']['sample_columns'], 0, 3);
                                                            $sample_values = [];
                                                            foreach ($sample_fields as $field) {
                                                                if (isset($row[$field])) {
                                                                    $value = strlen($row[$field]) > 15 ? substr($row[$field], 0, 15) . '...' : $row[$field];
                                                                    $sample_values[] = "<strong>{$field}:</strong> " . htmlspecialchars($value);
                                                                }
                                                            }
                                                            echo implode(', ', $sample_values);
                                                            ?>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Clear Results -->
<?php if (!empty($clearResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><?php echo $clearResults['success'] ? '✅' : '❌'; ?> Clear Operation Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-<?php echo $clearResults['success'] ? 'success' : 'danger'; ?>">
                    <strong><?php echo htmlspecialchars($clearResults['message']); ?></strong>
                </div>

                <?php if ($clearResults['success'] && isset($clearResults['records_cleared'])): ?>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h6 class="card-title">📊 Operation Summary</h6>
                                    <p class="mb-1"><strong>Records Cleared:</strong> <?php echo number_format($clearResults['records_cleared']); ?></p>
                                    <p class="mb-1"><strong>Backup Created:</strong> <?php echo $clearResults['backup_created'] ? 'Yes' : 'No'; ?></p>
                                    <?php if ($clearResults['backup_created'] && isset($clearResults['backup_file'])): ?>
                                        <p class="mb-0"><strong>Backup File:</strong> <code><?php echo htmlspecialchars($clearResults['backup_file']); ?></code></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb"></i> Next Steps:</h6>
                                <ul class="mb-0">
                                    <li>Table is now empty and ready for fresh data</li>
                                    <li>You can now import new SQL data without conflicts</li>
                                    <?php if ($clearResults['backup_created']): ?>
                                        <li>Backup is stored in <code>/backups/</code> directory</li>
                                    <?php endif; ?>
                                    <li>Use SQL Import to populate with new data</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function confirmClearTable() {
    const tableName = document.getElementById('table_name').value;
    const createBackup = document.getElementById('create_backup').checked;

    if (!tableName) {
        alert('Please select a table to clear.');
        return false;
    }

    let message = `Are you sure you want to clear ALL DATA from the "${tableName}" table?\n\n`;
    message += 'This action will:\n';
    message += '• Remove all records from the table\n';
    message += '• Keep the table structure intact\n';

    if (createBackup) {
        message += '• Create a backup file before clearing\n';
    } else {
        message += '• NOT create a backup (data will be lost permanently)\n';
    }

    message += '\nThis action cannot be undone without a backup. Continue?';

    return confirm(message);
}
</script>

<?php echo getPageFooter(); ?>
