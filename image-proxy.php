<?php
/**
 * Image Proxy Script
 * Serves images from the icon directory through HTTP
 */

// Include path manager for dynamic path handling
require_once __DIR__ . '/includes/path-manager.php';

// Security check - only allow access from the installer
if (!isset($_GET['file']) || empty($_GET['file'])) {
    http_response_code(404);
    exit('File not specified');
}

$file_param = $_GET['file'];
$path_manager = getPathManager();

// Handle verifying-permission files
if (strpos($file_param, 'verifying-permission/') === 0) {
    $filename = basename(substr($file_param, strlen('verifying-permission/')));
    $filepath = $path_manager->getRootDir() . '/verifying-permission/' . $filename;
} else {
    $filename = basename($file_param); // Security: only allow filenames, no paths
    $media_dir = $path_manager->getMediaDirectory();
    // Remove trailing slash if present and add filename
    $filepath = rtrim($media_dir, '/') . '/' . $filename;
}

// Debug: Log the file path for troubleshooting
error_log("Image Proxy Debug: Requested file: $file_param");
error_log("Image Proxy Debug: Resolved filepath: $filepath");
error_log("Image Proxy Debug: File exists: " . (file_exists($filepath) ? 'YES' : 'NO'));

// Check if file exists and is readable
if (!file_exists($filepath) || !is_readable($filepath)) {
    http_response_code(404);
    error_log("Image Proxy Error: File not found or not readable: $filepath");
    exit('File not found: ' . htmlspecialchars($filename));
}

// Get file info
$fileinfo = pathinfo($filepath);
$extension = strtolower($fileinfo['extension']);

// Set appropriate content type
$contentTypes = [
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'webp' => 'image/webp',
    'svg' => 'image/svg+xml',
    'ico' => 'image/x-icon'
];

if (!isset($contentTypes[$extension])) {
    http_response_code(415);
    exit('Unsupported file type');
}

// Check for cache-busting parameter
$cacheControl = 'public, max-age=3600'; // Default: cache for 1 hour
if (isset($_GET['v']) || isset($_GET['nocache'])) {
    $cacheControl = 'no-cache, no-store, must-revalidate';
}

// Set headers
header('Content-Type: ' . $contentTypes[$extension]);
header('Content-Length: ' . filesize($filepath));
header('Cache-Control: ' . $cacheControl);
header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime($filepath)) . ' GMT');

// Add cache-busting headers if requested
if (isset($_GET['nocache'])) {
    header('Pragma: no-cache');
    header('Expires: 0');
}

// Output the file
readfile($filepath);
exit;
?>
