<?php
/**
 * Cache Manager Module
 * Clear browser cache, server cache, and force refresh of media files
 */

require_once 'includes/auth.php';
requireAuth();

class CacheManager {

    private $cache_directories = [
        '../../cache/',
        '../../tmp/',
        '../../temp/',
        '../cache/',
        './cache/'
    ];

    private $main_domain_path = '../../'; // Path to main banking website
    
    private $htaccess_rules = [
        'no_cache' => '
# Disable caching for media files during development
<FilesMatch "\.(png|jpg|jpeg|gif|ico|svg|webp|mp4|webm|ogg)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</FilesMatch>

# Force browsers to revalidate
<IfModule mod_headers.c>
    Header always set Cache-Control "no-cache, no-store, must-revalidate"
    Header always set Pragma "no-cache"
    Header always set Expires "0"
</IfModule>
',
        'enable_cache' => '
# Enable caching for media files
<FilesMatch "\.(png|jpg|jpeg|gif|ico|svg|webp|mp4|webm|ogg)$">
    Header set Cache-Control "public, max-age=********"
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
</FilesMatch>
'
    ];
    
    /**
     * Detect the main domain URL
     */
    public function detectMainDomain() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];

        // Remove /mod/installer from the current path to get main domain
        $current_path = $_SERVER['REQUEST_URI'];
        $main_path = str_replace('/mod/installer', '', dirname($current_path));
        $main_path = rtrim($main_path, '/');

        return $protocol . '://' . $host . $main_path;
    }

    /**
     * Clear domain-specific cache by making HTTP requests
     */
    public function clearDomainCache() {
        $main_domain = $this->detectMainDomain();
        $results = [];

        // Reduced list of most important pages to avoid timeout
        $pages_to_clear = [
            '/',
            '/index.php',
            '/login.php'
        ];

        $cleared_pages = 0;
        $total_pages = count($pages_to_clear);

        foreach ($pages_to_clear as $page) {
            $url = $main_domain . $page;

            try {
                // Make HTTP request with cache-busting headers and shorter timeout
                $context = stream_context_create([
                    'http' => [
                        'method' => 'HEAD', // Use HEAD instead of GET for faster response
                        'header' => [
                            'Cache-Control: no-cache, no-store, must-revalidate',
                            'Pragma: no-cache',
                            'Expires: 0',
                            'User-Agent: BankingInstaller-CacheClearer/1.0'
                        ],
                        'timeout' => 3, // Reduced timeout to 3 seconds
                        'ignore_errors' => true
                    ]
                ]);

                $response = @file_get_contents($url, false, $context);

                // Check if we got any response (even error responses count as cache clearing)
                if ($response !== false || !empty($http_response_header)) {
                    $cleared_pages++;
                    $results[] = "✅ Cleared: " . $page;
                } else {
                    $results[] = "⚠️ Could not access: " . $page;
                }

            } catch (Exception $e) {
                $results[] = "❌ Error accessing: " . $page . " - " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'main_domain' => $main_domain,
            'cleared_pages' => $cleared_pages,
            'total_pages' => $total_pages,
            'details' => $results,
            'message' => "Domain cache clearing completed. Accessed {$cleared_pages}/{$total_pages} pages on {$main_domain}"
        ];
    }

    /**
     * Advanced cache clearing - SAFE method that only clears cache, doesn't modify code
     */
    public function advancedCacheClear() {
        $results = [];
        $timestamp = time();

        // Clear multiple cache layers without modifying any code
        $cache_operations = [
            'Server Cache' => $this->clearServerCache(),
            'Media Cache Busting' => $this->addCacheBusting(),
            'Session Cache' => $this->clearSessionCache(),
            'Domain Cache' => $this->simpleDomainCacheClear(),
            'Browser Cache Control' => $this->disableCaching()
        ];

        $total_operations = 0;
        $successful_operations = 0;

        foreach ($cache_operations as $operation_name => $result) {
            $total_operations++;
            if ($result['success']) {
                $successful_operations++;
                $results[] = "✅ {$operation_name}: " . $result['message'];
            } else {
                $results[] = "❌ {$operation_name}: " . $result['message'];
            }
        }

        return [
            'success' => true,
            'timestamp' => $timestamp,
            'successful_operations' => $successful_operations,
            'total_operations' => $total_operations,
            'details' => $results,
            'message' => "Advanced cache clearing completed. {$successful_operations}/{$total_operations} operations successful. NO CODE MODIFIED - only cache cleared."
        ];
    }

    /**
     * Clear PHP session cache
     */
    public function clearSessionCache() {
        $results = [];

        // Clear session files if accessible
        $session_paths = [
            '../../tmp/',
            '../../temp/',
            sys_get_temp_dir() . '/sess_*'
        ];

        $cleared_sessions = 0;

        foreach ($session_paths as $path) {
            if (is_dir($path)) {
                $session_files = glob($path . 'sess_*');
                foreach ($session_files as $session_file) {
                    if (is_file($session_file) && is_writable($session_file)) {
                        if (unlink($session_file)) {
                            $cleared_sessions++;
                        }
                    }
                }
                $results[] = "Cleared sessions from: " . $path;
            }
        }

        return [
            'success' => true,
            'cleared_sessions' => $cleared_sessions,
            'details' => $results,
            'message' => "Cleared {$cleared_sessions} session files"
        ];
    }

    /**
     * Safe favicon cache clearing - doesn't modify code
     */
    public function safeFaviconCacheClear() {
        $timestamp = time();

        // Create a favicon cache-bust file instead of modifying code
        $favicon_cache_file = $this->main_domain_path . 'favicon_cache_bust.txt';

        try {
            file_put_contents($favicon_cache_file, $timestamp);

            // Also update the cache manifest for favicon
            $manifest_file = $this->main_domain_path . 'icon/cache_manifest.json';
            if (file_exists($manifest_file)) {
                $manifest = json_decode(file_get_contents($manifest_file), true);
                $manifest['favicon.png'] = $timestamp;
                file_put_contents($manifest_file, json_encode($manifest, JSON_PRETTY_PRINT));
            }

            return [
                'success' => true,
                'timestamp' => $timestamp,
                'message' => "Safe favicon cache clearing completed. Created cache-bust file with timestamp: {$timestamp}"
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => "Failed to create favicon cache-bust file: " . $e->getMessage()
            ];
        }
    }

    /**
     * Simple domain cache clearing using .htaccess modification
     */
    public function simpleDomainCacheClear() {
        $main_domain = $this->detectMainDomain();
        $timestamp = time();

        // Create a simple cache-busting file in the main domain
        $cache_bust_file = $this->main_domain_path . 'cache_bust.txt';

        try {
            file_put_contents($cache_bust_file, $timestamp);

            return [
                'success' => true,
                'main_domain' => $main_domain,
                'timestamp' => $timestamp,
                'message' => "Simple domain cache clearing completed. Cache-bust file updated with timestamp: {$timestamp}"
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'main_domain' => $main_domain,
                'message' => "Failed to create cache-bust file: " . $e->getMessage()
            ];
        }
    }

    /**
     * Generate cache-busting URLs for media files
     */
    public function generateCacheBustingUrls() {
        $main_domain = $this->detectMainDomain();
        $timestamp = time();

        // Common media file paths
        $media_files = [
            '/icon/logo.png',
            '/icon/logodark.png',
            '/icon/favicon.png',
            '/icon/lbg.png',
            '/icon/apple-icon.png',
            '/images/logo.png',
            '/assets/images/logo.png',
            '/img/logo.png'
        ];

        $cache_busting_urls = [];

        foreach ($media_files as $file) {
            $cache_busting_urls[] = $main_domain . $file . '?v=' . $timestamp . '&nocache=1';
        }

        return [
            'success' => true,
            'timestamp' => $timestamp,
            'urls' => $cache_busting_urls,
            'main_domain' => $main_domain,
            'message' => "Generated " . count($cache_busting_urls) . " cache-busting URLs for media files"
        ];
    }

    /**
     * Clear server-side cache directories
     */
    public function clearServerCache() {
        $results = [];
        $total_cleared = 0;
        
        foreach ($this->cache_directories as $dir) {
            if (is_dir($dir)) {
                $cleared = $this->clearDirectory($dir);
                $results[$dir] = $cleared;
                $total_cleared += $cleared;
            }
        }
        
        return [
            'success' => true,
            'total_cleared' => $total_cleared,
            'directories' => $results,
            'message' => "Cleared {$total_cleared} cache files from server directories"
        ];
    }
    
    /**
     * Clear directory contents
     */
    private function clearDirectory($dir) {
        $cleared = 0;
        
        if (!is_dir($dir) || !is_writable($dir)) {
            return 0;
        }
        
        $files = glob($dir . '*');
        foreach ($files as $file) {
            if (is_file($file)) {
                if (unlink($file)) {
                    $cleared++;
                }
            }
        }
        
        return $cleared;
    }
    
    /**
     * Add cache-busting parameters to media files
     */
    public function addCacheBusting() {
        $media_dir = '../../icon/';
        $timestamp = time();
        $updated_files = [];
        
        if (!is_dir($media_dir)) {
            return [
                'success' => false,
                'message' => 'Media directory not found'
            ];
        }
        
        // Create a cache-busting manifest
        $manifest_file = $media_dir . 'cache_manifest.json';
        $manifest = [];
        
        $files = glob($media_dir . '*.{png,jpg,jpeg,gif,ico,svg,webp,mp4,webm,ogg}', GLOB_BRACE);
        foreach ($files as $file) {
            $filename = basename($file);
            $manifest[$filename] = $timestamp;
            $updated_files[] = $filename;
        }
        
        file_put_contents($manifest_file, json_encode($manifest, JSON_PRETTY_PRINT));
        
        return [
            'success' => true,
            'timestamp' => $timestamp,
            'files_updated' => count($updated_files),
            'message' => "Added cache-busting timestamp {$timestamp} to " . count($updated_files) . " media files"
        ];
    }
    
    /**
     * Disable caching via .htaccess
     */
    public function disableCaching() {
        $htaccess_files = [
            '../../.htaccess',
            '../../icon/.htaccess'
        ];
        
        $results = [];
        
        foreach ($htaccess_files as $htaccess_file) {
            $dir = dirname($htaccess_file);
            if (!is_dir($dir)) {
                continue;
            }
            
            $current_content = '';
            if (file_exists($htaccess_file)) {
                $current_content = file_get_contents($htaccess_file);
            }
            
            // Remove existing cache rules
            $current_content = preg_replace('/# Disable caching for media files.*?<\/IfModule>\s*/s', '', $current_content);
            $current_content = preg_replace('/# Enable caching for media files.*?<\/FilesMatch>\s*/s', '', $current_content);
            
            // Add no-cache rules
            $new_content = $current_content . "\n" . $this->htaccess_rules['no_cache'];
            
            if (file_put_contents($htaccess_file, $new_content)) {
                $results[] = "Updated " . basename(dirname($htaccess_file)) . "/.htaccess";
            }
        }
        
        return [
            'success' => true,
            'files_updated' => $results,
            'message' => 'Disabled caching in .htaccess files'
        ];
    }
    
    /**
     * Re-enable caching via .htaccess
     */
    public function enableCaching() {
        $htaccess_files = [
            '../../.htaccess',
            '../../icon/.htaccess'
        ];
        
        $results = [];
        
        foreach ($htaccess_files as $htaccess_file) {
            $dir = dirname($htaccess_file);
            if (!is_dir($dir)) {
                continue;
            }
            
            $current_content = '';
            if (file_exists($htaccess_file)) {
                $current_content = file_get_contents($htaccess_file);
            }
            
            // Remove existing cache rules
            $current_content = preg_replace('/# Disable caching for media files.*?<\/IfModule>\s*/s', '', $current_content);
            $current_content = preg_replace('/# Enable caching for media files.*?<\/FilesMatch>\s*/s', '', $current_content);
            
            // Add cache rules
            $new_content = $current_content . "\n" . $this->htaccess_rules['enable_cache'];
            
            if (file_put_contents($htaccess_file, $new_content)) {
                $results[] = "Updated " . basename(dirname($htaccess_file)) . "/.htaccess";
            }
        }
        
        return [
            'success' => true,
            'files_updated' => $results,
            'message' => 'Enabled caching in .htaccess files'
        ];
    }
    
    /**
     * Get cache status information
     */
    public function getCacheStatus() {
        $status = [
            'server_cache' => [],
            'htaccess_status' => [],
            'media_files' => []
        ];
        
        // Check server cache directories
        foreach ($this->cache_directories as $dir) {
            if (is_dir($dir)) {
                $files = glob($dir . '*');
                $status['server_cache'][$dir] = [
                    'exists' => true,
                    'files' => count($files),
                    'writable' => is_writable($dir)
                ];
            }
        }
        
        // Check .htaccess files
        $htaccess_files = ['../../.htaccess', '../../icon/.htaccess'];
        foreach ($htaccess_files as $file) {
            $status['htaccess_status'][$file] = [
                'exists' => file_exists($file),
                'writable' => is_writable(dirname($file)),
                'has_cache_rules' => false
            ];
            
            if (file_exists($file)) {
                $content = file_get_contents($file);
                $status['htaccess_status'][$file]['has_cache_rules'] = 
                    (strpos($content, 'Cache-Control') !== false);
            }
        }
        
        // Check media files
        $media_dir = '../../icon/';
        if (is_dir($media_dir)) {
            $files = glob($media_dir . '*.{png,jpg,jpeg,gif,ico,svg,webp}', GLOB_BRACE);
            $status['media_files'] = [
                'directory' => $media_dir,
                'total_files' => count($files),
                'last_modified' => 0
            ];
            
            foreach ($files as $file) {
                $mtime = filemtime($file);
                if ($mtime > $status['media_files']['last_modified']) {
                    $status['media_files']['last_modified'] = $mtime;
                }
            }
        }
        
        return $status;
    }
}

// Initialize cache manager
$cacheManager = new CacheManager();
$cacheStatus = $cacheManager->getCacheStatus();

// Handle form submissions with POST-Redirect-GET pattern
$message = '';
$messageType = '';
$actionResults = [];

// Check for success message from redirect
if (isset($_GET['success'])) {
    $message = urldecode($_GET['message'] ?? 'Operation completed successfully');
    $messageType = 'success';
    $actionResults = isset($_GET['results']) ? json_decode(urldecode($_GET['results']), true) : [];
}

if (isset($_GET['error'])) {
    $message = urldecode($_GET['message'] ?? 'Operation failed');
    $messageType = 'danger';
}

if ($_POST) {
    $action = $_POST['action'] ?? '';
    $redirectUrl = 'cache-manager.php';

    switch ($action) {
        case 'clear_server_cache':
            $actionResults = $cacheManager->clearServerCache();
            $message = $actionResults['message'];
            $messageType = $actionResults['success'] ? 'success' : 'error';
            break;

        case 'add_cache_busting':
            $actionResults = $cacheManager->addCacheBusting();
            $message = $actionResults['message'];
            $messageType = $actionResults['success'] ? 'success' : 'error';
            break;

        case 'disable_caching':
            $actionResults = $cacheManager->disableCaching();
            $message = $actionResults['message'];
            $messageType = $actionResults['success'] ? 'success' : 'error';
            break;

        case 'enable_caching':
            $actionResults = $cacheManager->enableCaching();
            $message = $actionResults['message'];
            $messageType = $actionResults['success'] ? 'success' : 'error';
            break;

        case 'clear_domain_cache':
            $actionResults = $cacheManager->clearDomainCache();
            $message = $actionResults['message'];
            $messageType = $actionResults['success'] ? 'success' : 'error';
            break;

        case 'simple_domain_cache_clear':
            $actionResults = $cacheManager->simpleDomainCacheClear();
            $message = $actionResults['message'];
            $messageType = $actionResults['success'] ? 'success' : 'error';
            break;

        case 'advanced_cache_clear':
            $actionResults = $cacheManager->advancedCacheClear();
            $message = $actionResults['message'];
            $messageType = $actionResults['success'] ? 'success' : 'error';
            break;

        case 'clear_session_cache':
            $actionResults = $cacheManager->clearSessionCache();
            $message = $actionResults['message'];
            $messageType = $actionResults['success'] ? 'success' : 'error';
            break;

        case 'safe_favicon_cache_clear':
            $actionResults = $cacheManager->safeFaviconCacheClear();
            $message = $actionResults['message'];
            $messageType = $actionResults['success'] ? 'success' : 'error';
            break;

        case 'generate_cache_busting_urls':
            $actionResults = $cacheManager->generateCacheBustingUrls();
            $message = $actionResults['message'];
            $messageType = $actionResults['success'] ? 'success' : 'error';
            break;
    }

    // Redirect to prevent form resubmission on refresh
    $redirectUrl .= '?' . $messageType . '=1';
    $redirectUrl .= '&message=' . urlencode($message);

    if (!empty($actionResults)) {
        $redirectUrl .= '&results=' . urlencode(json_encode($actionResults));
    }

    header('Location: ' . $redirectUrl);
    exit;
}

// Get cache status
$cacheStatus = $cacheManager->getCacheStatus();

// Display the page
echo getPageHeader('Cache Manager', 'Clear browser cache and manage media file caching');
echo displayMessage($message, $messageType);
?>

<!-- Cache Management Dashboard -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🧹 Cache Management Dashboard</h5>
            </div>
            <div class="card-body">
                <p class="mb-4">
                    <strong>Having trouble seeing updated logos or favicons?</strong> Use these tools to clear cache and force browsers to load fresh media files.
                </p>
                
                <!-- Quick Actions -->
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h6 class="card-title">🚀 Quick Cache Clear</h6>
                                <p class="card-text small">Clear all caches and force refresh</p>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="clear_all">
                                    <button type="button" class="btn btn-primary" onclick="clearAllCache()">
                                        <i class="fas fa-broom"></i> Clear All
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h6 class="card-title">🎯 Advanced Clear</h6>
                                <p class="card-text small">Fix hardcoded logo references</p>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="advanced_cache_clear">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-magic"></i> Advanced
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h6 class="card-title">⚡ Force Refresh</h6>
                                <p class="card-text small">Add timestamp to media files</p>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="add_cache_busting">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-sync"></i> Refresh
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h6 class="card-title">🌐 Domain Cache</h6>
                                <p class="card-text small">Clear cache on main website</p>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="clear_domain_cache">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-globe"></i> Domain
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Cache Controls -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6>🗂️ Server Cache</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">Clear server-side cache files and temporary data.</p>
                
                <?php if (!empty($cacheStatus['server_cache'])): ?>
                    <div class="mb-3">
                        <small class="text-muted">Cache Directories:</small>
                        <?php foreach ($cacheStatus['server_cache'] as $dir => $info): ?>
                            <div class="d-flex justify-content-between align-items-center border-bottom py-1">
                                <code class="small"><?php echo htmlspecialchars($dir); ?></code>
                                <span class="badge bg-<?php echo $info['files'] > 0 ? 'warning' : 'success'; ?>">
                                    <?php echo $info['files']; ?> files
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST">
                    <input type="hidden" name="action" value="clear_server_cache">
                    <button type="submit" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash"></i> Clear Server Cache
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6>🌐 Browser Cache Control</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">Control how browsers cache your media files.</p>
                
                <div class="mb-3">
                    <small class="text-muted">Current Status:</small>
                    <?php foreach ($cacheStatus['htaccess_status'] as $file => $info): ?>
                        <div class="d-flex justify-content-between align-items-center border-bottom py-1">
                            <code class="small"><?php echo basename(dirname($file)); ?>/.htaccess</code>
                            <span class="badge bg-<?php echo $info['has_cache_rules'] ? 'info' : 'secondary'; ?>">
                                <?php echo $info['has_cache_rules'] ? 'Has Rules' : 'No Rules'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="btn-group-vertical w-100" role="group">
                    <form method="POST" class="mb-2">
                        <input type="hidden" name="action" value="disable_caching">
                        <button type="submit" class="btn btn-outline-warning btn-sm w-100">
                            <i class="fas fa-ban"></i> Disable Caching
                        </button>
                    </form>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="enable_caching">
                        <button type="submit" class="btn btn-outline-success btn-sm w-100">
                            <i class="fas fa-check"></i> Enable Caching
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Advanced Cache Management -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">🧹 Advanced Cache Management - Safe Cache Clearing</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>🔍 Cache Analysis Complete!</strong> Multiple cache layers detected. This tool clears ALL cache types safely without modifying any code.
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <h6>Cache Types That Will Be Cleared:</h6>
                        <ul class="small">
                            <li><strong>Server Cache:</strong> Temporary files and cache directories</li>
                            <li><strong>Media Cache:</strong> Image and video file cache manifests</li>
                            <li><strong>Session Cache:</strong> PHP session files</li>
                            <li><strong>Domain Cache:</strong> Website page cache</li>
                            <li><strong>Browser Cache:</strong> .htaccess cache control headers</li>
                        </ul>

                        <div class="mt-3">
                            <strong>What Advanced Clear Does (SAFE):</strong>
                            <ul class="small text-success">
                                <li>✅ Clears all server-side cache files</li>
                                <li>✅ Updates media cache manifests with new timestamps</li>
                                <li>✅ Clears PHP session cache</li>
                                <li>✅ Forces domain cache refresh</li>
                                <li>✅ Disables browser caching temporarily</li>
                                <li>✅ <strong>NO CODE MODIFICATION</strong> - only cache clearing</li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="d-grid gap-2">
                            <form method="POST">
                                <input type="hidden" name="action" value="advanced_cache_clear">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-broom"></i><br>
                                    🧹 Advanced Cache Clear
                                </button>
                            </form>

                            <form method="POST">
                                <input type="hidden" name="action" value="safe_favicon_cache_clear">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-star"></i> Safe Favicon Clear
                                </button>
                            </form>

                            <form method="POST">
                                <input type="hidden" name="action" value="clear_session_cache">
                                <button type="submit" class="btn btn-outline-warning">
                                    <i class="fas fa-user-times"></i> Clear Sessions
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <?php if (isset($actionResults) && in_array($_POST['action'] ?? '', ['advanced_cache_clear', 'safe_favicon_cache_clear', 'clear_session_cache'])): ?>
                    <hr>
                    <div class="mt-3">
                        <h6>📊 Safe Cache Clear Results:</h6>

                        <?php if ($_POST['action'] === 'advanced_cache_clear'): ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Operations:</strong> <?php echo $actionResults['successful_operations']; ?>/<?php echo $actionResults['total_operations']; ?> successful<br>
                                    <strong>Timestamp:</strong> <?php echo $actionResults['timestamp']; ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Cache Operations:</strong>
                                    <div class="small">
                                        <?php foreach ($actionResults['details'] as $detail): ?>
                                            <div><?php echo htmlspecialchars($detail); ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($_POST['action'] === 'safe_favicon_cache_clear'): ?>
                            <div class="alert alert-success">
                                <strong>Safe Favicon Clear:</strong> Created cache-bust file with timestamp <?php echo $actionResults['timestamp']; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($_POST['action'] === 'clear_session_cache'): ?>
                            <div class="alert alert-info">
                                <strong>Session Cache:</strong> Cleared <?php echo $actionResults['cleared_sessions']; ?> session files
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Domain Cache Management -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">🌐 Domain Cache Management</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6>Main Website Cache Control</h6>
                        <p class="text-muted small">
                            Clear cache on your main banking website domain. This helps ensure updated logos and media files
                            are immediately visible to users visiting your website.
                        </p>

                        <div class="alert alert-info">
                            <strong>🎯 Detected Domain:</strong>
                            <code><?php echo htmlspecialchars($cacheManager->detectMainDomain()); ?></code>
                        </div>

                        <div class="mb-3">
                            <strong>Two Methods Available:</strong>
                            <ul class="small">
                                <li><strong>⚡ Quick Domain Clear:</strong> Fast method using cache-bust file (recommended)</li>
                                <li><strong>🌐 Advanced Domain Clear:</strong> Accesses website pages directly (slower but thorough)</li>
                                <li><strong>🔗 Generate URLs:</strong> Creates timestamped URLs for testing</li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="d-grid gap-2">
                            <form method="POST">
                                <input type="hidden" name="action" value="simple_domain_cache_clear">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-bolt"></i><br>
                                    Quick Domain Clear
                                </button>
                            </form>

                            <form method="POST">
                                <input type="hidden" name="action" value="clear_domain_cache">
                                <button type="submit" class="btn btn-outline-success">
                                    <i class="fas fa-globe"></i> Advanced Domain Clear
                                </button>
                            </form>

                            <form method="POST">
                                <input type="hidden" name="action" value="generate_cache_busting_urls">
                                <button type="submit" class="btn btn-outline-info">
                                    <i class="fas fa-link"></i> Generate URLs
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <?php if (isset($actionResults) && in_array($_POST['action'] ?? '', ['clear_domain_cache', 'generate_cache_busting_urls'])): ?>
                    <hr>
                    <div class="mt-3">
                        <h6>📊 Results:</h6>

                        <?php if ($_POST['action'] === 'clear_domain_cache'): ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Domain:</strong> <?php echo htmlspecialchars($actionResults['main_domain']); ?><br>
                                    <strong>Pages Cleared:</strong> <?php echo $actionResults['cleared_pages']; ?>/<?php echo $actionResults['total_pages']; ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Details:</strong>
                                    <div class="small">
                                        <?php foreach ($actionResults['details'] as $detail): ?>
                                            <div><?php echo htmlspecialchars($detail); ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if ($_POST['action'] === 'generate_cache_busting_urls'): ?>
                            <div class="row">
                                <div class="col-12">
                                    <strong>Cache-Busting URLs Generated:</strong>
                                    <div class="small mt-2">
                                        <?php foreach ($actionResults['urls'] as $url): ?>
                                            <div class="mb-1">
                                                <code><?php echo htmlspecialchars($url); ?></code>
                                                <button class="btn btn-sm btn-outline-primary ms-2" onclick="testUrl('<?php echo htmlspecialchars($url); ?>')">
                                                    Test
                                                </button>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Media Files Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6>📁 Media Files Status</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($cacheStatus['media_files'])): ?>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="text-primary"><?php echo $cacheStatus['media_files']['total_files']; ?></h4>
                                <small class="text-muted">Total Media Files</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="text-info"><?php echo date('Y-m-d H:i:s', $cacheStatus['media_files']['last_modified']); ?></h4>
                                <small class="text-muted">Last Modified</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="text-success"><?php echo htmlspecialchars($cacheStatus['media_files']['directory']); ?></h4>
                                <small class="text-muted">Media Directory</small>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Media directory not found or inaccessible.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Browser Instructions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">💡 Manual Browser Cache Clearing Instructions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>🖥️ Desktop Browsers:</h6>
                        <ul class="small">
                            <li><strong>Chrome/Edge:</strong> Ctrl+Shift+R or Ctrl+F5</li>
                            <li><strong>Firefox:</strong> Ctrl+Shift+R or Ctrl+F5</li>
                            <li><strong>Safari:</strong> Cmd+Shift+R</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>📱 Mobile Browsers:</h6>
                        <ul class="small">
                            <li><strong>Chrome Mobile:</strong> Menu → Settings → Privacy → Clear browsing data</li>
                            <li><strong>Safari Mobile:</strong> Settings → Safari → Clear History and Website Data</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3 mb-0">
                    <small>
                        <i class="fas fa-info-circle"></i>
                        <strong>Pro Tip:</strong> After uploading new logos or favicons, use the "Force Browser Refresh" button above, 
                        then press Ctrl+Shift+R (or Cmd+Shift+R on Mac) to see changes immediately.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function clearAllCache() {
    if (confirm('This will clear all cache including domain cache and force browsers to reload media files. Continue?')) {
        // Show loading indicator
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Clearing...';
        button.disabled = true;

        // Clear server cache first
        fetch('', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: 'action=clear_server_cache'
        }).then(response => {
            if (!response.ok) throw new Error('Server cache clearing failed');
            // Add cache busting
            return fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=add_cache_busting'
            });
        }).then(response => {
            if (!response.ok) throw new Error('Cache busting failed');
            // Advanced cache clear (fix hardcoded references)
            return fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=advanced_cache_clear'
            });
        }).then(response => {
            if (!response.ok) throw new Error('Advanced cache clearing failed');
            // Clear domain cache (this might take a moment)
            return fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=clear_domain_cache'
            });
        }).then(response => {
            if (!response.ok) throw new Error('Domain cache clearing failed');
            // Disable caching temporarily
            return fetch('', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=disable_caching'
            });
        }).then(response => {
            if (!response.ok) throw new Error('Cache disabling failed');
            // Success - redirect to show results
            window.location.href = 'cache-manager.php?success=1&message=' + encodeURIComponent('🎉 COMPLETE CACHE CLEAR SUCCESS! Cleared all cache layers safely without modifying any code. Your website should now show updated logos immediately!');
        }).catch(error => {
            console.error('Error:', error);
            button.innerHTML = originalText;
            button.disabled = false;
            alert('Some cache clearing operations failed: ' + error.message + '. Please try individual operations.');
        });
    }
}

function testUrl(url) {
    window.open(url, '_blank');
}
</script>

<?php include 'includes/footer.php'; ?>
