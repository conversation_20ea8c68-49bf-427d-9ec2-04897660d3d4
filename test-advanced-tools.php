<?php
/**
 * Test Advanced Tools
 * Test SQL Import and ZIP Extraction functionality
 */

require_once 'includes/auth.php';
requireAuth();

echo getPageHeader('Advanced Tools Test', 'Testing SQL Import and ZIP Extraction functionality');

// Test SQL Import
require_once 'sql-import.php';
$sqlImporter = new SQLImporter();

// Test ZIP Extraction
require_once 'zip-extraction.php';
$zipExtractor = new ZipExtractor();

?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🧪 Advanced Tools Integration Test</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>ℹ️ Testing Overview:</strong><br>
                    This page tests the new SQL Import and ZIP Extraction tools to ensure they work correctly
                    with your current system configuration.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SQL Import Tests -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🗄️ SQL Import Module Test</h5>
            </div>
            <div class="card-body">
                
                <h6>📁 SQL Files Detection:</h6>
                <?php
                $sqlFiles = $sqlImporter->getAvailableSQLFiles();
                ?>
                
                <div class="alert alert-<?php echo !empty($sqlFiles) ? 'success' : 'warning'; ?>">
                    <strong><?php echo !empty($sqlFiles) ? '✅' : '⚠️'; ?> SQL Files:</strong><br>
                    Found <?php echo count($sqlFiles); ?> SQL file(s) in the sql/ directory
                    
                    <?php if (!empty($sqlFiles)): ?>
                        <ul class="mt-2 mb-0">
                            <?php foreach ($sqlFiles as $file): ?>
                                <li>
                                    <code><?php echo htmlspecialchars($file['name']); ?></code> - 
                                    <?php echo number_format($file['size']); ?> bytes
                                    <span class="badge bg-<?php echo $file['readable'] ? 'success' : 'danger'; ?>">
                                        <?php echo $file['readable'] ? 'Readable' : 'Not Readable'; ?>
                                    </span>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
                
                <h6 class="mt-4">🔌 Database Connection Test:</h6>
                <?php
                $connectionTest = $sqlImporter->testConnection();
                ?>
                
                <div class="alert alert-<?php echo $connectionTest['success'] ? 'success' : 'danger'; ?>">
                    <strong><?php echo $connectionTest['success'] ? '✅' : '❌'; ?> Database Connection:</strong><br>
                    <?php echo htmlspecialchars($connectionTest['message']); ?>
                </div>
                
                <?php if ($connectionTest['success']): ?>
                    <h6 class="mt-4">📊 Database Information:</h6>
                    <?php
                    $dbInfo = $sqlImporter->getDatabaseInfo();
                    ?>
                    
                    <?php if ($dbInfo['success']): ?>
                        <div class="alert alert-success">
                            <strong>✅ Database Info Retrieved:</strong><br>
                            <strong>Database:</strong> <?php echo htmlspecialchars($dbInfo['database_name']); ?><br>
                            <strong>Tables:</strong> <?php echo $dbInfo['table_count']; ?> tables<br>
                            
                            <?php if (!empty($dbInfo['tables'])): ?>
                                <details class="mt-2">
                                    <summary>View Tables</summary>
                                    <div class="mt-2">
                                        <?php foreach (array_chunk($dbInfo['tables'], 4) as $tableChunk): ?>
                                            <div class="row">
                                                <?php foreach ($tableChunk as $table): ?>
                                                    <div class="col-md-3">
                                                        <code><?php echo htmlspecialchars($table); ?></code>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </details>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                
                <div class="alert alert-primary">
                    <strong>🎯 SQL Import Status:</strong><br>
                    <?php if (!empty($sqlFiles) && $connectionTest['success']): ?>
                        ✅ Ready for SQL import! You can import <?php echo count($sqlFiles); ?> SQL file(s).
                    <?php elseif (empty($sqlFiles)): ?>
                        ⚠️ No SQL files found. Place SQL files in the sql/ directory.
                    <?php else: ?>
                        ❌ Database connection failed. Check your database credentials.
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ZIP Extraction Tests -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📦 ZIP Extraction Module Test</h5>
            </div>
            <div class="card-body">
                
                <h6>📁 ZIP File Detection:</h6>
                <?php
                $zipInfo = $zipExtractor->checkZipFile();
                ?>
                
                <div class="alert alert-<?php echo $zipInfo['exists'] && $zipInfo['readable'] ? 'success' : 'warning'; ?>">
                    <strong><?php echo $zipInfo['exists'] && $zipInfo['readable'] ? '✅' : '⚠️'; ?> ZIP File:</strong><br>
                    
                    <strong>Path:</strong> <code><?php echo htmlspecialchars($zipInfo['path']); ?></code><br>
                    <strong>Exists:</strong> <?php echo $zipInfo['exists'] ? 'Yes' : 'No'; ?><br>
                    <strong>Readable:</strong> <?php echo $zipInfo['readable'] ? 'Yes' : 'No'; ?><br>
                    
                    <?php if ($zipInfo['exists']): ?>
                        <strong>Size:</strong> <?php echo number_format($zipInfo['size']); ?> bytes<br>
                        <strong>Modified:</strong> <?php echo $zipInfo['modified']; ?>
                    <?php endif; ?>
                </div>
                
                <h6 class="mt-4">🔧 System Requirements:</h6>
                <?php
                $permissions = $zipExtractor->checkPermissions();
                ?>
                
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Requirement</th>
                                <th>Status</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>ZIP Extension</td>
                                <td>
                                    <span class="badge bg-<?php echo $permissions['zip_extension_loaded'] ? 'success' : 'danger'; ?>">
                                        <?php echo $permissions['zip_extension_loaded'] ? 'Available' : 'Missing'; ?>
                                    </span>
                                </td>
                                <td><?php echo $permissions['zip_extension_loaded'] ? 'PHP ZIP extension loaded' : 'PHP ZIP extension required'; ?></td>
                            </tr>
                            <tr>
                                <td>PHP Version</td>
                                <td>
                                    <span class="badge bg-<?php echo $permissions['php_version_ok'] ? 'success' : 'danger'; ?>">
                                        <?php echo $permissions['php_version_ok'] ? 'Compatible' : 'Incompatible'; ?>
                                    </span>
                                </td>
                                <td>Current: <?php echo PHP_VERSION; ?></td>
                            </tr>
                            <tr>
                                <td>Write Permissions</td>
                                <td>
                                    <span class="badge bg-<?php echo $permissions['extract_path_writable'] ? 'success' : 'danger'; ?>">
                                        <?php echo $permissions['extract_path_writable'] ? 'OK' : 'Failed'; ?>
                                    </span>
                                </td>
                                <td>Root directory write access</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h6 class="mt-4">📂 Current Folder Structure:</h6>
                <?php
                $currentStructure = $zipExtractor->checkCurrentStructure();
                ?>
                
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Folder</th>
                                <th>Status</th>
                                <th>Files</th>
                                <th>Writable</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($currentStructure as $folder_name => $folder_info): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($folder_name); ?></code></td>
                                    <td>
                                        <span class="badge bg-<?php echo $folder_info['exists'] ? 'success' : 'secondary'; ?>">
                                            <?php echo $folder_info['exists'] ? 'Exists' : 'Missing'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo $folder_info['file_count']; ?> files</td>
                                    <td>
                                        <span class="badge bg-<?php echo $folder_info['writable'] ? 'success' : 'warning'; ?>">
                                            <?php echo $folder_info['writable'] ? 'Yes' : 'No'; ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="alert alert-primary">
                    <strong>🎯 ZIP Extraction Status:</strong><br>
                    <?php if ($zipInfo['exists'] && $zipInfo['readable'] && $permissions['all_ok']): ?>
                        ✅ Ready for ZIP extraction! All requirements met.
                    <?php elseif (!$zipInfo['exists']): ?>
                        ⚠️ banking.zip file not found in root directory.
                    <?php elseif (!$permissions['all_ok']): ?>
                        ❌ System requirements not met. Check permissions and PHP extensions.
                    <?php else: ?>
                        ⚠️ ZIP file found but not readable.
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Overall Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📊 Overall Status Summary</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-<?php echo (!empty($sqlFiles) && $connectionTest['success']) ? 'success' : 'warning'; ?>">
                            <div class="card-body text-center">
                                <h5 class="text-<?php echo (!empty($sqlFiles) && $connectionTest['success']) ? 'success' : 'warning'; ?>">
                                    <?php echo (!empty($sqlFiles) && $connectionTest['success']) ? '✅' : '⚠️'; ?>
                                </h5>
                                <h6>SQL Import</h6>
                                <p class="small mb-0">
                                    <?php echo (!empty($sqlFiles) && $connectionTest['success']) ? 'Ready to import SQL files' : 'Needs attention'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-<?php echo ($zipInfo['exists'] && $zipInfo['readable'] && $permissions['all_ok']) ? 'success' : 'warning'; ?>">
                            <div class="card-body text-center">
                                <h5 class="text-<?php echo ($zipInfo['exists'] && $zipInfo['readable'] && $permissions['all_ok']) ? 'success' : 'warning'; ?>">
                                    <?php echo ($zipInfo['exists'] && $zipInfo['readable'] && $permissions['all_ok']) ? '✅' : '⚠️'; ?>
                                </h5>
                                <h6>ZIP Extraction</h6>
                                <p class="small mb-0">
                                    <?php echo ($zipInfo['exists'] && $zipInfo['readable'] && $permissions['all_ok']) ? 'Ready to extract ZIP' : 'Needs attention'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="sql-import.php" class="btn btn-primary">
            <i class="fas fa-database"></i> SQL Import Tool
        </a>
        <a href="zip-extraction.php" class="btn btn-warning ms-2">
            <i class="fas fa-file-archive"></i> ZIP Extraction Tool
        </a>
        <a href="index.php" class="btn btn-secondary ms-2">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php echo getPageFooter(); ?>
