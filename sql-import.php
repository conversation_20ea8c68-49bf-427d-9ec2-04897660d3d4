<?php
/**
 * SQL Import Module
 * Import SQL files from installer to database automatically
 */

require_once 'includes/auth.php';
requireAuth();

class SQLImporter {

    private $sql_directory = 'sql/';
    private $db_connection = null;
    private $path_manager;

    public function __construct() {
        $this->path_manager = getPathManager();
    }
    
    /**
     * Get available SQL files
     */
    public function getAvailableSQLFiles() {
        $files = [];
        
        if (is_dir($this->sql_directory)) {
            $iterator = new DirectoryIterator($this->sql_directory);
            
            foreach ($iterator as $file) {
                if ($file->isFile() && strtolower($file->getExtension()) === 'sql') {
                    $filepath = $this->sql_directory . $file->getFilename();
                    
                    $files[] = [
                        'name' => $file->getFilename(),
                        'path' => $filepath,
                        'size' => $file->getSize(),
                        'modified' => date('Y-m-d H:i:s', $file->getMTime()),
                        'readable' => is_readable($filepath)
                    ];
                }
            }
        }
        
        return $files;
    }
    
    /**
     * Get database connection using current credentials
     */
    private function getDatabaseConnection() {
        if ($this->db_connection !== null) {
            return $this->db_connection;
        }
        
        // Try to get database credentials from connectdb.php
        $connectdb_path = $this->path_manager->getDatabaseConnectionFile();
        if (!file_exists($connectdb_path)) {
            throw new Exception('Database connection file not found: ' . $connectdb_path);
        }
        
        $content = file_get_contents($connectdb_path);
        
        // Extract database credentials
        $host = $this->extractCredential($content, 'host');
        $dbname = $this->extractCredential($content, 'db_name');
        $username = $this->extractCredential($content, 'username');
        $password = $this->extractCredential($content, 'password');
        
        if (!$host || !$dbname || !$username || !$password) {
            throw new Exception('Could not extract database credentials from connectdb.php');
        }
        
        try {
            $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
            $this->db_connection = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]);
            
            return $this->db_connection;
        } catch (PDOException $e) {
            throw new Exception('Database connection failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Extract database credential from content
     */
    private function extractCredential($content, $type) {
        $patterns = [
            'host' => [
                '/private\s+\$host\s*=\s*["\']([^"\']+)["\']/',
                '/\$(?:DB_host|host)\s*=\s*["\']([^"\']+)["\']/'
            ],
            'db_name' => [
                '/private\s+\$db_name\s*=\s*["\']([^"\']+)["\']/',
                '/\$(?:DB_name|database)\s*=\s*["\']([^"\']+)["\']/'
            ],
            'username' => [
                '/private\s+\$username\s*=\s*["\']([^"\']+)["\']/',
                '/\$(?:DB_user|username)\s*=\s*["\']([^"\']+)["\']/'
            ],
            'password' => [
                '/private\s+\$password\s*=\s*["\']([^"\']+)["\']/',
                '/\$(?:DB_pass|password)\s*=\s*["\']([^"\']+)["\']/'
            ]
        ];
        
        if (!isset($patterns[$type])) {
            return null;
        }
        
        foreach ($patterns[$type] as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }
    
    /**
     * Test database connection
     */
    public function testConnection() {
        try {
            $pdo = $this->getDatabaseConnection();
            $stmt = $pdo->query('SELECT 1');
            return [
                'success' => true,
                'message' => 'Database connection successful'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Import SQL file with advanced options
     */
    public function importSQLFile($filename, $options = []) {
        $filepath = $this->sql_directory . $filename;
        
        if (!file_exists($filepath)) {
            return [
                'success' => false,
                'message' => 'SQL file not found: ' . $filename
            ];
        }
        
        if (!is_readable($filepath)) {
            return [
                'success' => false,
                'message' => 'SQL file is not readable: ' . $filename
            ];
        }
        
        try {
            $pdo = $this->getDatabaseConnection();
            $sql_content = file_get_contents($filepath);

            $executed = 0;
            $errors = [];
            $pre_statements = [];
            $post_statements = [];

            // Handle foreign key constraints
            if (isset($options['disable_foreign_keys']) && $options['disable_foreign_keys']) {
                $pre_statements[] = 'SET FOREIGN_KEY_CHECKS = 0';
                $post_statements[] = 'SET FOREIGN_KEY_CHECKS = 1';
            }

            // Handle duplicate cleaning (after import)
            if (isset($options['clean_duplicates']) && $options['clean_duplicates']) {
                // Add duplicate removal statements for each table
                $post_statements[] = "DELETE t1 FROM alerts t1 INNER JOIN alerts t2 WHERE t1.id > t2.id AND t1.cs_uname = t2.cs_uname AND t1.amount = t2.amount AND t1.date = t2.date";
                $post_statements[] = "DELETE t1 FROM temp_transfer t1 INNER JOIN temp_transfer t2 WHERE t1.id > t2.id AND t1.email = t2.email AND t1.amount = t2.amount AND t1.date = t2.date";
                $post_statements[] = "DELETE t1 FROM transfer t1 INNER JOIN transfer t2 WHERE t1.id > t2.id AND t1.email = t2.email AND t1.amount = t2.amount AND t1.date = t2.date";
            }

            // Handle table truncation (check existence first)
            if (isset($options['truncate_tables']) && $options['truncate_tables']) {
                $tables_to_truncate = ['alerts', 'temp_transfer', 'transfer'];
                foreach ($tables_to_truncate as $table) {
                    // Check if table exists before attempting to truncate
                    try {
                        $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?");
                        $check_stmt->execute([$table]);
                        $table_exists = $check_stmt->fetchColumn() > 0;

                        if ($table_exists) {
                            $pre_statements[] = "TRUNCATE TABLE `{$table}`";
                        }
                    } catch (PDOException $e) {
                        // If we can't check, skip this table
                        $errors[] = [
                            'error' => 'Table existence check failed for ' . $table . ': ' . $e->getMessage(),
                            'statement_number' => 'CHECK-' . $table,
                            'statement_preview' => "Table existence check for {$table}",
                            'statement_length' => 0
                        ];
                    }
                }
            }

            // Execute pre-statements
            foreach ($pre_statements as $statement) {
                try {
                    $pdo->exec($statement);
                    $executed++;
                } catch (PDOException $e) {
                    $errors[] = [
                        'error' => 'Pre-statement error: ' . $e->getMessage(),
                        'statement_number' => 'PRE-' . (count($errors) + 1),
                        'statement_preview' => $statement,
                        'statement_length' => strlen($statement)
                    ];
                }
            }

            // Remove comments and split into statements
            $statements = $this->parseSQLFile($sql_content);

            // Execute main SQL statements
            foreach ($statements as $index => $statement) {
                $statement = trim($statement);
                if (empty($statement)) continue;

                // Skip statements that don't look like valid SQL
                if (!preg_match('/^(INSERT|UPDATE|DELETE|CREATE|ALTER|DROP|SET)/i', $statement)) {
                    continue;
                }

                try {
                    $pdo->exec($statement);
                    $executed++;
                } catch (PDOException $e) {
                    $statement_preview = strlen($statement) > 200 ? substr($statement, 0, 200) . '...' : $statement;
                    $errors[] = [
                        'error' => $e->getMessage(),
                        'statement_number' => $index + 1,
                        'statement_preview' => $statement_preview,
                        'statement_length' => strlen($statement)
                    ];
                    // Continue with other statements for better error reporting
                }
            }

            // Execute post-statements
            foreach ($post_statements as $statement) {
                try {
                    $pdo->exec($statement);
                    $executed++;
                } catch (PDOException $e) {
                    $errors[] = [
                        'error' => 'Post-statement error: ' . $e->getMessage(),
                        'statement_number' => 'POST-' . (count($errors) + 1),
                        'statement_preview' => $statement,
                        'statement_length' => strlen($statement)
                    ];
                }
            }

            if (empty($errors)) {
                return [
                    'success' => true,
                    'message' => "SQL import successful! Executed {$executed} statements.",
                    'statements_executed' => $executed,
                    'errors' => [],
                    'options_used' => $options
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "SQL import completed with " . count($errors) . " errors. {$executed} statements executed successfully.",
                    'statements_executed' => $executed,
                    'errors' => $errors,
                    'options_used' => $options
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage(),
                'statements_executed' => 0,
                'errors' => [$e->getMessage()]
            ];
        }
    }

    /**
     * Clean SQL file and create a cleaned version
     */
    public function cleanSQLFile($filename) {
        $filepath = $this->sql_directory . $filename;

        if (!file_exists($filepath)) {
            return [
                'success' => false,
                'message' => 'SQL file not found: ' . $filename
            ];
        }

        if (!is_readable($filepath)) {
            return [
                'success' => false,
                'message' => 'SQL file is not readable: ' . $filename
            ];
        }

        try {
            $original_content = file_get_contents($filepath);
            $cleaned_content = $this->cleanupSQLContent($original_content);

            // Create cleaned filename
            $pathinfo = pathinfo($filename);
            $cleaned_filename = $pathinfo['filename'] . '_cleaned.' . $pathinfo['extension'];
            $cleaned_filepath = $this->sql_directory . $cleaned_filename;

            // Write cleaned content to new file
            $bytes_written = file_put_contents($cleaned_filepath, $cleaned_content);

            if ($bytes_written === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to write cleaned SQL file: ' . $cleaned_filename
                ];
            }

            // Get statistics
            $original_size = filesize($filepath);
            $cleaned_size = filesize($cleaned_filepath);
            $original_lines = substr_count($original_content, "\n") + 1;
            $cleaned_lines = substr_count($cleaned_content, "\n") + 1;

            return [
                'success' => true,
                'message' => "✅ SQL file cleaned successfully!",
                'original_file' => $filename,
                'cleaned_file' => $cleaned_filename,
                'original_size' => $original_size,
                'cleaned_size' => $cleaned_size,
                'original_lines' => $original_lines,
                'cleaned_lines' => $cleaned_lines,
                'size_reduction' => $original_size - $cleaned_size,
                'line_reduction' => $original_lines - $cleaned_lines
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Cleanup failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Parse SQL file into individual statements
     */
    private function parseSQLFile($content) {
        // First, clean up the SQL content
        $content = $this->cleanupSQLContent($content);

        // Remove SQL comments more carefully
        $content = preg_replace('/--.*$/m', '', $content);
        $content = preg_replace('/\/\*.*?\*\//s', '', $content);

        // Normalize line endings but preserve structure
        $content = str_replace(["\r\n", "\r"], "\n", $content);

        // Smart SQL statement splitting that respects quoted strings
        $statements = $this->splitSQLStatements($content);

        // Filter out empty statements and clean up
        $statements = array_filter($statements, function($stmt) {
            $stmt = trim($stmt);
            return !empty($stmt) && strlen($stmt) > 5; // Ignore very short statements
        });

        // Clean up each statement
        $statements = array_map(function($stmt) {
            return trim($stmt);
        }, $statements);

        return array_values($statements); // Re-index array
    }

    /**
     * Clean up SQL file content before parsing
     */
    private function cleanupSQLContent($content) {
        // Fix HTML entities
        $content = str_replace('&amp;', '&', $content);
        $content = str_replace('&lt;', '<', $content);
        $content = str_replace('&gt;', '>', $content);
        $content = str_replace('&quot;', '"', $content);

        // Fix common encoding issues
        $content = str_replace("\u2018", "'", $content); // Left single quote
        $content = str_replace("\u2019", "'", $content); // Right single quote
        $content = str_replace("\u201C", '"', $content); // Left double quote
        $content = str_replace("\u201D", '"', $content); // Right double quote

        // Fix broken INSERT statements by ensuring proper line endings
        $content = preg_replace('/INSERT INTO\s+`([^`]+)`\s+\([^)]+\)\s+VALUES\s*\n\s*\(/i',
                               'INSERT INTO `$1` VALUES (', $content);

        // Remove problematic characters that might break parsing
        $content = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $content);

        // Fix malformed statements that start without INSERT
        $lines = explode("\n", $content);
        $cleaned_lines = [];
        $in_insert = false;
        $current_insert = '';

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip empty lines and comments
            if (empty($line) || strpos($line, '--') === 0) {
                if (!$in_insert) {
                    $cleaned_lines[] = $line;
                }
                continue;
            }

            // Check if this is the start of an INSERT statement
            if (preg_match('/^INSERT INTO/i', $line)) {
                // If we were in an insert, finish it
                if ($in_insert && !empty($current_insert)) {
                    $cleaned_lines[] = $current_insert;
                }
                $current_insert = $line;
                $in_insert = true;
            } elseif ($in_insert) {
                // Continue building the INSERT statement
                $current_insert .= ' ' . $line;

                // Check if this line ends the INSERT (ends with );
                if (preg_match('/\);?\s*$/', $line)) {
                    $cleaned_lines[] = $current_insert;
                    $current_insert = '';
                    $in_insert = false;
                }
            } else {
                // Regular line, not part of INSERT
                $cleaned_lines[] = $line;
            }
        }

        // Add any remaining INSERT statement
        if ($in_insert && !empty($current_insert)) {
            $cleaned_lines[] = $current_insert;
        }

        return implode("\n", $cleaned_lines);
    }

    /**
     * Split SQL content into statements while respecting quoted strings
     */
    private function splitSQLStatements($content) {
        $statements = [];
        $current_statement = '';
        $in_single_quote = false;
        $in_double_quote = false;
        $escaped = false;

        $length = strlen($content);

        for ($i = 0; $i < $length; $i++) {
            $char = $content[$i];
            $next_char = ($i + 1 < $length) ? $content[$i + 1] : '';

            // Handle escape sequences
            if ($escaped) {
                $current_statement .= $char;
                $escaped = false;
                continue;
            }

            // Check for escape character
            if ($char === '\\') {
                $current_statement .= $char;
                $escaped = true;
                continue;
            }

            // Handle quotes
            if ($char === "'" && !$in_double_quote) {
                $in_single_quote = !$in_single_quote;
                $current_statement .= $char;
                continue;
            }

            if ($char === '"' && !$in_single_quote) {
                $in_double_quote = !$in_double_quote;
                $current_statement .= $char;
                continue;
            }

            // Handle semicolon (statement separator)
            if ($char === ';' && !$in_single_quote && !$in_double_quote) {
                $current_statement = trim($current_statement);
                if (!empty($current_statement)) {
                    $statements[] = $current_statement;
                }
                $current_statement = '';
                continue;
            }

            // Add character to current statement
            $current_statement .= $char;
        }

        // Add the last statement if it exists
        $current_statement = trim($current_statement);
        if (!empty($current_statement)) {
            $statements[] = $current_statement;
        }

        return $statements;
    }
    
    /**
     * Get database info
     */
    public function getDatabaseInfo() {
        try {
            $pdo = $this->getDatabaseConnection();
            
            // Get database name
            $stmt = $pdo->query('SELECT DATABASE() as db_name');
            $db_info = $stmt->fetch();
            
            // Get table count
            $stmt = $pdo->query('SHOW TABLES');
            $tables = $stmt->fetchAll();
            
            return [
                'success' => true,
                'database_name' => $db_info['db_name'],
                'table_count' => count($tables),
                'tables' => array_column($tables, array_keys($tables[0])[0])
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to get database info: ' . $e->getMessage()
            ];
        }
    }
}

// Initialize importer
$sqlImporter = new SQLImporter();

// Handle form submissions
$message = '';
$messageType = '';
$importResults = [];
$connectionTest = [];
$databaseInfo = [];

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'test_connection':
                $connectionTest = $sqlImporter->testConnection();
                $message = $connectionTest['message'];
                $messageType = $connectionTest['success'] ? 'success' : 'danger';
                break;
                
            case 'import_sql':
                $filename = $_POST['sql_file'] ?? '';
                if (!empty($filename)) {
                    // Collect import options
                    $options = [
                        'disable_foreign_keys' => isset($_POST['disable_foreign_keys']),
                        'truncate_tables' => isset($_POST['truncate_tables']),
                        'clean_duplicates' => isset($_POST['clean_duplicates'])
                    ];

                    $importResults = $sqlImporter->importSQLFile($filename, $options);
                    $message = $importResults['message'];
                    $messageType = $importResults['success'] ? 'success' : 'danger';
                    
                    if ($importResults['success']) {
                        logSecurityEvent('SQL file imported via installer', 'INFO', [
                            'filename' => $filename,
                            'statements_executed' => $importResults['statements_executed']
                        ]);
                    }
                } else {
                    $message = '⚠️ Please select an SQL file to import.';
                    $messageType = 'warning';
                }
                break;

            case 'clean_sql':
                $filename = $_POST['sql_file'] ?? '';
                if (!empty($filename)) {
                    $cleanResults = $sqlImporter->cleanSQLFile($filename);
                    $message = $cleanResults['message'];
                    $messageType = $cleanResults['success'] ? 'success' : 'danger';
                } else {
                    $message = 'Please select a SQL file to clean.';
                    $messageType = 'warning';
                }
                break;

            case 'get_db_info':
                $databaseInfo = $sqlImporter->getDatabaseInfo();
                if ($databaseInfo['success']) {
                    $message = "✅ Database info retrieved successfully.";
                    $messageType = 'success';
                } else {
                    $message = "❌ " . $databaseInfo['message'];
                    $messageType = 'danger';
                }
                break;
        }
    }
}

$availableFiles = $sqlImporter->getAvailableSQLFiles();

echo getPageHeader('SQL Import Manager', 'Import SQL files directly from the installer dashboard');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🗄️ SQL Import Manager</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>ℹ️ About SQL Import:</strong><br>
                    This tool allows you to import SQL files directly from the installer to your database,
                    eliminating the need to manually upload via phpMyAdmin.
                </div>

                <div class="alert alert-warning">
                    <strong>⚠️ Important:</strong><br>
                    • Ensure you have a backup of your database before importing<br>
                    • Test the database connection first<br>
                    • Large SQL files may take time to process
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Database Connection Test -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>🔌 Database Connection Test</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Test the database connection using current credentials from connectdb.php</p>

                <form method="post">
                    <input type="hidden" name="action" value="test_connection">
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-plug"></i> Test Connection
                    </button>
                </form>

                <?php if (!empty($connectionTest)): ?>
                    <div class="alert alert-<?php echo $connectionTest['success'] ? 'success' : 'danger'; ?> mt-3">
                        <?php echo $connectionTest['success'] ? '✅' : '❌'; ?>
                        <?php echo htmlspecialchars($connectionTest['message']); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>📊 Database Information</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Get current database information and table count</p>

                <form method="post">
                    <input type="hidden" name="action" value="get_db_info">
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-info-circle"></i> Get Database Info
                    </button>
                </form>

                <?php if (!empty($databaseInfo) && $databaseInfo['success']): ?>
                    <div class="alert alert-success mt-3">
                        <strong>Database:</strong> <?php echo htmlspecialchars($databaseInfo['database_name']); ?><br>
                        <strong>Tables:</strong> <?php echo $databaseInfo['table_count']; ?> tables
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- SQL File Cleanup -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🧹 SQL File Cleanup</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <strong>🎯 Recommended First Step:</strong><br>
                    Clean your SQL file before importing to fix encoding issues, HTML entities (&amp;), and malformed statements.
                    This creates a new cleaned file that imports more reliably.
                </div>

                <form method="post" class="row align-items-end">
                    <input type="hidden" name="action" value="clean_sql">

                    <div class="col-md-6">
                        <label for="clean_sql_file" class="form-label">Select SQL File to Clean</label>
                        <select class="form-control" id="clean_sql_file" name="sql_file" required>
                            <option value="">Choose SQL file to clean...</option>
                            <?php foreach ($availableFiles as $file): ?>
                                <?php if ($file['readable']): ?>
                                    <option value="<?php echo htmlspecialchars($file['name']); ?>">
                                        <?php echo htmlspecialchars($file['name']); ?>
                                        (<?php echo number_format($file['size']); ?> bytes)
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-6">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-broom"></i> Clean SQL File
                        </button>
                        <div class="mt-2">
                            <small class="text-muted">
                                Creates: filename_cleaned.sql with fixed encoding and structure
                            </small>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Advanced SQL Import -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🚀 Advanced SQL Import</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>🎯 Recommended for newsql.sql:</strong><br>
                    For importing the main database file (newsql.sql), enable both options below to prevent foreign key conflicts and duplicate data issues.
                </div>

                <form method="post" id="advancedImportForm">
                    <input type="hidden" name="action" value="import_sql">

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sql_file" class="form-label">Select SQL File</label>
                                <select class="form-control" id="sql_file" name="sql_file" required>
                                    <option value="">Choose SQL file to import...</option>
                                    <?php foreach ($availableFiles as $file): ?>
                                        <?php if ($file['readable']): ?>
                                            <option value="<?php echo htmlspecialchars($file['name']); ?>">
                                                <?php echo htmlspecialchars($file['name']); ?>
                                                (<?php echo number_format($file['size']); ?> bytes)
                                            </option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Import Options</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="disable_foreign_keys" name="disable_foreign_keys" checked>
                                    <label class="form-check-label" for="disable_foreign_keys">
                                        <strong>Disable Foreign Key Checks</strong><br>
                                        <small class="text-muted">Prevents foreign key constraint errors during import</small>
                                    </label>
                                </div>
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" id="truncate_tables" name="truncate_tables">
                                    <label class="form-check-label" for="truncate_tables">
                                        <strong>Clear Existing Data (Before Import)</strong><br>
                                        <small class="text-muted">Only clears tables that already exist: alerts, temp_transfer, transfer</small>
                                    </label>
                                </div>
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" id="clean_duplicates" name="clean_duplicates" checked>
                                    <label class="form-check-label" for="clean_duplicates">
                                        <strong>Remove Duplicate Data (After Import)</strong><br>
                                        <small class="text-muted">Recommended: Removes duplicate entries after successful import</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Import Action</label><br>
                                <button type="submit" class="btn btn-success btn-lg"
                                        onclick="return confirmAdvancedImport()">
                                    <i class="fas fa-rocket"></i> Import with Options
                                </button>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        This will execute all SQL statements with the selected options
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Available SQL Files (Quick Import) -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📁 Quick Import (Basic)</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Quick import without advanced options. Use the Advanced Import above for better results.</p>

                <?php if (!empty($availableFiles)): ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>File Name</th>
                                    <th>Size</th>
                                    <th>Modified</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($availableFiles as $file): ?>
                                    <tr>
                                        <td><code><?php echo htmlspecialchars($file['name']); ?></code></td>
                                        <td><?php echo number_format($file['size']); ?> bytes</td>
                                        <td><?php echo $file['modified']; ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $file['readable'] ? 'success' : 'danger'; ?>">
                                                <?php echo $file['readable'] ? 'Readable' : 'Not Readable'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($file['readable']): ?>
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="action" value="import_sql">
                                                    <input type="hidden" name="sql_file" value="<?php echo htmlspecialchars($file['name']); ?>">
                                                    <button type="submit" class="btn btn-outline-primary btn-sm"
                                                            onclick="return confirm('Basic import of <?php echo htmlspecialchars($file['name']); ?>? Consider using Advanced Import for better results.')">
                                                        <i class="fas fa-upload"></i> Basic Import
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <span class="text-muted">Cannot import</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <strong>⚠️ No SQL files found</strong><br>
                        Place your SQL files in the <code>sql/</code> directory to import them.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Cleanup Results -->
<?php if (!empty($cleanResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><?php echo $cleanResults['success'] ? '✅' : '❌'; ?> SQL Cleanup Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-<?php echo $cleanResults['success'] ? 'success' : 'danger'; ?>">
                    <strong><?php echo htmlspecialchars($cleanResults['message']); ?></strong>
                </div>

                <?php if ($cleanResults['success']): ?>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="card-title">📄 File Information</h6>
                                    <p class="mb-1"><strong>Original:</strong> <code><?php echo htmlspecialchars($cleanResults['original_file']); ?></code></p>
                                    <p class="mb-1"><strong>Cleaned:</strong> <code><?php echo htmlspecialchars($cleanResults['cleaned_file']); ?></code></p>
                                    <p class="mb-0"><strong>Status:</strong> <span class="badge bg-success">Ready for import</span></p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title">📊 Cleanup Statistics</h6>
                                    <p class="mb-1"><strong>Size:</strong> <?php echo number_format($cleanResults['original_size']); ?> → <?php echo number_format($cleanResults['cleaned_size']); ?> bytes</p>
                                    <p class="mb-1"><strong>Lines:</strong> <?php echo number_format($cleanResults['original_lines']); ?> → <?php echo number_format($cleanResults['cleaned_lines']); ?></p>
                                    <p class="mb-0"><strong>Reduction:</strong>
                                        <?php if ($cleanResults['size_reduction'] > 0): ?>
                                            <span class="text-success">-<?php echo number_format($cleanResults['size_reduction']); ?> bytes</span>
                                        <?php else: ?>
                                            <span class="text-info">No size reduction</span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-success mt-3">
                        <h6><i class="fas fa-lightbulb"></i> Next Steps:</h6>
                        <ol class="mb-0">
                            <li>Use the <strong>Advanced SQL Import</strong> section below</li>
                            <li>Select the cleaned file: <code><?php echo htmlspecialchars($cleanResults['cleaned_file']); ?></code></li>
                            <li>Enable recommended options and import</li>
                        </ol>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Import Results -->
<?php if (!empty($importResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><?php echo $importResults['success'] ? '✅' : '❌'; ?> Import Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-<?php echo $importResults['success'] ? 'success' : 'danger'; ?>">
                    <strong><?php echo htmlspecialchars($importResults['message']); ?></strong>

                    <?php if (isset($importResults['statements_executed'])): ?>
                        <br><small>Statements executed: <?php echo $importResults['statements_executed']; ?></small>
                    <?php endif; ?>
                </div>

                <?php if (isset($importResults['options_used']) && !empty($importResults['options_used'])): ?>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-cog"></i> Import Options Used:</h6>
                        <ul class="mb-0">
                            <?php if ($importResults['options_used']['disable_foreign_keys']): ?>
                                <li>✅ Foreign key checks were disabled during import</li>
                            <?php endif; ?>
                            <?php if ($importResults['options_used']['truncate_tables']): ?>
                                <li>✅ Existing tables were cleared before import (if they existed)</li>
                            <?php endif; ?>
                            <?php if ($importResults['options_used']['clean_duplicates']): ?>
                                <li>✅ Duplicate data was removed after import</li>
                            <?php endif; ?>
                            <?php if (!$importResults['options_used']['disable_foreign_keys'] && !$importResults['options_used']['truncate_tables'] && !$importResults['options_used']['clean_duplicates']): ?>
                                <li>ℹ️ No advanced options were used (basic import)</li>
                            <?php endif; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (!empty($importResults['errors'])): ?>
                    <h6>Errors encountered:</h6>
                    <div class="alert alert-danger">
                        <?php foreach ($importResults['errors'] as $index => $error): ?>
                            <div class="mb-3 p-3 border rounded bg-light">
                                <h6 class="text-danger mb-2">Error #<?php echo $index + 1; ?></h6>

                                <?php if (is_array($error)): ?>
                                    <p class="mb-1"><strong>Message:</strong> <?php echo htmlspecialchars($error['error']); ?></p>
                                    <p class="mb-1"><strong>Statement #:</strong> <?php echo $error['statement_number']; ?></p>
                                    <p class="mb-1"><strong>Statement Length:</strong> <?php echo $error['statement_length']; ?> characters</p>
                                    <p class="mb-0"><strong>Statement Preview:</strong></p>
                                    <code class="d-block p-2 bg-white border rounded small"><?php echo htmlspecialchars($error['statement_preview']); ?></code>
                                <?php else: ?>
                                    <p class="mb-0"><?php echo htmlspecialchars($error); ?></p>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Database Tables (if info available) -->
<?php if (!empty($databaseInfo) && $databaseInfo['success'] && !empty($databaseInfo['tables'])): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📋 Current Database Tables</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach (array_chunk($databaseInfo['tables'], 4) as $tableChunk): ?>
                        <div class="col-md-3">
                            <ul class="list-unstyled">
                                <?php foreach ($tableChunk as $table): ?>
                                    <li><code><?php echo htmlspecialchars($table); ?></code></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<script>
function confirmAdvancedImport() {
    const sqlFile = document.getElementById('sql_file').value;
    const disableForeignKeys = document.getElementById('disable_foreign_keys').checked;
    const truncateTables = document.getElementById('truncate_tables').checked;
    const cleanDuplicates = document.getElementById('clean_duplicates').checked;

    if (!sqlFile) {
        alert('Please select a SQL file to import.');
        return false;
    }

    let message = `Are you sure you want to import "${sqlFile}"?\n\n`;
    message += 'Selected options:\n';

    if (disableForeignKeys) {
        message += '✓ Foreign key checks will be disabled during import\n';
    }

    if (truncateTables) {
        message += '✓ Existing tables will be cleared before import (if they exist)\n';
    }

    if (cleanDuplicates) {
        message += '✓ Duplicate data will be removed after import\n';
    }

    if (!disableForeignKeys && !truncateTables && !cleanDuplicates) {
        message += '⚠️ No advanced options selected - this may cause conflicts\n';
    }

    message += '\nThis action cannot be undone. Continue?';

    return confirm(message);
}
</script>

<?php echo getPageFooter(); ?>
