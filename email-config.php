<?php
/**
 * Enhanced Email Configuration Replacer
 * Comprehensive email settings detection and replacement
 */

require_once 'includes/auth.php';
requireAuth();

/**
 * Test SMTP connection using PHPMailer or fallback methods
 */
function testSMTPConnection($config) {
    // Validate required fields first
    $required_fields = ['test_smtp_host', 'test_email_username', 'test_email_password', 'test_from_email', 'test_to_email'];
    foreach ($required_fields as $field) {
        if (empty($config[$field])) {
            return [
                'success' => false,
                'message' => "❌ Missing required field: " . str_replace('test_', '', $field)
            ];
        }
    }

    // Check if PHPMailer is available
    $phpmailer_paths = [
        '../../vendor/autoload.php',
        '../../../vendor/autoload.php',
        '../../../../vendor/autoload.php',
        __DIR__ . '/../../vendor/autoload.php'
    ];

    $phpmailer_loaded = false;
    foreach ($phpmailer_paths as $path) {
        if (file_exists($path)) {
            try {
                require_once $path;
                $phpmailer_loaded = true;
                break;
            } catch (Exception $e) {
                // Continue to next path
                continue;
            }
        }
    }

    // Try PHPMailer if available
    if ($phpmailer_loaded && class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        return testSMTPWithPHPMailer($config);
    }

    // Fallback to basic SMTP test
    return testSMTPBasic($config);
}

/**
 * Test SMTP using PHPMailer
 */
function testSMTPWithPHPMailer($config) {
    try {
        // Use PHPMailer classes
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = $config['test_smtp_host'] ?? '';
        $mail->SMTPAuth = true;
        $mail->Username = $config['test_email_username'] ?? '';
        $mail->Password = $config['test_email_password'] ?? '';
        $mail->SMTPSecure = \PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = intval($config['test_smtp_port'] ?? 587);
        $mail->Timeout = 10; // 10 second timeout

        // Enable verbose debug output (capture it)
        $mail->SMTPDebug = 0; // Disable debug for cleaner output

        // Recipients
        $mail->setFrom($config['test_from_email'] ?? '', 'SMTP Test');
        $mail->addAddress($config['test_to_email'] ?? '', 'Test Recipient');

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'SMTP Test from Banking System Installer';
        $mail->Body = '
            <h2>🏛️ SMTP Configuration Test</h2>
            <p>This is a test email sent from the Banking System Installer to verify SMTP configuration.</p>
            <hr>
            <p><strong>Test Details:</strong></p>
            <ul>
                <li><strong>SMTP Host:</strong> ' . htmlspecialchars($config['test_smtp_host'] ?? '') . '</li>
                <li><strong>SMTP Port:</strong> ' . htmlspecialchars($config['test_smtp_port'] ?? '') . '</li>
                <li><strong>Username:</strong> ' . htmlspecialchars($config['test_email_username'] ?? '') . '</li>
                <li><strong>From Email:</strong> ' . htmlspecialchars($config['test_from_email'] ?? '') . '</li>
                <li><strong>Test Time:</strong> ' . date('Y-m-d H:i:s') . '</li>
            </ul>
            <p>If you received this email, your SMTP configuration is working correctly!</p>
            <hr>
            <p><small>Sent from Banking System Installer</small></p>
        ';

        // Attempt to send
        $mail->send();

        return [
            'success' => true,
            'message' => '✅ SMTP test successful! Email sent to ' . htmlspecialchars($config['test_to_email'] ?? '') . '. Please check the recipient\'s inbox.'
        ];

    } catch (\PHPMailer\PHPMailer\Exception $e) {
        return [
            'success' => false,
            'message' => '❌ SMTP test failed (PHPMailer): ' . htmlspecialchars($e->getMessage())
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '❌ SMTP test failed: ' . htmlspecialchars($e->getMessage())
        ];
    }
}

/**
 * Basic SMTP connection test without PHPMailer
 */
function testSMTPBasic($config) {
    $host = $config['test_smtp_host'] ?? '';
    $port = intval($config['test_smtp_port'] ?? 587);
    $username = $config['test_email_username'] ?? '';
    $password = $config['test_email_password'] ?? '';

    // Test basic connection
    $socket = @fsockopen($host, $port, $errno, $errstr, 10);

    if (!$socket) {
        return [
            'success' => false,
            'message' => "❌ Cannot connect to SMTP server {$host}:{$port}. Error: {$errstr} ({$errno})"
        ];
    }

    // Read initial response
    $response = fgets($socket, 512);
    if (substr($response, 0, 3) !== '220') {
        fclose($socket);
        return [
            'success' => false,
            'message' => "❌ SMTP server did not respond correctly. Response: " . trim($response)
        ];
    }

    fclose($socket);

    return [
        'success' => true,
        'message' => "✅ Basic SMTP connection test successful! Connected to {$host}:{$port}. Note: PHPMailer not available for full email test. Install PHPMailer for complete testing."
    ];
}

class EnhancedEmailConfigReplacer {
    
    private $target_files = [
        '../../backend/class.admin.php',
        '../../backend/class.crud.php', 
        '../../backend/connectdb.php',
        '../../mod/controller.php',
        '../../mod/dbconnect.php',
        '../../mod/mailsms.php'
    ];
    
    /**
     * Detect all email configurations with detailed information
     */
    public function detectEmailConfigs() {
        $configs = [];
        
        foreach ($this->target_files as $file) {
            if (file_exists($file) && is_readable($file)) {
                $content = file_get_contents($file);
                $fileConfigs = $this->extractDetailedEmailConfig($content, $file);
                if (!empty($fileConfigs)) {
                    $configs[$file] = $fileConfigs;
                }
            }
        }
        
        return $configs;
    }
    
    /**
     * Extract detailed email configuration with line numbers and context
     */
    private function extractDetailedEmailConfig($content, $filepath) {
        $configs = [];
        $lines = explode("\n", $content);
        
        foreach ($lines as $lineNum => $line) {
            $lineNum++; // 1-based line numbers
            
            // SMTP Host
            if (preg_match('/\$mail->Host\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $configs['smtp_host'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line),
                    'pattern' => '$mail->Host'
                ];
            }
            
            // SMTP Port
            if (preg_match('/\$mail->Port\s*=\s*(\d+)/', $line, $matches)) {
                $configs['smtp_port'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line),
                    'pattern' => '$mail->Port'
                ];
            }
            
            // Email Username
            if (preg_match('/\$mail->Username\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $configs['email_username'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line),
                    'pattern' => '$mail->Username'
                ];
            }
            
            // Email Password
            if (preg_match('/\$mail->Password\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $configs['email_password'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line),
                    'pattern' => '$mail->Password'
                ];
            }
            
            // SetFrom Email
            if (preg_match('/\$mail->SetFrom\s*\(\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $configs['from_email'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line),
                    'pattern' => '$mail->SetFrom()'
                ];
            }
            
            // AddReplyTo Email
            if (preg_match('/\$mail->AddReplyTo\s*\(\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $configs['reply_to_email'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line),
                    'pattern' => '$mail->AddReplyTo()'
                ];
            }
            
            // Site Email
            if (preg_match('/\$site_email\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $configs['site_email'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line),
                    'pattern' => '$site_email'
                ];
            }
            
            // Domain
            if (preg_match('/\$domain\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $configs['domain'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line),
                    'pattern' => '$domain'
                ];
            }
            
            // Site URL
            if (preg_match('/\$site_url\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $configs['site_url'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line),
                    'pattern' => '$site_url'
                ];
            }
        }
        
        return $configs;
    }
    
    /**
     * Preview email configuration changes
     */
    public function previewChanges($newConfigs) {
        $preview = [];
        $totalChanges = 0;
        
        foreach ($this->target_files as $file) {
            if (!file_exists($file) || !is_readable($file)) continue;
            
            $content = file_get_contents($file);
            $fileChanges = [];
            $changeCount = 0;
            
            foreach ($newConfigs as $configType => $newValue) {
                if (empty($newValue)) continue;
                
                $pattern = $this->getReplacementPattern($configType);
                if ($pattern) {
                    $matches = [];
                    if (preg_match_all($pattern['search'], $content, $matches, PREG_OFFSET_CAPTURE)) {
                        foreach ($matches[0] as $match) {
                            $lineNum = substr_count(substr($content, 0, $match[1]), "\n") + 1;
                            $fileChanges[] = [
                                'type' => $configType,
                                'line' => $lineNum,
                                'old_value' => $match[0],
                                'new_value' => preg_replace($pattern['search'], $pattern['replace'], $match[0]),
                                'new_config' => $newValue
                            ];
                            $changeCount++;
                        }
                    }
                }
            }
            
            if ($changeCount > 0) {
                $preview[$file] = [
                    'changes' => $fileChanges,
                    'count' => $changeCount
                ];
                $totalChanges += $changeCount;
            }
        }
        
        return [
            'files' => $preview,
            'total_changes' => $totalChanges,
            'total_files' => count($preview)
        ];
    }
    
    /**
     * Get replacement pattern for configuration type
     */
    private function getReplacementPattern($configType) {
        $patterns = [
            'smtp_host' => [
                'search' => '/(\$mail->Host\s*=\s*["\'])[^"\']+(["\'])/',
                'replace' => '${1}' . '${NEW_VALUE}' . '${2}'
            ],
            'smtp_port' => [
                'search' => '/(\$mail->Port\s*=\s*)\d+/',
                'replace' => '${1}' . '${NEW_VALUE}'
            ],
            'email_username' => [
                'search' => '/(\$mail->Username\s*=\s*["\'])[^"\']+(["\'])/',
                'replace' => '${1}' . '${NEW_VALUE}' . '${2}'
            ],
            'email_password' => [
                'search' => '/(\$mail->Password\s*=\s*["\'])[^"\']+(["\'])/',
                'replace' => '${1}' . '${NEW_VALUE}' . '${2}'
            ],
            'from_email' => [
                'search' => '/(\$mail->SetFrom\s*\(\s*["\'])[^"\']+(["\'])/',
                'replace' => '${1}' . '${NEW_VALUE}' . '${2}'
            ],
            'reply_to_email' => [
                'search' => '/(\$mail->AddReplyTo\s*\(\s*["\'])[^"\']+(["\'])/',
                'replace' => '${1}' . '${NEW_VALUE}' . '${2}'
            ],
            'site_email' => [
                'search' => '/(\$site_email\s*=\s*["\'])[^"\']+(["\'])/',
                'replace' => '${1}' . '${NEW_VALUE}' . '${2}'
            ],
            'domain' => [
                'search' => '/(\$domain\s*=\s*["\'])[^"\']+(["\'])/',
                'replace' => '${1}' . '${NEW_VALUE}' . '${2}'
            ],
            'site_url' => [
                'search' => '/(\$site_url\s*=\s*["\'])[^"\']+(["\'])/',
                'replace' => '${1}' . '${NEW_VALUE}' . '${2}'
            ]
        ];
        
        return $patterns[$configType] ?? null;
    }

    /**
     * Execute email configuration replacement
     */
    public function executeReplacements($newConfigs) {
        $results = [];
        $backupTimestamp = date('Y-m-d-H-i-s');
        $totalChanges = 0;

        foreach ($this->target_files as $file) {
            if (!file_exists($file) || !is_writable($file)) continue;

            $content = file_get_contents($file);
            $originalContent = $content;
            $fileChanges = 0;

            foreach ($newConfigs as $configType => $newValue) {
                if (empty($newValue)) continue;

                $pattern = $this->getReplacementPattern($configType);
                if ($pattern) {
                    $replacement = str_replace('${NEW_VALUE}', $newValue, $pattern['replace']);
                    $content = preg_replace($pattern['search'], $replacement, $content, -1, $count);
                    $fileChanges += $count;
                }
            }

            if ($fileChanges > 0) {
                // Create backup
                $backupPath = createBackup($file);

                // Write new content
                file_put_contents($file, $content);

                $results[] = [
                    'file' => $file,
                    'changes' => $fileChanges,
                    'backup' => $backupPath,
                    'success' => true
                ];

                $totalChanges += $fileChanges;
            }
        }

        return [
            'results' => $results,
            'total_changes' => $totalChanges,
            'total_files' => count($results),
            'backup_timestamp' => $backupTimestamp
        ];
    }
}

// Initialize the email config replacer
$emailReplacer = new EnhancedEmailConfigReplacer();
$currentConfigs = $emailReplacer->detectEmailConfigs();

// Handle form submissions
$message = '';
$messageType = '';
$previewResults = [];
$replaceResults = [];

if ($_POST) {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'preview') {
            $newConfigs = array_filter([
                'smtp_host' => sanitizeInput($_POST['smtp_host'] ?? ''),
                'smtp_port' => sanitizeInput($_POST['smtp_port'] ?? ''),
                'email_username' => sanitizeInput($_POST['email_username'] ?? ''),
                'email_password' => $_POST['email_password'] ?? '',
                'from_email' => sanitizeInput($_POST['from_email'] ?? ''),
                'reply_to_email' => sanitizeInput($_POST['reply_to_email'] ?? ''),
                'site_email' => sanitizeInput($_POST['site_email'] ?? ''),
                'domain' => sanitizeInput($_POST['domain'] ?? ''),
                'site_url' => sanitizeInput($_POST['site_url'] ?? '')
            ]);
            
            if (!empty($newConfigs)) {
                $previewResults = $emailReplacer->previewChanges($newConfigs);
                $message = "🔍 Preview completed: Found {$previewResults['total_changes']} potential changes in {$previewResults['total_files']} files.";
                $messageType = 'info';
            } else {
                $message = "⚠️ Please fill in at least one field to preview changes.";
                $messageType = 'warning';
            }
        }
        elseif ($_POST['action'] === 'execute') {
            $newConfigs = array_filter([
                'smtp_host' => sanitizeInput($_POST['smtp_host'] ?? ''),
                'smtp_port' => sanitizeInput($_POST['smtp_port'] ?? ''),
                'email_username' => sanitizeInput($_POST['email_username'] ?? ''),
                'email_password' => $_POST['email_password'] ?? '',
                'from_email' => sanitizeInput($_POST['from_email'] ?? ''),
                'reply_to_email' => sanitizeInput($_POST['reply_to_email'] ?? ''),
                'site_email' => sanitizeInput($_POST['site_email'] ?? ''),
                'domain' => sanitizeInput($_POST['domain'] ?? ''),
                'site_url' => sanitizeInput($_POST['site_url'] ?? '')
            ]);

            if (!empty($newConfigs)) {
                $replaceResults = $emailReplacer->executeReplacements($newConfigs);
                $message = "✅ Email configuration updated successfully! Made {$replaceResults['total_changes']} changes in {$replaceResults['total_files']} files.";
                $messageType = 'success';

                // Refresh current configs
                $currentConfigs = $emailReplacer->detectEmailConfigs();

                logSecurityEvent('Email configuration updated via installer', 'INFO', [
                    'total_changes' => $replaceResults['total_changes'],
                    'files_modified' => $replaceResults['total_files'],
                    'configs_updated' => array_keys($newConfigs)
                ]);
            } else {
                $message = "⚠️ Please fill in at least one field to execute changes.";
                $messageType = 'warning';
            }
        }
        elseif ($_POST['action'] === 'test_smtp') {
            try {
                $testResult = testSMTPConnection($_POST);
                $message = $testResult['message'];
                $messageType = $testResult['success'] ? 'success' : 'danger';
            } catch (Exception $e) {
                $message = "❌ SMTP test failed with error: " . htmlspecialchars($e->getMessage());
                $messageType = 'danger';

                // Log the error for debugging
                logSecurityEvent('SMTP test error', 'ERROR', [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
        }
    }
}

// Get the most common values for pre-filling the form
$commonConfigs = [];
foreach (['smtp_host', 'smtp_port', 'email_username', 'email_password', 'from_email', 'reply_to_email', 'site_email', 'domain', 'site_url'] as $configType) {
    $values = [];
    foreach ($currentConfigs as $fileConfigs) {
        if (isset($fileConfigs[$configType])) {
            foreach ($fileConfigs[$configType] as $config) {
                $values[] = $config['value'];
            }
        }
    }
    // Get the most common value
    if (!empty($values)) {
        $valueCounts = array_count_values($values);
        arsort($valueCounts);
        $commonConfigs[$configType] = key($valueCounts);
    }
}

// Display the page
echo getPageHeader('Email Configuration', 'Detect and replace email settings across the banking system');
echo displayMessage($message, $messageType);
?>

<!-- Update Email Configuration Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>⚙️ Update Email Configuration</h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    Update email configurations across all system files. The form is pre-filled with currently detected values.
                    Only modify the fields you want to change.
                </p>

                <form method="post">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">📮 SMTP Settings</h6>

                            <div class="mb-3">
                                <label for="smtp_host" class="form-label">SMTP Host</label>
                                <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                       placeholder="e.g., smtp.gmail.com"
                                       value="<?php echo htmlspecialchars($_POST['smtp_host'] ?? $commonConfigs['smtp_host'] ?? ''); ?>">
                                <div class="form-text">SMTP server hostname</div>
                            </div>

                            <div class="mb-3">
                                <label for="smtp_port" class="form-label">SMTP Port</label>
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                       placeholder="e.g., 587"
                                       value="<?php echo htmlspecialchars($_POST['smtp_port'] ?? $commonConfigs['smtp_port'] ?? ''); ?>">
                                <div class="form-text">SMTP server port (587 for TLS, 465 for SSL)</div>
                            </div>

                            <div class="mb-3">
                                <label for="email_username" class="form-label">Email Username</label>
                                <input type="email" class="form-control" id="email_username" name="email_username"
                                       placeholder="e.g., <EMAIL>"
                                       value="<?php echo htmlspecialchars($_POST['email_username'] ?? $commonConfigs['email_username'] ?? ''); ?>">
                                <div class="form-text">SMTP authentication username</div>
                            </div>

                            <div class="mb-3">
                                <label for="email_password" class="form-label">Email Password</label>
                                <input type="password" class="form-control" id="email_password" name="email_password"
                                       placeholder="Enter email password"
                                       value="<?php echo htmlspecialchars($_POST['email_password'] ?? $commonConfigs['email_password'] ?? ''); ?>">
                                <div class="form-text">SMTP authentication password</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="text-success">📧 Email Addresses</h6>

                            <div class="mb-3">
                                <label for="from_email" class="form-label">From Email</label>
                                <input type="email" class="form-control" id="from_email" name="from_email"
                                       placeholder="e.g., <EMAIL>"
                                       value="<?php echo htmlspecialchars($_POST['from_email'] ?? $commonConfigs['from_email'] ?? ''); ?>">
                                <div class="form-text">Default sender email address</div>
                            </div>

                            <div class="mb-3">
                                <label for="reply_to_email" class="form-label">Reply-To Email</label>
                                <input type="email" class="form-control" id="reply_to_email" name="reply_to_email"
                                       placeholder="e.g., <EMAIL>"
                                       value="<?php echo htmlspecialchars($_POST['reply_to_email'] ?? $commonConfigs['reply_to_email'] ?? ''); ?>">
                                <div class="form-text">Reply-to email address</div>
                            </div>

                            <div class="mb-3">
                                <label for="site_email" class="form-label">Site Email</label>
                                <input type="email" class="form-control" id="site_email" name="site_email"
                                       placeholder="e.g., <EMAIL>"
                                       value="<?php echo htmlspecialchars($_POST['site_email'] ?? $commonConfigs['site_email'] ?? ''); ?>">
                                <div class="form-text">General site email address</div>
                            </div>

                            <div class="mb-3">
                                <label for="domain" class="form-label">Domain</label>
                                <input type="text" class="form-control" id="domain" name="domain"
                                       placeholder="e.g., yourbank.com"
                                       value="<?php echo htmlspecialchars($_POST['domain'] ?? $commonConfigs['domain'] ?? ''); ?>">
                                <div class="form-text">Primary domain name</div>
                            </div>

                            <div class="mb-3">
                                <label for="site_url" class="form-label">Site URL</label>
                                <input type="url" class="form-control" id="site_url" name="site_url"
                                       placeholder="e.g., https://yourbank.com"
                                       value="<?php echo htmlspecialchars($_POST['site_url'] ?? $commonConfigs['site_url'] ?? ''); ?>">
                                <div class="form-text">Complete site URL</div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <strong>💡 Selective Updates:</strong> Only fill in the fields you want to change. Empty fields will be ignored.
                        All changes will be backed up automatically.
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" name="action" value="preview" class="btn btn-info">
                            🔍 Preview Changes
                        </button>
                        <button type="submit" name="action" value="execute" class="btn btn-warning"
                                onclick="return confirm('This will update email configurations across multiple files. Backups will be created. Continue?')">
                            🔄 Execute Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Detected Email Configurations -->
<?php if (!empty($currentConfigs)): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>
                    <button class="btn btn-link p-0 text-decoration-none text-dark w-100 text-start d-flex justify-content-between align-items-center"
                            type="button" data-bs-toggle="collapse" data-bs-target="#detectedEmailConfigsCollapse"
                            aria-expanded="false" aria-controls="detectedEmailConfigsCollapse">
                        <span>🔍 Detected Email Configurations</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h5>
            </div>
            <div class="collapse" id="detectedEmailConfigsCollapse">
                <div class="card-body">
                <?php
                $configTypes = [
                    'smtp_host' => 'SMTP Host',
                    'smtp_port' => 'SMTP Port',
                    'email_username' => 'Email Username',
                    'email_password' => 'Email Password',
                    'from_email' => 'From Email',
                    'reply_to_email' => 'Reply-To Email',
                    'site_email' => 'Site Email',
                    'domain' => 'Domain',
                    'site_url' => 'Site URL'
                ];
                ?>

                <div class="row">
                    <?php foreach ($configTypes as $configKey => $configLabel): ?>
                        <?php
                        $foundConfigs = [];
                        foreach ($currentConfigs as $file => $fileConfigs) {
                            if (isset($fileConfigs[$configKey])) {
                                foreach ($fileConfigs[$configKey] as $config) {
                                    $foundConfigs[] = [
                                        'file' => $file,
                                        'config' => $config
                                    ];
                                }
                            }
                        }
                        ?>

                        <?php if (!empty($foundConfigs)): ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="file-item">
                                <h6 class="text-primary"><?php echo $configLabel; ?></h6>
                                <?php foreach ($foundConfigs as $found): ?>
                                    <div class="mb-2">
                                        <strong>Value:</strong>
                                        <code class="match-highlight"><?php echo htmlspecialchars($found['config']['value']); ?></code><br>
                                        <small class="text-muted">
                                            📁 <?php echo basename($found['file']); ?> (Line <?php echo $found['config']['line']; ?>)<br>
                                            Pattern: <code><?php echo htmlspecialchars($found['config']['pattern']); ?></code>
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>



<!-- SMTP Test Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📧 SMTP Connection Test</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Test your SMTP configuration by sending a test email. This uses PHPMailer to verify your email settings work correctly.</p>

                <form method="POST" id="smtpTestForm">
                    <input type="hidden" name="action" value="test_smtp">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_smtp_host" class="form-label">SMTP Host</label>
                                <input type="text" class="form-control" id="test_smtp_host" name="test_smtp_host"
                                       value="<?php echo htmlspecialchars($commonConfigs['smtp_host'] ?? ''); ?>"
                                       placeholder="smtp.hostinger.com" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_smtp_port" class="form-label">SMTP Port</label>
                                <input type="number" class="form-control" id="test_smtp_port" name="test_smtp_port"
                                       value="<?php echo htmlspecialchars($commonConfigs['smtp_port'] ?? '587'); ?>"
                                       placeholder="587" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_email_username" class="form-label">Email Username</label>
                                <input type="email" class="form-control" id="test_email_username" name="test_email_username"
                                       value="<?php echo htmlspecialchars($commonConfigs['email_username'] ?? ''); ?>"
                                       placeholder="<EMAIL>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_email_password" class="form-label">Email Password</label>
                                <input type="password" class="form-control" id="test_email_password" name="test_email_password"
                                       placeholder="Enter email password" required>
                                <div class="form-text">Password is not stored, only used for testing</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_from_email" class="form-label">From Email</label>
                                <input type="email" class="form-control" id="test_from_email" name="test_from_email"
                                       value="<?php echo htmlspecialchars($commonConfigs['from_email'] ?? $commonConfigs['email_username'] ?? ''); ?>"
                                       placeholder="<EMAIL>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="test_to_email" class="form-label">Test Recipient Email</label>
                                <input type="email" class="form-control" id="test_to_email" name="test_to_email"
                                       placeholder="<EMAIL>" required>
                                <div class="form-text">Email address to send the test email to</div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <strong>ℹ️ Test Details:</strong>
                        <ul class="mb-0">
                            <li>This will send a test email using the provided SMTP settings</li>
                            <li>The test email contains configuration details for verification</li>
                            <li>No passwords are stored - they're only used for the test</li>
                            <li>Requires PHPMailer to be installed (composer require phpmailer/phpmailer)</li>
                        </ul>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-paper-plane"></i> Send Test Email
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Preview Results -->
<?php if (!empty($previewResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔍 Preview Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>Changes Found:</strong> <?php echo $previewResults['total_changes']; ?> changes in <?php echo $previewResults['total_files']; ?> files
                </div>

                <div class="result-box">
                    <?php foreach ($previewResults['files'] as $file => $fileData): ?>
                        <div class="file-item">
                            <h6>📁 <?php echo basename($file); ?> <span class="badge bg-primary"><?php echo $fileData['count']; ?> changes</span></h6>
                            <small class="text-muted"><?php echo htmlspecialchars($file); ?></small>

                            <?php foreach ($fileData['changes'] as $change): ?>
                                <div class="mt-2 p-2 bg-light border-start border-warning border-3">
                                    <strong>Line <?php echo $change['line']; ?>:</strong> <?php echo ucwords(str_replace('_', ' ', $change['type'])); ?><br>
                                    <small>
                                        <span class="text-danger">Old:</span> <code><?php echo htmlspecialchars($change['old_value']); ?></code><br>
                                        <span class="text-success">New:</span> <code><?php echo htmlspecialchars($change['new_value']); ?></code>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Execution Results -->
<?php if (!empty($replaceResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>✅ Execution Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong>Changes Applied:</strong> <?php echo $replaceResults['total_changes']; ?> changes in <?php echo $replaceResults['total_files']; ?> files
                </div>

                <div class="result-box">
                    <?php foreach ($replaceResults['results'] as $result): ?>
                        <div class="file-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6>📁 <?php echo basename($result['file']); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($result['file']); ?></small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success"><?php echo $result['changes']; ?> changes</span><br>
                                    <?php if ($result['backup']): ?>
                                        <small class="text-muted">Backup: <?php echo basename($result['backup']); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="alert alert-info mt-3">
                    <strong>💡 Next Steps:</strong>
                    <ul class="mb-0">
                        <li>Test email functionality to ensure settings are correct</li>
                        <li>Check that all email notifications are working</li>
                        <li>Verify SMTP authentication with your email provider</li>
                        <li>Backup files are available for rollback if needed</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
/* Enhanced Collapse Arrow Styling for Email Config */
[data-bs-toggle="collapse"] .fas.fa-chevron-down {
    transition: transform 0.3s ease-in-out, color 0.2s ease;
    color: #007bff !important;
    font-size: 14px;
    font-weight: bold;
}

[data-bs-toggle="collapse"]:not(.collapsed) .fas.fa-chevron-down,
[data-bs-toggle="collapse"][aria-expanded="true"] .fas.fa-chevron-down {
    transform: rotate(180deg);
    color: #28a745 !important;
}

[data-bs-toggle="collapse"]:hover .fas.fa-chevron-down {
    color: #0056b3 !important;
}

[data-bs-toggle="collapse"]:not(.collapsed):hover .fas.fa-chevron-down,
[data-bs-toggle="collapse"][aria-expanded="true"]:hover .fas.fa-chevron-down {
    color: #1e7e34 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Email Config: Initializing form sync and collapsible elements...');

    // Auto-populate test form fields when main form fields change
    const mainFormFields = {
        'smtp_host': 'test_smtp_host',
        'smtp_port': 'test_smtp_port',
        'email_username': 'test_email_username',
        'from_email': 'test_from_email'
    };

    // Set up field synchronization
    Object.keys(mainFormFields).forEach(function(mainFieldId) {
        const mainField = document.getElementById(mainFieldId);
        const testField = document.getElementById(mainFormFields[mainFieldId]);

        if (mainField && testField) {
            mainField.addEventListener('input', function() {
                if (this.value.trim() !== '') {
                    testField.value = this.value;
                    console.log('Synced', mainFieldId, 'to', mainFormFields[mainFieldId], ':', this.value);
                }
            });
        }
    });

    // Handle collapsible elements
    const collapseButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
    collapseButtons.forEach(function(button) {
        const targetId = button.getAttribute('data-bs-target');
        const targetElement = document.querySelector(targetId);

        if (targetElement) {
            // Add event listeners for chevron rotation
            targetElement.addEventListener('show.bs.collapse', function() {
                console.log('Showing collapse:', targetId);
                const chevron = button.querySelector('.fa-chevron-down');
                if (chevron) {
                    chevron.style.transform = 'rotate(180deg)';
                    chevron.style.color = '#28a745';
                }
            });

            targetElement.addEventListener('hide.bs.collapse', function() {
                console.log('Hiding collapse:', targetId);
                const chevron = button.querySelector('.fa-chevron-down');
                if (chevron) {
                    chevron.style.transform = 'rotate(0deg)';
                    chevron.style.color = '#007bff';
                }
            });
        }
    });

    // SMTP Test Form validation
    const smtpTestForm = document.getElementById('smtpTestForm');
    if (smtpTestForm) {
        smtpTestForm.addEventListener('submit', function(e) {
            const requiredFields = ['test_smtp_host', 'test_smtp_port', 'test_email_username', 'test_email_password', 'test_from_email', 'test_to_email'];
            let allValid = true;

            requiredFields.forEach(function(fieldId) {
                const field = document.getElementById(fieldId);
                if (field && field.value.trim() === '') {
                    field.classList.add('is-invalid');
                    allValid = false;
                } else if (field) {
                    field.classList.remove('is-invalid');
                }
            });

            if (!allValid) {
                e.preventDefault();
                alert('Please fill in all required fields for the SMTP test.');
                return false;
            }

            if (!confirm('Send test email with the provided SMTP settings?')) {
                e.preventDefault();
                return false;
            }
        });
    }

    console.log('Email Config: Initialization complete');
});
</script>

<?php echo getPageFooter(); ?>

<?php include 'includes/footer.php'; ?>
