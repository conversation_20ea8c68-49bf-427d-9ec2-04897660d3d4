<?php
/**
 * Image Replacement Module
 * Replace branding images throughout the banking system
 */

require_once 'includes/auth.php';
requireAuth();

class ImageReplacer {
    
    private $image_directory = '../../icon/';
    private $target_images = [
        'logo.png' => 'Main Logo',
        'logodark.png' => 'Dark Logo',
        'favicon.png' => 'Favicon',
        'lbg.png' => 'Background Image',
        'apple-icon.png' => 'Apple Touch Icon'
    ];
    
    /**
     * Get current images information
     */
    public function getCurrentImages() {
        $images = [];
        
        foreach ($this->target_images as $filename => $description) {
            $filepath = $this->image_directory . $filename;
            
            if (file_exists($filepath)) {
                $images[$filename] = [
                    'path' => $filepath,
                    'description' => $description,
                    'size' => filesize($filepath),
                    'modified' => date('Y-m-d H:i:s', filemtime($filepath)),
                    'dimensions' => $this->getImageDimensions($filepath),
                    'exists' => true
                ];
            } else {
                $images[$filename] = [
                    'path' => $filepath,
                    'description' => $description,
                    'exists' => false
                ];
            }
        }
        
        return $images;
    }
    
    /**
     * Get image dimensions
     */
    private function getImageDimensions($filepath) {
        $info = getimagesize($filepath);
        if ($info) {
            return [
                'width' => $info[0],
                'height' => $info[1],
                'type' => $info['mime']
            ];
        }
        return null;
    }
    
    /**
     * Replace an image file
     */
    public function replaceImage($target_filename, $uploaded_file) {
        if (!isset($this->target_images[$target_filename])) {
            return [
                'success' => false,
                'message' => 'Invalid target image filename'
            ];
        }
        
        $target_path = $this->image_directory . $target_filename;
        
        // Validate uploaded file
        $validation = $this->validateUploadedImage($uploaded_file);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'message' => $validation['message']
            ];
        }
        
        // Create backup if original exists
        $backup_path = null;
        if (file_exists($target_path)) {
            $backup_path = createBackup($target_path);
        }
        
        // Move uploaded file to target location
        if (move_uploaded_file($uploaded_file['tmp_name'], $target_path)) {
            return [
                'success' => true,
                'message' => 'Image replaced successfully',
                'backup' => $backup_path,
                'new_dimensions' => $this->getImageDimensions($target_path)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to move uploaded file'
            ];
        }
    }
    
    /**
     * Validate uploaded image
     */
    private function validateUploadedImage($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return [
                'valid' => false,
                'message' => 'Upload error: ' . $this->getUploadErrorMessage($file['error'])
            ];
        }
        
        // Check file size (max 5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            return [
                'valid' => false,
                'message' => 'File too large. Maximum size is 5MB.'
            ];
        }
        
        // Check if it's a valid image
        $image_info = getimagesize($file['tmp_name']);
        if (!$image_info) {
            return [
                'valid' => false,
                'message' => 'Invalid image file.'
            ];
        }
        
        // Check allowed image types
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($image_info['mime'], $allowed_types)) {
            return [
                'valid' => false,
                'message' => 'Invalid image type. Allowed: JPEG, PNG, GIF, WebP.'
            ];
        }
        
        return [
            'valid' => true,
            'message' => 'Valid image file'
        ];
    }
    
    /**
     * Get upload error message
     */
    private function getUploadErrorMessage($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
    
    /**
     * Get target images list
     */
    public function getTargetImages() {
        return $this->target_images;
    }
}

// Initialize image replacer
$imageReplacer = new ImageReplacer();
$currentImages = $imageReplacer->getCurrentImages();

// Handle form submissions
$message = '';
$messageType = '';
$replaceResults = [];

if ($_FILES && isset($_POST['action']) && $_POST['action'] === 'replace_image') {
    $target_image = $_POST['target_image'] ?? '';
    
    if (!empty($target_image) && isset($_FILES['image_file'])) {
        $result = $imageReplacer->replaceImage($target_image, $_FILES['image_file']);
        
        if ($result['success']) {
            $message = "✅ Image '{$target_image}' replaced successfully!";
            $messageType = 'success';
            $replaceResults = $result;
            
            // Refresh current images
            $currentImages = $imageReplacer->getCurrentImages();
            
            logSecurityEvent('Image replacement completed via installer', 'INFO', [
                'target_image' => $target_image,
                'backup_created' => !empty($result['backup'])
            ]);
        } else {
            $message = "❌ Image replacement failed: " . $result['message'];
            $messageType = 'danger';
        }
    } else {
        $message = "⚠️ Please select an image file and target.";
        $messageType = 'warning';
    }
}

// Display the page
echo getPageHeader('Image Replacement', 'Replace branding images throughout the banking system');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🖼️ Image Replacement System</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Replace key branding images in the banking system. All images are located in the <code>/icon/</code> directory.</p>
            </div>
        </div>
    </div>
</div>

<!-- Current Images Display -->
<div class="row mt-4">
    <?php foreach ($currentImages as $filename => $info): ?>
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6><?php echo htmlspecialchars($info['description']); ?></h6>
                    <small><code><?php echo htmlspecialchars($filename); ?></code></small>
                </div>
                <div class="card-body">
                    <?php if ($info['exists']): ?>
                        <div class="text-center mb-3">
                            <img src="<?php echo htmlspecialchars($info['path']); ?>?v=<?php echo time(); ?>" 
                                 alt="<?php echo htmlspecialchars($filename); ?>" 
                                 style="max-width: 150px; max-height: 150px; border: 1px solid #dee2e6; border-radius: 8px;">
                        </div>
                        
                        <small class="text-muted">
                            <strong>Size:</strong> <?php echo number_format($info['size'] / 1024, 1); ?> KB<br>
                            <?php if ($info['dimensions']): ?>
                                <strong>Dimensions:</strong> <?php echo $info['dimensions']['width']; ?>×<?php echo $info['dimensions']['height']; ?>px<br>
                            <?php endif; ?>
                            <strong>Modified:</strong> <?php echo $info['modified']; ?>
                        </small>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <small>⚠️ File not found</small>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Upload Form -->
                    <form method="post" enctype="multipart/form-data" class="mt-3">
                        <input type="hidden" name="action" value="replace_image">
                        <input type="hidden" name="target_image" value="<?php echo htmlspecialchars($filename); ?>">
                        
                        <div class="mb-3">
                            <label class="form-label">Choose New Image</label>
                            <input type="file" name="image_file" accept="image/*" class="form-control form-control-sm" required>
                            <div class="form-text">Max size: 5MB. Formats: JPEG, PNG, GIF, WebP</div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-sm w-100"
                                onclick="return confirm('Replace <?php echo htmlspecialchars($filename); ?>? A backup will be created.')">
                            🔄 Replace <?php echo htmlspecialchars($filename); ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<!-- Replacement Results -->
<?php if (!empty($replaceResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>✅ Image Replacement Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong><?php echo htmlspecialchars($replaceResults['message']); ?></strong>
                </div>

                <div class="result-box">
                    <?php if (!empty($replaceResults['backup'])): ?>
                        <p><strong>Backup Created:</strong> <?php echo basename($replaceResults['backup']); ?></p>
                    <?php endif; ?>

                    <?php if (!empty($replaceResults['new_dimensions'])): ?>
                        <p><strong>New Image Dimensions:</strong>
                           <?php echo $replaceResults['new_dimensions']['width']; ?>×<?php echo $replaceResults['new_dimensions']['height']; ?>px</p>
                        <p><strong>Image Type:</strong> <?php echo $replaceResults['new_dimensions']['type']; ?></p>
                    <?php endif; ?>
                </div>

                <div class="alert alert-info mt-3">
                    <strong>💡 Next Steps:</strong>
                    <ul class="mb-0">
                        <li>Clear browser cache to see the new images</li>
                        <li>Check that the new images display correctly throughout the system</li>
                        <li>Verify that image dimensions work well with the existing layout</li>
                        <li>Backup files are available for rollback if needed</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Image Replacement Tips -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>💡 Image Replacement Guidelines</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>📏 Recommended Dimensions:</h6>
                        <ul>
                            <li><strong>logo.png:</strong> 200×60px (or similar ratio)</li>
                            <li><strong>logodark.png:</strong> Same as logo.png</li>
                            <li><strong>favicon.png:</strong> 32×32px or 64×64px</li>
                            <li><strong>apple-icon.png:</strong> 180×180px</li>
                            <li><strong>lbg.png:</strong> 1920×1080px (background)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🎨 Best Practices:</h6>
                        <ul>
                            <li>Use PNG format for logos with transparency</li>
                            <li>Keep file sizes under 1MB for better performance</li>
                            <li>Maintain consistent branding across all images</li>
                            <li>Test images on different screen sizes</li>
                            <li>Consider dark/light theme compatibility</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php echo getPageFooter(); ?>

<?php include 'includes/footer.php'; ?>
