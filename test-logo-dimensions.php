<?php
/**
 * Test Logo Dimension Controller
 * Test the logo dimension patterns with ps_access.php content
 */

require_once 'includes/auth.php';
requireAuth();

echo getPageHeader('Logo Dimension Test', 'Testing logo dimension patterns');

// Test content from ps_access.php
$test_content = '
<div style="border-bottom:1px solid #eee">
    <img src="$bk_domain/icon/logodark.png" width="380px" />
</div>

<div class="logo">
    <img src="icon/logodark.png" alt="logo" style="width: 40%; height: 40%;">
</div>

<img src="icon/logodark.png" alt="logo" style="width: 20%; height: 20%;">
';

// Test the pattern matching
function testLogoDimensionPatterns($content) {
    $matches = [];
    $lines = explode("\n", $content);
    
    foreach ($lines as $lineNum => $line) {
        $lineNum++; // 1-based line numbers
        
        // Pattern 1: <img src="*logo*" style="width: X%; height: Y%;">
        if (preg_match('/(<img[^>]*logo[^>]*style=["\'][^"\']*width:\s*)(\d+%)[^"\']*height:\s*(\d+%)([^"\']*["\'][^>]*>)/', $line, $matches_found)) {
            $matches[] = [
                'line' => $lineNum,
                'type' => 'inline_style_percentage',
                'width' => $matches_found[2],
                'height' => $matches_found[3],
                'context' => trim($line),
                'full_match' => $matches_found[0]
            ];
        }
        
        // Pattern 2: <img src="*logo*" width="Xpx" />
        if (preg_match('/(<img[^>]*logo[^>]*width=["\'])(\d+(?:px)?)["\']/', $line, $matches_found)) {
            $matches[] = [
                'line' => $lineNum,
                'type' => 'width_only',
                'width' => $matches_found[2],
                'height' => 'auto',
                'context' => trim($line),
                'full_match' => $matches_found[0]
            ];
        }
    }
    
    return $matches;
}

// Test replacement function
function testReplacement($content, $newWidth, $newHeight, $unit = '%') {
    $originalContent = $content;
    
    // Pattern 1: Replace inline style percentages
    $content = preg_replace(
        '/(<img[^>]*logo[^>]*style=["\'][^"\']*width:\s*)\d+%([^"\']*height:\s*)\d+%([^"\']*["\'][^>]*>)/',
        '${1}' . $newWidth . $unit . '${2}' . $newHeight . $unit . '${3}',
        $content,
        -1,
        $count1
    );
    
    // Pattern 2: Replace width-only attributes (for pixels)
    if ($unit === 'px') {
        $content = preg_replace(
            '/(<img[^>]*logo[^>]*width=["\'])\d+(?:px)?(["\'])/',
            '${1}' . $newWidth . '${2}',
            $content,
            -1,
            $count2
        );
    } else {
        $count2 = 0;
    }
    
    return [
        'original' => $originalContent,
        'modified' => $content,
        'changes' => $count1 + $count2
    ];
}

$detected = testLogoDimensionPatterns($test_content);
$replacement_test = testReplacement($test_content, 30, 30, '%');

?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🧪 Logo Dimension Pattern Test</h5>
            </div>
            <div class="card-body">
                
                <h6>📝 Test Content (from ps_access.php):</h6>
                <pre class="bg-light p-3 rounded"><code><?php echo htmlspecialchars($test_content); ?></code></pre>
                
                <h6 class="mt-4">🔍 Detected Patterns:</h6>
                <?php if (!empty($detected)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Line</th>
                                    <th>Type</th>
                                    <th>Width</th>
                                    <th>Height</th>
                                    <th>Context</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($detected as $match): ?>
                                    <tr>
                                        <td><?php echo $match['line']; ?></td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo str_replace('_', ' ', $match['type']); ?>
                                            </span>
                                        </td>
                                        <td><code><?php echo htmlspecialchars($match['width']); ?></code></td>
                                        <td><code><?php echo htmlspecialchars($match['height']); ?></code></td>
                                        <td><code><?php echo htmlspecialchars($match['context']); ?></code></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="alert alert-success">
                        <strong>✅ Pattern Detection Working!</strong><br>
                        Found <?php echo count($detected); ?> logo dimension patterns.
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <strong>⚠️ No patterns detected!</strong> Check the regex patterns.
                    </div>
                <?php endif; ?>
                
                <h6 class="mt-4">🔄 Replacement Test (30% × 30%):</h6>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Before:</h6>
                        <pre class="bg-light p-3 rounded"><code><?php echo htmlspecialchars($replacement_test['original']); ?></code></pre>
                    </div>
                    <div class="col-md-6">
                        <h6>After:</h6>
                        <pre class="bg-success p-3 rounded text-white"><code><?php echo htmlspecialchars($replacement_test['modified']); ?></code></pre>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <strong>📊 Replacement Summary:</strong><br>
                    Changes made: <?php echo $replacement_test['changes']; ?><br>
                    Status: <?php echo $replacement_test['changes'] > 0 ? '✅ Working' : '❌ No changes'; ?>
                </div>
                
                <h6 class="mt-4">🎯 Your Specific Case:</h6>
                <div class="alert alert-primary">
                    <p><strong>Current in ps_access.php:</strong></p>
                    <code>&lt;img src="icon/logodark.png" alt="logo" style="width: 20%; height: 20%;"&gt;</code>
                    
                    <p class="mt-2"><strong>Will be detected as:</strong></p>
                    <ul class="mb-0">
                        <li>Type: inline_style_percentage</li>
                        <li>Width: 20%</li>
                        <li>Height: 20%</li>
                    </ul>
                    
                    <p class="mt-2"><strong>Can be changed to any dimensions like:</strong></p>
                    <ul class="mb-0">
                        <li>30% × 30% for larger logo</li>
                        <li>15% × 15% for smaller logo</li>
                        <li>200px × 80px for fixed size</li>
                    </ul>
                </div>
                
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="logo-dimension-controller.php" class="btn btn-primary">
            <i class="fas fa-expand-arrows-alt"></i> Use Logo Dimension Controller
        </a>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php echo getPageFooter(); ?>
