<?php
/**
 * Security Audit Tool
 * Comprehensive security analysis of the installer system
 */

require_once 'includes/auth.php';
requireAuth();

class SecurityAudit {
    
    private $path_manager;
    private $installer_dir;
    private $vulnerabilities = [];
    private $recommendations = [];
    private $security_score = 100;
    
    public function __construct() {
        $this->path_manager = getPathManager();
        $this->installer_dir = $this->path_manager->getBankingDir() . '/mod/installer/';
    }
    
    /**
     * Run comprehensive security audit
     */
    public function runAudit() {
        $this->checkFilePermissions();
        $this->checkPasswordSecurity();
        $this->checkSessionSecurity();
        $this->checkInputValidation();
        $this->checkFileUploadSecurity();
        $this->checkDirectoryTraversal();
        $this->checkSQLInjection();
        $this->checkXSSSecurity();
        $this->checkCSRFProtection();
        $this->checkHTTPSUsage();
        $this->checkErrorHandling();
        $this->checkLoggingSecurity();
        
        return [
            'vulnerabilities' => $this->vulnerabilities,
            'recommendations' => $this->recommendations,
            'security_score' => max(0, $this->security_score),
            'risk_level' => $this->getRiskLevel()
        ];
    }
    
    /**
     * Check file permissions
     */
    private function checkFilePermissions() {
        $critical_files = [
            'includes/auth.php',
            'includes/credentials.json',
            'index.php',
            'database.php'
        ];
        
        foreach ($critical_files as $file) {
            $filepath = $this->installer_dir . $file;
            if (file_exists($filepath)) {
                $perms = fileperms($filepath);
                $octal = substr(sprintf('%o', $perms), -4);
                
                if ($octal > '0644') {
                    $this->addVulnerability('File Permissions', "File $file has overly permissive permissions ($octal)", 'Medium');
                    $this->security_score -= 5;
                }
            }
        }
        
        // Check if installer directory is web accessible
        if (is_dir($this->installer_dir)) {
            $htaccess = $this->installer_dir . '.htaccess';
            if (!file_exists($htaccess)) {
                $this->addVulnerability('Directory Access', 'No .htaccess file to restrict installer access', 'High');
                $this->security_score -= 15;
                $this->addRecommendation('Create .htaccess file to restrict installer directory access');
            }
        }
    }
    
    /**
     * Check password security
     */
    private function checkPasswordSecurity() {
        $password = getInstallerPassword();
        
        // Check password strength
        if (strlen($password) < 12) {
            $this->addVulnerability('Password Security', 'Installer password is too short (< 12 characters)', 'High');
            $this->security_score -= 20;
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $this->addVulnerability('Password Security', 'Password lacks uppercase letters', 'Medium');
            $this->security_score -= 5;
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $this->addVulnerability('Password Security', 'Password lacks lowercase letters', 'Medium');
            $this->security_score -= 5;
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $this->addVulnerability('Password Security', 'Password lacks numbers', 'Medium');
            $this->security_score -= 5;
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $this->addVulnerability('Password Security', 'Password lacks special characters', 'Medium');
            $this->security_score -= 5;
        }
        
        // Check for common passwords
        $common_passwords = ['admin', 'password', '123456', 'installer', 'bank'];
        foreach ($common_passwords as $common) {
            if (stripos($password, $common) !== false) {
                $this->addVulnerability('Password Security', 'Password contains common words', 'High');
                $this->security_score -= 15;
                break;
            }
        }
    }
    
    /**
     * Check session security
     */
    private function checkSessionSecurity() {
        // Check session configuration
        if (!ini_get('session.cookie_httponly')) {
            $this->addVulnerability('Session Security', 'Session cookies not set to HTTP-only', 'Medium');
            $this->security_score -= 10;
        }
        
        if (!ini_get('session.cookie_secure') && isset($_SERVER['HTTPS'])) {
            $this->addVulnerability('Session Security', 'Session cookies not set to secure', 'Medium');
            $this->security_score -= 10;
        }
        
        // Check session timeout
        if (!isset($_SESSION['installer_login_time'])) {
            $this->addVulnerability('Session Security', 'No session timeout mechanism', 'Medium');
            $this->security_score -= 10;
        }
    }
    
    /**
     * Check input validation
     */
    private function checkInputValidation() {
        $files_to_check = glob($this->installer_dir . '*.php');
        
        foreach ($files_to_check as $file) {
            $content = file_get_contents($file);
            
            // Check for direct $_POST usage without sanitization
            if (preg_match('/\$_POST\[[^\]]+\](?!\s*\?\?\s*[\'"]|\s*\?\?\s*\w+|\s*\?\?\s*\d+)(?!.*sanitize)/i', $content)) {
                $this->addVulnerability('Input Validation', "Potential unsanitized input in " . basename($file), 'Medium');
                $this->security_score -= 5;
            }
            
            // Check for direct $_GET usage
            if (preg_match('/\$_GET\[[^\]]+\](?!.*sanitize)/i', $content)) {
                $this->addVulnerability('Input Validation', "Potential unsanitized GET input in " . basename($file), 'Medium');
                $this->security_score -= 5;
            }
        }
    }
    
    /**
     * Check file upload security
     */
    private function checkFileUploadSecurity() {
        $upload_files = ['media-replacement.php', 'login-background-controller.php'];
        
        foreach ($upload_files as $file) {
            $filepath = $this->installer_dir . $file;
            if (file_exists($filepath)) {
                $content = file_get_contents($filepath);
                
                // Check for file type validation
                if (!preg_match('/mime.*type.*check|file.*type.*validation/i', $content)) {
                    $this->addVulnerability('File Upload', "Missing file type validation in $file", 'High');
                    $this->security_score -= 15;
                }
                
                // Check for file size limits
                if (!preg_match('/size.*limit|max.*size/i', $content)) {
                    $this->addVulnerability('File Upload', "Missing file size limits in $file", 'Medium');
                    $this->security_score -= 10;
                }
            }
        }
    }
    
    /**
     * Check for directory traversal vulnerabilities
     */
    private function checkDirectoryTraversal() {
        $files_to_check = glob($this->installer_dir . '*.php');
        
        foreach ($files_to_check as $file) {
            $content = file_get_contents($file);
            
            // Check for potential directory traversal
            if (preg_match('/file_get_contents\(\$_[GET|POST]/i', $content) || 
                preg_match('/include\(\$_[GET|POST]/i', $content) ||
                preg_match('/require\(\$_[GET|POST]/i', $content)) {
                $this->addVulnerability('Directory Traversal', "Potential directory traversal in " . basename($file), 'High');
                $this->security_score -= 20;
            }
        }
    }
    
    /**
     * Check SQL injection protection
     */
    private function checkSQLInjection() {
        $files_to_check = glob($this->installer_dir . '*.php');
        
        foreach ($files_to_check as $file) {
            $content = file_get_contents($file);
            
            // Check for direct SQL queries with user input
            if (preg_match('/query.*\$_[GET|POST]|mysql.*\$_[GET|POST]/i', $content)) {
                $this->addVulnerability('SQL Injection', "Potential SQL injection in " . basename($file), 'Critical');
                $this->security_score -= 25;
            }
        }
    }
    
    /**
     * Check XSS protection
     */
    private function checkXSSSecurity() {
        $files_to_check = glob($this->installer_dir . '*.php');
        
        foreach ($files_to_check as $file) {
            $content = file_get_contents($file);
            
            // Check for unescaped output
            if (preg_match('/echo\s+\$_[GET|POST]|print\s+\$_[GET|POST]/i', $content)) {
                $this->addVulnerability('XSS', "Potential XSS vulnerability in " . basename($file), 'High');
                $this->security_score -= 15;
            }
        }
    }
    
    /**
     * Check CSRF protection
     */
    private function checkCSRFProtection() {
        $files_to_check = glob($this->installer_dir . '*.php');
        $csrf_protected = false;
        
        foreach ($files_to_check as $file) {
            $content = file_get_contents($file);
            
            if (preg_match('/csrf.*token|_token/i', $content)) {
                $csrf_protected = true;
                break;
            }
        }
        
        if (!$csrf_protected) {
            $this->addVulnerability('CSRF', 'No CSRF protection implemented', 'Medium');
            $this->security_score -= 10;
            $this->addRecommendation('Implement CSRF token protection for forms');
        }
    }
    
    /**
     * Check HTTPS usage
     */
    private function checkHTTPSUsage() {
        if (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] !== 'on') {
            $this->addVulnerability('HTTPS', 'Installer not using HTTPS', 'High');
            $this->security_score -= 15;
            $this->addRecommendation('Use HTTPS for all installer communications');
        }
    }
    
    /**
     * Check error handling
     */
    private function checkErrorHandling() {
        if (ini_get('display_errors')) {
            $this->addVulnerability('Error Handling', 'Error display is enabled', 'Medium');
            $this->security_score -= 10;
            $this->addRecommendation('Disable error display in production');
        }
    }
    
    /**
     * Check logging security
     */
    private function checkLoggingSecurity() {
        $log_dir = $this->installer_dir . 'includes/logs/';
        if (is_dir($log_dir)) {
            $htaccess = $log_dir . '.htaccess';
            if (!file_exists($htaccess)) {
                $this->addVulnerability('Logging Security', 'Log directory not protected', 'Medium');
                $this->security_score -= 10;
                $this->addRecommendation('Protect log directory with .htaccess');
            }
        }
    }
    
    /**
     * Add vulnerability to list
     */
    private function addVulnerability($category, $description, $severity) {
        $this->vulnerabilities[] = [
            'category' => $category,
            'description' => $description,
            'severity' => $severity,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Add recommendation to list
     */
    private function addRecommendation($recommendation) {
        $this->recommendations[] = $recommendation;
    }
    
    /**
     * Get risk level based on security score
     */
    private function getRiskLevel() {
        if ($this->security_score >= 90) return 'Low';
        if ($this->security_score >= 70) return 'Medium';
        if ($this->security_score >= 50) return 'High';
        return 'Critical';
    }
}

// Initialize audit
$securityAudit = new SecurityAudit();

// Run audit if requested
$auditResults = null;
if (isset($_GET['run_audit']) || isset($_POST['run_audit'])) {
    $auditResults = $securityAudit->runAudit();
}

// Display the page
echo getPageHeader('Security Audit', 'Comprehensive security analysis of the installer system');
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔒 Security Audit Tool</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Comprehensive security analysis of the installer system to identify vulnerabilities and security risks.</p>

                <?php if (!$auditResults): ?>
                <div class="text-center">
                    <form method="post" action="">
                        <button type="submit" name="run_audit" class="btn btn-primary btn-lg">
                            <i class="fas fa-shield-alt"></i> Run Security Audit
                        </button>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($auditResults): ?>
<!-- Security Score -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card border-<?php echo $auditResults['risk_level'] === 'Low' ? 'success' : ($auditResults['risk_level'] === 'Medium' ? 'warning' : 'danger'); ?>">
            <div class="card-body text-center">
                <h2 class="display-4 <?php echo $auditResults['risk_level'] === 'Low' ? 'text-success' : ($auditResults['risk_level'] === 'Medium' ? 'text-warning' : 'text-danger'); ?>">
                    <?php echo $auditResults['security_score']; ?>%
                </h2>
                <h5>Security Score</h5>
                <span class="badge bg-<?php echo $auditResults['risk_level'] === 'Low' ? 'success' : ($auditResults['risk_level'] === 'Medium' ? 'warning' : 'danger'); ?>">
                    <?php echo $auditResults['risk_level']; ?> Risk
                </span>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h2 class="display-4 text-danger"><?php echo count($auditResults['vulnerabilities']); ?></h2>
                <h5>Vulnerabilities Found</h5>
                <small class="text-muted">Issues requiring attention</small>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h2 class="display-4 text-info"><?php echo count($auditResults['recommendations']); ?></h2>
                <h5>Recommendations</h5>
                <small class="text-muted">Security improvements</small>
            </div>
        </div>
    </div>
</div>

<!-- Vulnerabilities -->
<?php if (!empty($auditResults['vulnerabilities'])): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">🚨 Security Vulnerabilities</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Description</th>
                                <th>Severity</th>
                                <th>Detected</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($auditResults['vulnerabilities'] as $vuln): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($vuln['category']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($vuln['description']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php
                                            echo $vuln['severity'] === 'Critical' ? 'danger' :
                                                ($vuln['severity'] === 'High' ? 'warning' :
                                                ($vuln['severity'] === 'Medium' ? 'info' : 'secondary'));
                                        ?>">
                                            <?php echo htmlspecialchars($vuln['severity']); ?>
                                        </span>
                                    </td>
                                    <td><small><?php echo htmlspecialchars($vuln['timestamp']); ?></small></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Recommendations -->
<?php if (!empty($auditResults['recommendations'])): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">💡 Security Recommendations</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <?php foreach ($auditResults['recommendations'] as $recommendation): ?>
                        <li class="list-group-item">
                            <i class="fas fa-lightbulb text-warning me-2"></i>
                            <?php echo htmlspecialchars($recommendation); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Security Best Practices -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">✅ Security Best Practices</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-lock"></i> Authentication Security</h6>
                        <ul class="small">
                            <li>Use strong passwords (12+ characters)</li>
                            <li>Include uppercase, lowercase, numbers, symbols</li>
                            <li>Avoid common words or patterns</li>
                            <li>Enable session timeouts</li>
                            <li>Use HTTPS for all communications</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-shield-alt"></i> File Security</h6>
                        <ul class="small">
                            <li>Set proper file permissions (644 for files, 755 for directories)</li>
                            <li>Protect sensitive directories with .htaccess</li>
                            <li>Validate all file uploads</li>
                            <li>Limit file sizes and types</li>
                            <li>Store uploads outside web root when possible</li>
                        </ul>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6><i class="fas fa-code"></i> Input Validation</h6>
                        <ul class="small">
                            <li>Sanitize all user inputs</li>
                            <li>Use prepared statements for SQL queries</li>
                            <li>Escape output to prevent XSS</li>
                            <li>Validate file paths to prevent directory traversal</li>
                            <li>Implement CSRF protection</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-trash"></i> Post-Installation</h6>
                        <ul class="small">
                            <li><strong style="color: #dc3545;">Delete installer folder immediately after use</strong></li>
                            <li>Remove any temporary files</li>
                            <li>Clear installation logs</li>
                            <li>Change default passwords</li>
                            <li>Review server access logs</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Re-run Audit -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <form method="post" action="">
            <button type="submit" name="run_audit" class="btn btn-outline-primary">
                <i class="fas fa-redo"></i> Re-run Security Audit
            </button>
        </form>
    </div>
</div>
<?php endif; ?>

<?php echo getPageFooter(); ?>
