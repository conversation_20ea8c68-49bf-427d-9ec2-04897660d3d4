<?php
/**
 * Test Portable Installer
 * Test installer functionality from different directory locations
 */

require_once 'includes/auth.php';
requireAuth();

echo getPageHeader('Portable Installer Test', 'Testing installer flexibility across different directory locations');

// Get path manager instance
$pathManager = getPathManager();
$installationInfo = $pathManager->getInstallationInfo();

?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🧪 Portable Installer Test Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>ℹ️ About Portable Installer:</strong><br>
                    This installer is now location-agnostic and can work from any directory. 
                    It automatically detects its environment and adjusts all paths accordingly.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Installation Detection -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📍 Installation Location Detection</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <tr>
                            <td><strong>Installer Directory:</strong></td>
                            <td><code><?php echo htmlspecialchars($installationInfo['installer_directory']); ?></code></td>
                        </tr>
                        <tr>
                            <td><strong>Root Directory:</strong></td>
                            <td><code><?php echo htmlspecialchars($installationInfo['root_directory']); ?></code></td>
                        </tr>
                        <tr>
                            <td><strong>Banking Directory:</strong></td>
                            <td><code><?php echo htmlspecialchars($installationInfo['banking_directory']); ?></code></td>
                        </tr>
                        <tr>
                            <td><strong>Verifying Permission Directory:</strong></td>
                            <td><code><?php echo htmlspecialchars($installationInfo['verifying_permission_directory']); ?></code></td>
                        </tr>
                        <tr>
                            <td><strong>Location Description:</strong></td>
                            <td>
                                <span class="badge bg-primary">
                                    <?php echo $pathManager->getLocationDescription(); ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Is Installer in Root:</strong></td>
                            <td>
                                <span class="badge bg-<?php echo $pathManager->isInstallerInRoot() ? 'success' : 'info'; ?>">
                                    <?php echo $pathManager->isInstallerInRoot() ? 'Yes' : 'No'; ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File Detection -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📁 File & Directory Detection</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Path</th>
                                <th>Status</th>
                                <th>Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>banking.zip</strong></td>
                                <td><code><?php echo htmlspecialchars($installationInfo['zip_file_path']); ?></code></td>
                                <td>
                                    <span class="badge bg-<?php echo $installationInfo['zip_file_exists'] ? 'success' : 'warning'; ?>">
                                        <?php echo $installationInfo['zip_file_exists'] ? 'Found' : 'Missing'; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($installationInfo['zip_file_exists']): ?>
                                        <?php echo number_format(filesize($installationInfo['zip_file_path'])); ?> bytes
                                    <?php else: ?>
                                        <span class="text-muted">Not available</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>banking folder</strong></td>
                                <td><code><?php echo htmlspecialchars($installationInfo['banking_directory']); ?></code></td>
                                <td>
                                    <span class="badge bg-<?php echo $installationInfo['banking_exists'] ? 'success' : 'warning'; ?>">
                                        <?php echo $installationInfo['banking_exists'] ? 'Found' : 'Missing'; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($installationInfo['banking_exists']): ?>
                                        Directory exists
                                    <?php else: ?>
                                        <span class="text-muted">Not found</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>verifying-permission folder</strong></td>
                                <td><code><?php echo htmlspecialchars($installationInfo['verifying_permission_directory']); ?></code></td>
                                <td>
                                    <span class="badge bg-<?php echo $installationInfo['verifying_permission_exists'] ? 'success' : 'warning'; ?>">
                                        <?php echo $installationInfo['verifying_permission_exists'] ? 'Found' : 'Missing'; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($installationInfo['verifying_permission_exists']): ?>
                                        Directory exists
                                    <?php else: ?>
                                        <span class="text-muted">Not found</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Relative Paths -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔗 Dynamic Path Calculation</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Target</th>
                                <th>Relative Path from Installer</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Root Directory</strong></td>
                                <td><code><?php echo htmlspecialchars($installationInfo['relative_paths']['to_root']); ?></code></td>
                                <td>
                                    <span class="badge bg-success">Calculated</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Banking Directory</strong></td>
                                <td><code><?php echo htmlspecialchars($installationInfo['relative_paths']['to_banking']); ?></code></td>
                                <td>
                                    <span class="badge bg-success">Calculated</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Verifying Permission Directory</strong></td>
                                <td><code><?php echo htmlspecialchars($installationInfo['relative_paths']['to_verifying_permission']); ?></code></td>
                                <td>
                                    <span class="badge bg-success">Calculated</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Module Path Testing -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔧 Module Path Testing</h5>
            </div>
            <div class="card-body">
                
                <h6>📄 Text Replacement Scan Directories:</h6>
                <div class="alert alert-secondary">
                    <?php
                    $scanDirs = $pathManager->getScanDirectories();
                    foreach ($scanDirs as $dir):
                    ?>
                        <code><?php echo htmlspecialchars($dir); ?></code><br>
                    <?php endforeach; ?>
                </div>
                
                <h6 class="mt-4">🖼️ Media Directory:</h6>
                <div class="alert alert-secondary">
                    <code><?php echo htmlspecialchars($pathManager->getMediaDirectory()); ?></code>
                </div>
                
                <h6 class="mt-4">🗄️ Database Connection File:</h6>
                <div class="alert alert-secondary">
                    <code><?php echo htmlspecialchars($pathManager->getDatabaseConnectionFile()); ?></code>
                    <?php if (file_exists($pathManager->getDatabaseConnectionFile())): ?>
                        <span class="badge bg-success ms-2">Found</span>
                    <?php else: ?>
                        <span class="badge bg-warning ms-2">Missing</span>
                    <?php endif; ?>
                </div>
                
                <h6 class="mt-4">⭐ Favicon Locations:</h6>
                <div class="alert alert-secondary">
                    <?php
                    $faviconLocations = $pathManager->getFaviconLocations();
                    foreach ($faviconLocations as $path => $description):
                    ?>
                        <strong><?php echo htmlspecialchars($description); ?>:</strong><br>
                        <code><?php echo htmlspecialchars($path); ?></code>
                        <?php if (file_exists($path)): ?>
                            <span class="badge bg-success">Found</span>
                        <?php else: ?>
                            <span class="badge bg-warning">Missing</span>
                        <?php endif; ?>
                        <br><br>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Deployment Scenarios -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🚀 Deployment Scenarios</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6>✅ Supported Deployment Scenarios:</h6>
                    <ul class="mb-0">
                        <li><strong>Root Directory:</strong> Place installer directly in root folder with banking.zip</li>
                        <li><strong>Subdirectory:</strong> Keep installer in banking/mod/installer/ (current structure)</li>
                        <li><strong>Custom Location:</strong> Place installer anywhere, it will auto-detect root</li>
                        <li><strong>Portable Package:</strong> Copy installer folder to any location</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h6>📋 Current Configuration:</h6>
                    <p><strong>Installer Location:</strong> <?php echo $pathManager->getLocationDescription(); ?></p>
                    <p><strong>Root Detection:</strong> 
                        <?php if ($installationInfo['zip_file_exists'] || $installationInfo['banking_exists']): ?>
                            ✅ Successfully detected root directory
                        <?php else: ?>
                            ⚠️ Root directory detection may need verification
                        <?php endif; ?>
                    </p>
                    <p class="mb-0"><strong>Ready for Use:</strong> 
                        <?php if ($installationInfo['zip_file_exists'] || $installationInfo['banking_exists']): ?>
                            ✅ Yes - All paths calculated correctly
                        <?php else: ?>
                            ⚠️ Verify banking.zip or banking folder exists
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Instructions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📖 Usage Instructions</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-primary">
                    <h6>🎯 How to Use the Portable Installer:</h6>
                    
                    <h6 class="mt-3">Option 1: Root Directory Deployment</h6>
                    <ol>
                        <li>Copy the entire <code>installer</code> folder to your root directory</li>
                        <li>Ensure <code>banking.zip</code> is in the same root directory</li>
                        <li>Access via: <code>yoursite.com/installer/</code></li>
                        <li>The installer will find banking.zip in the same directory</li>
                    </ol>
                    
                    <h6 class="mt-3">Option 2: Keep Current Structure</h6>
                    <ol>
                        <li>Keep installer in <code>banking/mod/installer/</code></li>
                        <li>Ensure <code>banking.zip</code> is in the root directory</li>
                        <li>Access via: <code>yoursite.com/banking/mod/installer/</code></li>
                        <li>The installer will automatically find the root directory</li>
                    </ol>
                    
                    <h6 class="mt-3">Option 3: Custom Location</h6>
                    <ol>
                        <li>Place installer folder anywhere in your directory structure</li>
                        <li>The installer will search up to 5 levels to find the root directory</li>
                        <li>Root is identified by the presence of banking.zip or banking folder</li>
                        <li>All paths will be calculated automatically</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="index.php" class="btn btn-primary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php echo getPageFooter(); ?>
