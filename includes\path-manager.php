<?php
/**
 * Path Manager
 * Automatically detects installer location and provides dynamic paths
 */

class PathManager {
    
    private static $instance = null;
    private $installer_dir;
    private $root_dir;
    private $banking_dir;
    private $verifying_permission_dir;
    private $paths_cache = [];
    
    private function __construct() {
        $this->detectPaths();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Automatically detect installer location and set up paths
     */
    private function detectPaths() {
        // Get current installer directory
        $this->installer_dir = dirname(__DIR__);
        
        // Try to find root directory by looking for banking.zip or banking folder
        $this->root_dir = $this->findRootDirectory();
        
        // Set up other important directories
        $this->banking_dir = $this->root_dir . '/banking';
        $this->verifying_permission_dir = $this->root_dir . '/verifying-permission';
    }
    
    /**
     * Find the root directory by looking for key files/folders
     */
    private function findRootDirectory() {
        $current_dir = $this->installer_dir;
        $max_levels = 5; // Prevent infinite loops
        $level = 0;
        
        while ($level < $max_levels) {
            // Check if we're at root level (has banking.zip or banking folder)
            if ($this->isRootDirectory($current_dir)) {
                return $current_dir;
            }
            
            // Go up one level
            $parent_dir = dirname($current_dir);
            
            // If we can't go up anymore, break
            if ($parent_dir === $current_dir) {
                break;
            }
            
            $current_dir = $parent_dir;
            $level++;
        }
        
        // If not found, assume installer is in root
        return $this->installer_dir;
    }
    
    /**
     * Check if directory is the root directory
     */
    private function isRootDirectory($dir) {
        // Look for key indicators of root directory
        $indicators = [
            'banking.zip',
            'banking',
            'verifying-permission'
        ];
        
        foreach ($indicators as $indicator) {
            if (file_exists($dir . '/' . $indicator)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get installer directory path
     */
    public function getInstallerDir() {
        return $this->installer_dir;
    }
    
    /**
     * Get root directory path
     */
    public function getRootDir() {
        return $this->root_dir;
    }
    
    /**
     * Get banking directory path
     */
    public function getBankingDir() {
        return $this->banking_dir;
    }
    
    /**
     * Get verifying-permission directory path
     */
    public function getVerifyingPermissionDir() {
        return $this->verifying_permission_dir;
    }
    
    /**
     * Get relative path from installer to target
     */
    public function getRelativePath($target_dir) {
        $installer_path = realpath($this->installer_dir);
        $target_path = realpath($target_dir);
        
        if (!$installer_path || !$target_path) {
            // Fallback to simple calculation
            return $this->calculateRelativePath($this->installer_dir, $target_dir);
        }
        
        return $this->calculateRelativePath($installer_path, $target_path);
    }
    
    /**
     * Calculate relative path between two directories
     */
    private function calculateRelativePath($from, $to) {
        $from = rtrim(str_replace('\\', '/', $from), '/');
        $to = rtrim(str_replace('\\', '/', $to), '/');
        
        $from_parts = explode('/', $from);
        $to_parts = explode('/', $to);
        
        // Find common path
        $common_length = 0;
        $min_length = min(count($from_parts), count($to_parts));
        
        for ($i = 0; $i < $min_length; $i++) {
            if ($from_parts[$i] === $to_parts[$i]) {
                $common_length++;
            } else {
                break;
            }
        }
        
        // Build relative path
        $relative_parts = [];
        
        // Add ../ for each directory we need to go up
        for ($i = $common_length; $i < count($from_parts); $i++) {
            $relative_parts[] = '..';
        }
        
        // Add the remaining path to target
        for ($i = $common_length; $i < count($to_parts); $i++) {
            $relative_parts[] = $to_parts[$i];
        }
        
        return empty($relative_parts) ? '.' : implode('/', $relative_parts);
    }
    
    /**
     * Get scan directories for text replacement, logo controller, etc.
     */
    public function getScanDirectories() {
        $relative_root = $this->getRelativePath($this->root_dir);
        $relative_banking = $this->getRelativePath($this->banking_dir);
        $relative_verifying = $this->getRelativePath($this->verifying_permission_dir);
        
        return [
            $relative_root . '/',
            $relative_banking . '/',
            $relative_banking . '/backend/',
            $relative_banking . '/heads/',
            $relative_banking . '/mod/',
            $relative_banking . '/assets/',
            $relative_banking . '/SQL/',
            $relative_verifying . '/',
        ];
    }
    
    /**
     * Get media directory path
     */
    public function getMediaDirectory() {
        $relative_banking = $this->getRelativePath($this->banking_dir);
        return $relative_banking . '/icon/';
    }
    
    /**
     * Get database connection file path
     */
    public function getDatabaseConnectionFile() {
        $relative_banking = $this->getRelativePath($this->banking_dir);
        return $relative_banking . '/backend/connectdb.php';
    }
    
    /**
     * Get ZIP file path
     */
    public function getZipFilePath() {
        return $this->root_dir . '/banking.zip';
    }
    
    /**
     * Get ZIP extraction path
     */
    public function getZipExtractionPath() {
        return $this->root_dir . '/';
    }
    
    /**
     * Get favicon locations
     */
    public function getFaviconLocations() {
        $relative_banking = $this->getRelativePath($this->banking_dir);
        $relative_verifying = $this->getRelativePath($this->verifying_permission_dir);
        
        return [
            $relative_banking . '/icon/favicon.png' => 'Main Icon Directory',
            $relative_verifying . '/favicon.png' => 'Verifying Permission Directory',
            $relative_banking . '/assets/img/favicon.png' => 'Assets Directory (if exists)',
            $relative_banking . '/heads/favicon.png' => 'Heads Directory (if exists)',
        ];
    }
    
    /**
     * Get installation info for debugging
     */
    public function getInstallationInfo() {
        return [
            'installer_directory' => $this->installer_dir,
            'root_directory' => $this->root_dir,
            'banking_directory' => $this->banking_dir,
            'verifying_permission_directory' => $this->verifying_permission_dir,
            'zip_file_path' => $this->getZipFilePath(),
            'zip_file_exists' => file_exists($this->getZipFilePath()),
            'banking_exists' => is_dir($this->banking_dir),
            'verifying_permission_exists' => is_dir($this->verifying_permission_dir),
            'relative_paths' => [
                'to_root' => $this->getRelativePath($this->root_dir),
                'to_banking' => $this->getRelativePath($this->banking_dir),
                'to_verifying_permission' => $this->getRelativePath($this->verifying_permission_dir)
            ]
        ];
    }
    
    /**
     * Check if installer is in root directory
     */
    public function isInstallerInRoot() {
        return realpath($this->installer_dir) === realpath($this->root_dir);
    }
    
    /**
     * Get installer location description
     */
    public function getLocationDescription() {
        if ($this->isInstallerInRoot()) {
            return 'Root Directory';
        }
        
        $relative_path = $this->getRelativePath($this->root_dir);
        $levels_up = substr_count($relative_path, '../');
        
        if ($levels_up === 0) {
            return 'Root Directory';
        } elseif ($levels_up === 1) {
            return 'One Level Deep';
        } elseif ($levels_up === 2) {
            return 'Two Levels Deep (banking/mod/installer)';
        } else {
            return $levels_up . ' Levels Deep';
        }
    }
}

// Global function to get path manager instance
function getPathManager() {
    return PathManager::getInstance();
}

// Global function to get dynamic paths
function getDynamicPath($type) {
    $pm = getPathManager();
    
    switch ($type) {
        case 'root':
            return $pm->getRootDir();
        case 'banking':
            return $pm->getBankingDir();
        case 'verifying-permission':
            return $pm->getVerifyingPermissionDir();
        case 'installer':
            return $pm->getInstallerDir();
        case 'zip':
            return $pm->getZipFilePath();
        case 'media':
            return $pm->getMediaDirectory();
        case 'database':
            return $pm->getDatabaseConnectionFile();
        default:
            return null;
    }
}
?>
