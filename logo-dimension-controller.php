<?php
/**
 * Logo Dimension Controller
 * Control logo width and height across all system files
 */

require_once 'includes/auth.php';
requireAuth();

class LogoDimensionController {

    private $path_manager;
    private $media_directory;
    private $web_media_directory;

    public function __construct() {
        $this->path_manager = getPathManager();
        $this->media_directory = $this->path_manager->getBankingDir() . '/icon/';
        $this->web_media_directory = $this->path_manager->getMediaDirectory();
    }

    /**
     * Get logo information
     */
    public function getLogoInfo() {
        $logoInfo = [];
        $logoFiles = ['logo.png', 'logodark.png', 'favicon.png', 'favicon.ico'];

        foreach ($logoFiles as $filename) {
            $filepath = $this->media_directory . $filename;
            $webPath = $this->web_media_directory . $filename;

            $logoInfo[$filename] = [
                'exists' => file_exists($filepath),
                'path' => $filepath,
                'web_path' => $webPath,
                'size' => file_exists($filepath) ? filesize($filepath) : 0,
                'modified' => file_exists($filepath) ? date('Y-m-d H:i:s', filemtime($filepath)) : null
            ];
        }

        return $logoInfo;
    }

    /**
     * Get current logo dimensions from ps_access.php
     */
    public function getCurrentDimensions() {
        $psAccessPath = $this->path_manager->getBankingDir() . '/ps_access.php';

        if (!file_exists($psAccessPath)) {
            return ['width' => '40%', 'height' => '40%']; // Default
        }

        $content = file_get_contents($psAccessPath);

        // Look for logo dimensions in ps_access.php
        if (preg_match('/style="width:\s*(\d+%)[^"]*height:\s*(\d+%)[^"]*"/', $content, $matches)) {
            return [
                'width' => $matches[1],
                'height' => $matches[2]
            ];
        }

        return ['width' => '40%', 'height' => '40%']; // Default
    }

    /**
     * Scan for logo dimensions across files
     */
    public function scanLogoDimensions() {
        $results = [];
        $targetFiles = [
            'ps_access.php' => 'Main login page',
            'index.php' => 'Main index page',
            'dashboard.php' => 'Dashboard page',
            'login.php' => 'Login page'
        ];

        foreach ($targetFiles as $filename => $description) {
            $filepath = $this->path_manager->getBankingDir() . '/' . $filename;

            if (file_exists($filepath)) {
                $content = file_get_contents($filepath);
                $matches = $this->findLogoDimensions($content, $filepath);

                $results[] = [
                    'file' => $filename,
                    'description' => $description,
                    'path' => $filepath,
                    'matches' => $matches
                ];
            }
        }

        return $results;
    }

    /**
     * Update logo dimensions
     */
    public function updateDimensions($width, $height) {
        $results = [];
        $psAccessPath = $this->path_manager->getBankingDir() . '/ps_access.php';

        if (!file_exists($psAccessPath)) {
            return ['success' => false, 'message' => 'ps_access.php not found'];
        }

        // Create backup
        $backupPath = $psAccessPath . '.backup.' . date('Y-m-d-H-i-s');
        copy($psAccessPath, $backupPath);

        $content = file_get_contents($psAccessPath);
        $originalContent = $content;

        // Update logo dimensions
        $content = preg_replace(
            '/(style="[^"]*width:\s*)\d+%([^"]*height:\s*)\d+%([^"]*")/',
            '${1}' . $width . '%${2}' . $height . '%${3}',
            $content
        );

        if ($content !== $originalContent) {
            file_put_contents($psAccessPath, $content);

            return [
                'success' => true,
                'message' => 'Logo dimensions updated successfully',
                'file' => 'ps_access.php',
                'backup' => $backupPath,
                'changes' => 1
            ];
        }

        return ['success' => false, 'message' => 'No logo dimensions found to update'];
    }

    /**
     * Find logo dimensions in content
     */
    private function findLogoDimensions($content, $filepath) {
        $matches = [];
        $lines = explode("\n", $content);

        foreach ($lines as $lineNum => $line) {
            $lineNum++; // 1-based line numbers

            // Look for logo references with dimensions
            if (preg_match('/logo.*style="[^"]*width:\s*(\d+%)[^"]*height:\s*(\d+%)[^"]*"/', $line, $found)) {
                $matches[] = [
                    'line' => $lineNum,
                    'type' => 'inline_style_percentage',
                    'width' => $found[1],
                    'height' => $found[2],
                    'content' => trim($line)
                ];
            }
        }

        return $matches;
    }
}

// Initialize controller
$logoController = new LogoDimensionController();

// Handle form submissions
$message = '';
$messageType = '';
$scanResults = [];
$updateResults = [];

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'scan':
                $scanResults = $logoController->scanLogoDimensions();
                $totalMatches = array_sum(array_map(function($result) {
                    return count($result['matches']);
                }, $scanResults));

                $message = "Scan completed: Found {$totalMatches} logo dimension patterns in " . count($scanResults) . " files.";
                $messageType = 'info';
                break;

            case 'update':
                $width = sanitizeInput($_POST['width'] ?? '');
                $height = sanitizeInput($_POST['height'] ?? '');

                if (!empty($width) && !empty($height)) {
                    $result = $logoController->updateDimensions($width, $height);

                    if ($result['success']) {
                        $message = $result['message'];
                        $messageType = 'success';
                        $updateResults = [$result];

                        logSecurityEvent('Logo dimensions updated via installer', 'INFO', [
                            'new_width' => $width . '%',
                            'new_height' => $height . '%',
                            'file' => $result['file']
                        ]);
                    } else {
                        $message = $result['message'];
                        $messageType = 'warning';
                    }
                } else {
                    $message = "Please provide both width and height values.";
                    $messageType = 'warning';
                }
                break;
        }
    }
}

// Get current data
$logoInfo = $logoController->getLogoInfo();
$currentDimensions = $logoController->getCurrentDimensions();

// Display the page
echo getPageHeader('Logo Dimension Controller', 'Control logo width and height across all system files');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📐 Logo Dimension Controller</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Control logo width and height across all system files. This tool finds and replaces logo dimensions in HTML img tags.</p>
            </div>
        </div>
    </div>
</div>

<!-- Current Logo Preview -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>🖼️ Current Logo in ps_access.php</h5>
            </div>
            <div class="card-body text-center">
                <?php if ($logoInfo['logodark.png']['exists']): ?>
                    <div class="mb-3">
                        <img src="image-proxy.php?file=logodark.png&v=<?php echo time(); ?>"
                             alt="Current Logo"
                             class="img-fluid border rounded p-2"
                             style="width: <?php echo htmlspecialchars($currentDimensions['width']); ?>; height: <?php echo htmlspecialchars($currentDimensions['height']); ?>; max-width: 300px;">
                    </div>
                    <div class="alert alert-info small">
                        <strong>Current Dimensions:</strong> <?php echo htmlspecialchars($currentDimensions['width']); ?> x <?php echo htmlspecialchars($currentDimensions['height']); ?><br>
                        <strong>File:</strong> icon/logodark.png<br>
                        <strong>Size:</strong> <?php echo number_format($logoInfo['logodark.png']['size'] / 1024, 1); ?> KB
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i><br>
                        Logo file not found<br>
                        <small>Expected: icon/logodark.png</small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>📐 Dimension Control</h5>
            </div>
            <div class="card-body">
                <form method="post" action="">
                    <input type="hidden" name="action" value="update">

                    <div class="row">
                        <div class="col-6">
                            <label for="width" class="form-label">Width</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="width" name="width"
                                       value="<?php echo htmlspecialchars(str_replace('%', '', $currentDimensions['width'])); ?>"
                                       placeholder="40" required>
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <label for="height" class="form-label">Height</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="height" name="height"
                                       value="<?php echo htmlspecialchars(str_replace('%', '', $currentDimensions['height'])); ?>"
                                       placeholder="40" required>
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Update Logo Dimensions
                        </button>
                    </div>
                </form>

                <div class="mt-3">
                    <small class="text-muted">
                        <strong>Quick Options:</strong><br>
                        <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="setDimensions('30', '30')">30%</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="setDimensions('40', '40')">40%</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="setDimensions('50', '50')">50%</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="setDimensions('60', '60')">60%</button>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scan Files Section -->
<div class="row mt-4">
    <div class="col">
        <div class="card">
            <div class="card-header">
                <h5>🔍 Scan System Files</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Scan key system files to find logo references and current dimensions.</p>

                <form method="post" action="" class="d-inline">
                    <input type="hidden" name="action" value="scan">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Scan Files
                    </button>
                </form>

                <?php if (!empty($scanResults)): ?>
                <div class="mt-4">
                    <h6>Scan Results:</h6>
                    <?php foreach ($scanResults as $result): ?>
                        <div class="card mb-3 <?php echo $result['file'] === 'ps_access.php' ? 'border-primary' : ''; ?>">
                            <div class="card-body">
                                <h6>
                                    <i class="fas fa-file-code"></i>
                                    <?php echo htmlspecialchars($result['file']); ?>
                                    <?php if ($result['file'] === 'ps_access.php'): ?>
                                        <span class="badge bg-primary ms-2">Main Login Page</span>
                                    <?php endif; ?>
                                </h6>
                                <small class="text-muted"><?php echo htmlspecialchars($result['description']); ?></small>

                                <?php if (!empty($result['matches'])): ?>
                                    <div class="table-responsive mt-3">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Line</th>
                                                    <th>Type</th>
                                                    <th>Width</th>
                                                    <th>Height</th>
                                                    <th>Context</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($result['matches'] as $match): ?>
                                                    <tr>
                                                        <td><?php echo $match['line']; ?></td>
                                                        <td>
                                                            <span class="badge bg-info">
                                                                <?php echo htmlspecialchars($match['type']); ?>
                                                            </span>
                                                        </td>
                                                        <td><code><?php echo htmlspecialchars($match['width']); ?></code></td>
                                                        <td><code><?php echo htmlspecialchars($match['height']); ?></code></td>
                                                        <td><code><?php echo htmlspecialchars(substr($match['content'], 0, 60)) . '...'; ?></code></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info small mt-2">
                                        No logo dimensions found in this file.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Update Results -->
<?php if (!empty($updateResults)): ?>
<div class="row mt-4">
    <div class="col">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">✅ Update Results</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Description</th>
                                <th>Changes</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($updateResults as $result): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($result['file']); ?></td>
                                    <td><?php echo htmlspecialchars($result['message']); ?></td>
                                    <td><?php echo $result['changes']; ?></td>
                                    <td>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Updated
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function setDimensions(width, height) {
    document.getElementById('width').value = width;
    document.getElementById('height').value = height;
}
</script>

<?php echo getPageFooter(); ?>