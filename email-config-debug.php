<?php
/**
 * Email Config Debug Script
 * Helps diagnose HTTP 500 errors in email-config.php
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Email Config Debug</h1>";

try {
    echo "<h2>1. Testing Basic PHP Functionality</h2>";
    echo "✅ PHP is working<br>";
    echo "PHP Version: " . PHP_VERSION . "<br>";
    echo "Current time: " . date('Y-m-d H:i:s') . "<br><br>";

    echo "<h2>2. Testing File Includes</h2>";
    
    // Test auth include
    if (file_exists('includes/auth.php')) {
        echo "✅ includes/auth.php exists<br>";
        try {
            require_once 'includes/auth.php';
            echo "✅ includes/auth.php loaded successfully<br>";
        } catch (Exception $e) {
            echo "❌ Error loading includes/auth.php: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ includes/auth.php not found<br>";
    }

    echo "<br><h2>3. Testing PHPMailer Availability</h2>";
    
    $phpmailer_paths = [
        '../../vendor/autoload.php',
        '../../../vendor/autoload.php',
        '../../../../vendor/autoload.php',
        __DIR__ . '/../../vendor/autoload.php'
    ];

    $phpmailer_found = false;
    foreach ($phpmailer_paths as $path) {
        if (file_exists($path)) {
            echo "✅ Found autoload.php at: $path<br>";
            try {
                require_once $path;
                $phpmailer_found = true;
                echo "✅ Autoload loaded successfully<br>";
                break;
            } catch (Exception $e) {
                echo "❌ Error loading autoload from $path: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ Not found: $path<br>";
        }
    }

    if ($phpmailer_found) {
        if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
            echo "✅ PHPMailer class is available<br>";
        } else {
            echo "❌ PHPMailer class not found after loading autoload<br>";
        }
    } else {
        echo "⚠️ PHPMailer not found - will use fallback SMTP test<br>";
    }

    echo "<br><h2>4. Testing SMTP Connection Function</h2>";
    
    // Test the SMTP connection function with dummy data
    $test_config = [
        'test_smtp_host' => 'smtp.gmail.com',
        'test_smtp_port' => '587',
        'test_email_username' => '<EMAIL>',
        'test_email_password' => 'dummy_password',
        'test_from_email' => '<EMAIL>',
        'test_to_email' => '<EMAIL>'
    ];

    // Define the test functions if they don't exist
    if (!function_exists('testSMTPConnection')) {
        function testSMTPConnection($config) {
            return testSMTPBasic($config);
        }
    }

    if (!function_exists('testSMTPBasic')) {
        function testSMTPBasic($config) {
            $host = $config['test_smtp_host'] ?? '';
            $port = intval($config['test_smtp_port'] ?? 587);
            
            $socket = @fsockopen($host, $port, $errno, $errstr, 5);
            
            if (!$socket) {
                return [
                    'success' => false,
                    'message' => "Cannot connect to SMTP server {$host}:{$port}. Error: {$errstr} ({$errno})"
                ];
            }

            $response = fgets($socket, 512);
            fclose($socket);
            
            return [
                'success' => true,
                'message' => "Basic SMTP connection test successful! Connected to {$host}:{$port}"
            ];
        }
    }

    try {
        $result = testSMTPConnection($test_config);
        echo "✅ SMTP test function works: " . $result['message'] . "<br>";
    } catch (Exception $e) {
        echo "❌ SMTP test function error: " . $e->getMessage() . "<br>";
    }

    echo "<br><h2>5. Testing Form Processing Logic</h2>";
    
    // Simulate POST data
    $_POST = [
        'action' => 'test_smtp',
        'test_smtp_host' => 'smtp.gmail.com',
        'test_smtp_port' => '587',
        'test_email_username' => '<EMAIL>',
        'test_email_password' => 'dummy_password',
        'test_from_email' => '<EMAIL>',
        'test_to_email' => '<EMAIL>'
    ];

    if ($_POST['action'] === 'test_smtp') {
        try {
            $testResult = testSMTPConnection($_POST);
            echo "✅ Form processing works: " . $testResult['message'] . "<br>";
        } catch (Exception $e) {
            echo "❌ Form processing error: " . $e->getMessage() . "<br>";
        }
    }

    echo "<br><h2>6. Server Environment</h2>";
    echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
    echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "<br>";
    echo "Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'Unknown') . "<br>";
    echo "Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'Unknown') . "<br>";

    echo "<br><h2>7. Memory and Limits</h2>";
    echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
    echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
    echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";

    echo "<br><h2>✅ Debug Complete</h2>";
    echo "<p>If you see this message, the basic functionality is working. The HTTP 500 error might be caused by:</p>";
    echo "<ul>";
    echo "<li>Missing PHPMailer dependency (install with: composer require phpmailer/phpmailer)</li>";
    echo "<li>Authentication issues in includes/auth.php</li>";
    echo "<li>Memory or execution time limits</li>";
    echo "<li>Server configuration issues</li>";
    echo "</ul>";

    echo "<p><a href='email-config.php'>Try Email Config Again</a> | <a href='index.php'>Back to Dashboard</a></p>";

} catch (Exception $e) {
    echo "<h2>❌ Fatal Error Caught</h2>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
