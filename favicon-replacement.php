<?php
/**
 * Favicon Replacement Module
 * Replace favicon across all directories including verifying-permission
 */

require_once 'includes/auth.php';
requireAuth();

class FaviconReplacer {

    private $favicon_locations;
    private $path_manager;

    public function __construct() {
        $this->path_manager = getPathManager();
        $this->favicon_locations = $this->path_manager->getFaviconLocations();
    }
    
    /**
     * Get current favicon information
     */
    public function getCurrentFavicons() {
        $favicons = [];
        
        foreach ($this->favicon_locations as $filepath => $description) {
            $favicons[] = [
                'path' => $filepath,
                'description' => $description,
                'exists' => file_exists($filepath),
                'size' => file_exists($filepath) ? filesize($filepath) : 0,
                'modified' => file_exists($filepath) ? date('Y-m-d H:i:s', filemtime($filepath)) : 'N/A',
                'dimensions' => file_exists($filepath) ? $this->getImageDimensions($filepath) : 'N/A'
            ];
        }
        
        return $favicons;
    }
    
    /**
     * Replace favicon in all locations
     */
    public function replaceFavicon($uploaded_file) {
        $results = [];
        $totalReplaced = 0;
        
        // Validate uploaded file
        $validation = $this->validateFaviconFile($uploaded_file);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'message' => $validation['message'],
                'results' => []
            ];
        }
        
        foreach ($this->favicon_locations as $filepath => $description) {
            $directory = dirname($filepath);
            
            // Create directory if it doesn't exist
            if (!is_dir($directory)) {
                if (!mkdir($directory, 0755, true)) {
                    $results[] = [
                        'path' => $filepath,
                        'description' => $description,
                        'success' => false,
                        'message' => 'Failed to create directory: ' . $directory
                    ];
                    continue;
                }
            }
            
            // Create backup if file exists
            $backupPath = null;
            if (file_exists($filepath)) {
                $backupPath = $filepath . '.backup.' . date('Y-m-d-H-i-s');
                copy($filepath, $backupPath);
            }
            
            // Copy new favicon
            if (copy($uploaded_file['tmp_name'], $filepath)) {
                $results[] = [
                    'path' => $filepath,
                    'description' => $description,
                    'success' => true,
                    'message' => 'Favicon replaced successfully',
                    'backup' => $backupPath
                ];
                $totalReplaced++;
            } else {
                $results[] = [
                    'path' => $filepath,
                    'description' => $description,
                    'success' => false,
                    'message' => 'Failed to copy favicon to: ' . $filepath
                ];
            }
        }
        
        return [
            'success' => $totalReplaced > 0,
            'message' => "Favicon replaced in {$totalReplaced} locations",
            'results' => $results,
            'total_replaced' => $totalReplaced
        ];
    }
    
    /**
     * Validate favicon file
     */
    private function validateFaviconFile($file) {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            return ['valid' => false, 'message' => 'No file uploaded'];
        }
        
        // Check file type
        $allowed_types = ['image/png', 'image/x-icon', 'image/vnd.microsoft.icon'];
        if (!in_array($file['type'], $allowed_types)) {
            return ['valid' => false, 'message' => 'Invalid file type. Only PNG and ICO files are allowed for favicons.'];
        }
        
        // Check file size (max 1MB)
        if ($file['size'] > 1024 * 1024) {
            return ['valid' => false, 'message' => 'File too large. Maximum size is 1MB.'];
        }
        
        // Check if it's actually an image
        $imageInfo = @getimagesize($file['tmp_name']);
        if (!$imageInfo) {
            return ['valid' => false, 'message' => 'Invalid image file.'];
        }
        
        return ['valid' => true, 'message' => 'File is valid'];
    }
    
    /**
     * Get image dimensions
     */
    private function getImageDimensions($filepath) {
        $imageInfo = @getimagesize($filepath);
        if ($imageInfo) {
            return $imageInfo[0] . ' × ' . $imageInfo[1] . ' pixels';
        }
        return 'Unknown';
    }
}

// Initialize replacer
$faviconReplacer = new FaviconReplacer();

// Handle form submissions
$message = '';
$messageType = '';
$replaceResults = [];

if ($_POST && isset($_FILES['favicon_file'])) {
    $replaceResults = $faviconReplacer->replaceFavicon($_FILES['favicon_file']);
    
    if ($replaceResults['success']) {
        $message = "✅ " . $replaceResults['message'];
        $messageType = 'success';
        
        logSecurityEvent('Favicon replaced via installer', 'INFO', [
            'total_replaced' => $replaceResults['total_replaced'],
            'locations' => array_column($replaceResults['results'], 'path')
        ]);
    } else {
        $message = "❌ " . $replaceResults['message'];
        $messageType = 'danger';
    }
}

$currentFavicons = $faviconReplacer->getCurrentFavicons();

echo getPageHeader('Favicon Replacement', 'Replace favicon across all system directories');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔄 Favicon Replacement</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>ℹ️ About Favicon Replacement:</strong><br>
                    This tool replaces favicon.png in all system directories including the verifying-permission folder.
                    It ensures consistent branding across all parts of your banking system.
                </div>
                
                <form method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="favicon_file" class="form-label">Select New Favicon</label>
                        <input type="file" class="form-control" id="favicon_file" name="favicon_file" 
                               accept=".png,.ico" required>
                        <div class="form-text">
                            Upload a PNG or ICO file. Recommended size: 32×32 or 16×16 pixels. Maximum size: 1MB.
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" 
                            onclick="return confirm('This will replace favicon in all system directories. Continue?')">
                        <i class="fas fa-upload"></i> Replace Favicon
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Current Favicons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📁 Current Favicon Locations</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Location</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Size</th>
                                <th>Dimensions</th>
                                <th>Modified</th>
                                <th>Preview</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($currentFavicons as $favicon): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($favicon['path']); ?></code></td>
                                    <td><?php echo htmlspecialchars($favicon['description']); ?></td>
                                    <td>
                                        <?php if ($favicon['exists']): ?>
                                            <span class="badge bg-success">Exists</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Missing</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $favicon['exists'] ? number_format($favicon['size']) . ' bytes' : 'N/A'; ?></td>
                                    <td><?php echo $favicon['dimensions']; ?></td>
                                    <td><?php echo $favicon['modified']; ?></td>
                                    <td>
                                        <?php if ($favicon['exists']): ?>
                                            <img src="<?php echo $favicon['path']; ?>?v=<?php echo time(); ?>" 
                                                 alt="Favicon" style="width: 16px; height: 16px;">
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($replaceResults['results'])): ?>
<!-- Replacement Results -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>✅ Replacement Results</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Location</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Message</th>
                                <th>Backup</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($replaceResults['results'] as $result): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($result['path']); ?></code></td>
                                    <td><?php echo htmlspecialchars($result['description']); ?></td>
                                    <td>
                                        <?php if ($result['success']): ?>
                                            <span class="badge bg-success">Success</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Failed</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($result['message']); ?></td>
                                    <td>
                                        <?php if (isset($result['backup']) && $result['backup']): ?>
                                            <small class="text-muted"><?php echo basename($result['backup']); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php echo getPageFooter(); ?>
