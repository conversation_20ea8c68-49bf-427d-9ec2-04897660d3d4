<?php
/**
 * Database Settings Module
 * Manage admin credentials and bank settings directly
 */

require_once 'includes/auth.php';
requireAuth();

class DatabaseSettingsManager {
    
    private $db_config_files = [
        '../../backend/connectdb.php',
        '../../mod/dbconnect.php'
    ];
    
    private $admin_files = [
        '../../backend/class.admin.php',
        '../../backend/in.php',
        '../../backend/editpass.php',
        '../../backend/debitvc.php',
        '../../backend/creditvc.php'
    ];
    
    /**
     * Get database connection using current credentials
     */
    private function getDatabaseConnection() {
        // Try to get database credentials from config files
        $db_credentials = $this->detectDatabaseCredentials();
        
        if (empty($db_credentials)) {
            throw new Exception("Could not detect database credentials");
        }
        
        // Use the first set of credentials found
        $creds = reset($db_credentials);
        
        try {
            $pdo = new PDO(
                "mysql:host={$creds['host']};dbname={$creds['database']}", 
                $creds['username'], 
                $creds['password'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            return $pdo;
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * Detect current database credentials from config files
     */
    public function detectDatabaseCredentials() {
        $credentials = [];
        
        foreach ($this->db_config_files as $file) {
            if (file_exists($file) && is_readable($file)) {
                $content = file_get_contents($file);
                $creds = $this->extractDatabaseCredentials($content, $file);
                if (!empty($creds)) {
                    $credentials[$file] = $creds;
                }
            }
        }
        
        return $credentials;
    }
    
    /**
     * Extract database credentials from file content
     */
    private function extractDatabaseCredentials($content, $file) {
        $credentials = [];
        
        // Pattern for various database credential formats
        $patterns = [
            'host' => ['/\$DB_host\s*=\s*["\']([^"\']+)["\']/', '/\$host\s*=\s*["\']([^"\']+)["\']/', '/host["\']?\s*=>\s*["\']([^"\']+)["\']/', '/host["\']?\s*=\s*["\']([^"\']+)["\']/', '/hostname["\']?\s*=\s*["\']([^"\']+)["\']/'],
            'username' => ['/\$DB_user\s*=\s*["\']([^"\']+)["\']/', '/\$username\s*=\s*["\']([^"\']+)["\']/', '/username["\']?\s*=>\s*["\']([^"\']+)["\']/', '/user["\']?\s*=\s*["\']([^"\']+)["\']/'],
            'password' => ['/\$DB_pass\s*=\s*["\']([^"\']+)["\']/', '/\$password\s*=\s*["\']([^"\']+)["\']/', '/password["\']?\s*=>\s*["\']([^"\']+)["\']/', '/pass["\']?\s*=\s*["\']([^"\']+)["\']/'],
            'database' => ['/\$DB_name\s*=\s*["\']([^"\']+)["\']/', '/\$database\s*=\s*["\']([^"\']+)["\']/', '/database["\']?\s*=>\s*["\']([^"\']+)["\']/', '/dbname["\']?\s*=\s*["\']([^"\']+)["\']/']
        ];
        
        foreach ($patterns as $key => $pattern_list) {
            foreach ($pattern_list as $pattern) {
                if (preg_match($pattern, $content, $matches)) {
                    $credentials[$key] = $matches[1];
                    break;
                }
            }
        }
        
        return $credentials;
    }
    
    /**
     * Get current admin credentials from database
     */
    public function getCurrentAdminCredentials() {
        try {
            $pdo = $this->getDatabaseConnection();
            $stmt = $pdo->prepare("SELECT id, cs_uname, cs_pass, email, verified_count FROM admin WHERE id = 1");
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Get current bank settings from database
     */
    public function getCurrentBankSettings() {
        try {
            $pdo = $this->getDatabaseConnection();
            $stmt = $pdo->prepare("SELECT * FROM bk_settings WHERE id = 1");
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * Update admin credentials in database
     */
    public function updateAdminCredentials($username, $password, $email) {
        try {
            $pdo = $this->getDatabaseConnection();
            
            // Hash password using MD5 (matching existing system)
            $hashed_password = md5($password);
            
            $stmt = $pdo->prepare("UPDATE admin SET cs_uname = ?, cs_pass = ?, email = ? WHERE id = 1");
            $result = $stmt->execute([$username, $hashed_password, $email]);
            
            if ($result) {
                // Also update hardcoded references in files
                $this->updateAdminCredentialsInFiles($username, $hashed_password, $email);
                return ['success' => true, 'message' => 'Admin credentials updated successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to update admin credentials'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Update bank settings in database
     */
    public function updateBankSettings($name, $domain, $email, $phone, $address) {
        try {
            $pdo = $this->getDatabaseConnection();
            
            $stmt = $pdo->prepare("UPDATE bk_settings SET bk_name = ?, bk_domain = ?, bk_email = ?, bk_phone = ?, bk_address = ? WHERE id = 1");
            $result = $stmt->execute([$name, $domain, $email, $phone, $address]);
            
            if ($result) {
                // Also update hardcoded references in files
                $this->updateBankSettingsInFiles($name, $domain, $email, $phone, $address);
                return ['success' => true, 'message' => 'Bank settings updated successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to update bank settings'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Update admin credentials in files (file replacement approach)
     */
    private function updateAdminCredentialsInFiles($username, $hashed_password, $email) {
        $files_to_update = [
            '../../backend/connectdb.php',
            '../../mod/dbconnect.php'
        ];
        
        foreach ($files_to_update as $file) {
            if (file_exists($file) && is_writable($file)) {
                $content = file_get_contents($file);
                
                // Update email references
                $content = preg_replace('/\$site_email\s*=\s*["\'][^"\']*["\']/', '$site_email = \'' . $email . '\'', $content);
                
                file_put_contents($file, $content);
            }
        }
    }
    
    /**
     * Update bank settings in files (file replacement approach)
     */
    private function updateBankSettingsInFiles($name, $domain, $email, $phone, $address) {
        $files_to_update = [
            '../../backend/connectdb.php',
            '../../mod/dbconnect.php'
        ];
        
        foreach ($files_to_update as $file) {
            if (file_exists($file) && is_writable($file)) {
                $content = file_get_contents($file);
                
                // Update various site settings
                $content = preg_replace('/\$site_title\s*=\s*["\'][^"\']*["\']/', '$site_title = \'' . $name . '\'', $content);
                $content = preg_replace('/\$site_url\s*=\s*["\'][^"\']*["\']/', '$site_url = \'' . $domain . '\'', $content);
                $content = preg_replace('/\$domain\s*=\s*["\'][^"\']*["\']/', '$domain = \'' . str_replace(['http://', 'https://'], '', $domain) . '\'', $content);
                $content = preg_replace('/\$site_email\s*=\s*["\'][^"\']*["\']/', '$site_email = \'' . $email . '\'', $content);
                $content = preg_replace('/\$site_phone\s*=\s*["\'][^"\']*["\']/', '$site_phone = \'' . $phone . '\'', $content);
                
                file_put_contents($file, $content);
            }
        }
    }
    
    /**
     * Test database connection
     */
    public function testDatabaseConnection() {
        try {
            $pdo = $this->getDatabaseConnection();
            $stmt = $pdo->query("SELECT 1");
            return ['success' => true, 'message' => 'Database connection successful'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Get database statistics
     */
    public function getDatabaseStats() {
        try {
            $pdo = $this->getDatabaseConnection();
            
            $stats = [];
            
            // Get table counts
            $tables = ['admin', 'bk_settings', 'account', 'transfer', 'ticket'];
            foreach ($tables as $table) {
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    $stats['tables'][$table] = $result['count'];
                } catch (Exception $e) {
                    $stats['tables'][$table] = 'Error: ' . $e->getMessage();
                }
            }
            
            // Get database size
            try {
                $stmt = $pdo->query("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' FROM information_schema.tables WHERE table_schema = DATABASE()");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['size'] = $result['DB Size in MB'] . ' MB';
            } catch (Exception $e) {
                $stats['size'] = 'Unknown';
            }
            
            return $stats;
        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}

// Initialize database settings manager
$dbSettingsManager = new DatabaseSettingsManager();

// Handle form submissions
$message = '';
$messageType = '';

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_admin':
                $username = $_POST['admin_username'] ?? '';
                $password = $_POST['admin_password'] ?? '';
                $email = $_POST['admin_email'] ?? '';
                
                if (empty($username) || empty($password) || empty($email)) {
                    $message = "❌ All admin fields are required.";
                    $messageType = 'danger';
                } else {
                    $result = $dbSettingsManager->updateAdminCredentials($username, $password, $email);
                    
                    if ($result['success']) {
                        $message = "✅ " . $result['message'];
                        $messageType = 'success';
                        
                        logSecurityEvent('Admin credentials updated via database settings', 'INFO', [
                            'username' => $username,
                            'email' => $email
                        ]);
                    } else {
                        $message = "❌ " . $result['message'];
                        $messageType = 'danger';
                    }
                }
                break;
                
            case 'update_bank_settings':
                $name = $_POST['bank_name'] ?? '';
                $domain = $_POST['bank_domain'] ?? '';
                $email = $_POST['bank_email'] ?? '';
                $phone = $_POST['bank_phone'] ?? '';
                $address = $_POST['bank_address'] ?? '';
                
                if (empty($name) || empty($domain) || empty($email)) {
                    $message = "❌ Bank name, domain, and email are required.";
                    $messageType = 'danger';
                } else {
                    $result = $dbSettingsManager->updateBankSettings($name, $domain, $email, $phone, $address);
                    
                    if ($result['success']) {
                        $message = "✅ " . $result['message'];
                        $messageType = 'success';
                        
                        logSecurityEvent('Bank settings updated via database settings', 'INFO', [
                            'name' => $name,
                            'domain' => $domain,
                            'email' => $email
                        ]);
                    } else {
                        $message = "❌ " . $result['message'];
                        $messageType = 'danger';
                    }
                }
                break;
                
            case 'test_connection':
                $result = $dbSettingsManager->testDatabaseConnection();
                
                if ($result['success']) {
                    $message = "✅ " . $result['message'];
                    $messageType = 'success';
                } else {
                    $message = "❌ " . $result['message'];
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Get current data
$adminCredentials = $dbSettingsManager->getCurrentAdminCredentials();
$bankSettings = $dbSettingsManager->getCurrentBankSettings();
$dbStats = $dbSettingsManager->getDatabaseStats();
$dbCredentials = $dbSettingsManager->detectDatabaseCredentials();

// Display the page
echo getPageHeader('Database Settings', 'Manage admin credentials and bank settings directly');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🏛️ Database Settings Manager</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Manage admin credentials and bank settings directly in the database. This module provides both database updates and file-based replacements for comprehensive system configuration.</p>

                <!-- Database Connection Test -->
                <form method="POST" class="d-inline">
                    <input type="hidden" name="action" value="test_connection">
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-plug"></i> Test Database Connection
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Database Statistics -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>📊 Database Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Database Credentials Detected:</h6>
                        <?php if (isset($dbCredentials) && !empty($dbCredentials)): ?>
                            <?php foreach ($dbCredentials as $file => $creds): ?>
                                <div class="file-item mb-2">
                                    <strong>File:</strong> <code><?php echo htmlspecialchars(basename($file)); ?></code><br>
                                    <strong>Host:</strong> <code><?php echo htmlspecialchars($creds['host'] ?? 'Not found'); ?></code><br>
                                    <strong>Database:</strong> <code><?php echo htmlspecialchars($creds['database'] ?? 'Not found'); ?></code><br>
                                    <strong>Username:</strong> <code><?php echo htmlspecialchars($creds['username'] ?? 'Not found'); ?></code>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="alert alert-warning">No database credentials detected in config files.</div>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-6">
                        <h6>Database Statistics:</h6>
                        <?php if (isset($dbStats['error'])): ?>
                            <div class="alert alert-danger">Error: <?php echo htmlspecialchars($dbStats['error']); ?></div>
                        <?php else: ?>
                            <div class="file-item">
                                <strong>Database Size:</strong> <?php echo htmlspecialchars($dbStats['size'] ?? 'Unknown'); ?><br>
                                <?php if (isset($dbStats['tables'])): ?>
                                    <strong>Table Records:</strong><br>
                                    <?php foreach ($dbStats['tables'] as $table => $count): ?>
                                        <small>• <?php echo htmlspecialchars($table); ?>: <?php echo htmlspecialchars($count); ?></small><br>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Admin Credentials Management -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>👤 Admin Credentials Management</h5>
            </div>
            <div class="card-body">
                <!-- Current Admin Credentials -->
                <h6>Current Admin Credentials:</h6>
                <?php if (isset($adminCredentials['error'])): ?>
                    <div class="alert alert-danger">Error: <?php echo htmlspecialchars($adminCredentials['error']); ?></div>
                <?php else: ?>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="file-item">
                                <strong>Username:</strong><br>
                                <code><?php echo htmlspecialchars($adminCredentials['cs_uname'] ?? 'Not found'); ?></code>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="file-item">
                                <strong>Email:</strong><br>
                                <code><?php echo htmlspecialchars($adminCredentials['email'] ?? 'Not found'); ?></code>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="file-item">
                                <strong>Status:</strong><br>
                                <?php if (($adminCredentials['verified_count'] ?? '') === 'Y'): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Inactive</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Admin Update Form -->
                <hr>
                <h6>Update Admin Credentials:</h6>
                <form method="POST" class="mt-3">
                    <input type="hidden" name="action" value="update_admin">

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="admin_username" class="form-label">Admin Username</label>
                                <input type="text" class="form-control" id="admin_username" name="admin_username"
                                       value="<?php echo htmlspecialchars($adminCredentials['cs_uname'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="admin_password" class="form-label">Admin Password</label>
                                <input type="password" class="form-control" id="admin_password" name="admin_password"
                                       placeholder="Enter new password" required>
                                <div class="form-text">Password will be MD5 hashed automatically</div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="admin_email" class="form-label">Admin Email</label>
                                <input type="email" class="form-control" id="admin_email" name="admin_email"
                                       value="<?php echo htmlspecialchars($adminCredentials['email'] ?? ''); ?>" required>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-edit"></i> Update Admin Credentials
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bank Settings Management -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🏦 Bank Settings Management</h5>
            </div>
            <div class="card-body">
                <!-- Current Bank Settings -->
                <h6>Current Bank Settings:</h6>
                <?php if (isset($bankSettings['error'])): ?>
                    <div class="alert alert-danger">Error: <?php echo htmlspecialchars($bankSettings['error']); ?></div>
                <?php else: ?>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="file-item">
                                <strong>Bank Name:</strong><br>
                                <code><?php echo htmlspecialchars($bankSettings['bk_name'] ?? 'Not found'); ?></code><br><br>
                                <strong>Domain:</strong><br>
                                <code><?php echo htmlspecialchars($bankSettings['bk_domain'] ?? 'Not found'); ?></code><br><br>
                                <strong>Email:</strong><br>
                                <code><?php echo htmlspecialchars($bankSettings['bk_email'] ?? 'Not found'); ?></code>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="file-item">
                                <strong>Phone:</strong><br>
                                <code><?php echo htmlspecialchars($bankSettings['bk_phone'] ?? 'Not found'); ?></code><br><br>
                                <strong>Address:</strong><br>
                                <code><?php echo htmlspecialchars($bankSettings['bk_address'] ?? 'Not found'); ?></code>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Bank Settings Update Form -->
                <hr>
                <h6>Update Bank Settings:</h6>
                <form method="POST" class="mt-3">
                    <input type="hidden" name="action" value="update_bank_settings">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_name" class="form-label">Bank Name</label>
                                <input type="text" class="form-control" id="bank_name" name="bank_name"
                                       value="<?php echo htmlspecialchars($bankSettings['bk_name'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_domain" class="form-label">Bank Domain</label>
                                <input type="url" class="form-control" id="bank_domain" name="bank_domain"
                                       value="<?php echo htmlspecialchars($bankSettings['bk_domain'] ?? ''); ?>"
                                       placeholder="https://example.com" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_email" class="form-label">Bank Email</label>
                                <input type="email" class="form-control" id="bank_email" name="bank_email"
                                       value="<?php echo htmlspecialchars($bankSettings['bk_email'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_phone" class="form-label">Bank Phone</label>
                                <input type="text" class="form-control" id="bank_phone" name="bank_phone"
                                       value="<?php echo htmlspecialchars($bankSettings['bk_phone'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="bank_address" class="form-label">Bank Address</label>
                                <textarea class="form-control" id="bank_address" name="bank_address" rows="3"><?php echo htmlspecialchars($bankSettings['bk_address'] ?? ''); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-building"></i> Update Bank Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <div class="alert alert-info">
                    <strong>ℹ️ Note:</strong> Changes made here will update both the database records and configuration files to ensure system-wide consistency.
                </div>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<?php echo getPageFooter(); ?>

<?php include 'includes/footer.php'; ?>
