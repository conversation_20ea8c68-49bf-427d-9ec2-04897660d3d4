<?php
/**
 * Test Database Pattern Recognition
 * Tests the new private class property patterns
 */

require_once 'includes/auth.php';
requireAuth();

echo getPageHeader('Database Pattern Test', 'Testing the new private class property patterns');

// Test content that mimics connectdb.php
$test_content = '<?php
class Database
{
    private $host = "localhost";
    private $db_name = "u883539600_seabridge";
    private $username = "u883539600_seabridge";
    private $password = "Money2024@Demo#";
}

$DB_host = "localhost";
$DB_user = "u883539600_seabridge";
$DB_pass = "Money2024@Demo#";
$DB_name = "u883539600_seabridge";

$host = "localhost";
$database = "u883539600_seabridge";
$username = "u883539600_seabridge";
$password = "Money2024@Demo#";
?>';

// Test the extraction patterns
function testExtractCredentials($content) {
    $credentials = [];
    $lines = explode("\n", $content);
    
    foreach ($lines as $lineNum => $line) {
        $lineNum++; // 1-based line numbers
        
        // Database host patterns
        if (preg_match('/\$(?:DB_host|host)\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
            $credentials['db_host'][] = [
                'value' => $matches[1],
                'line' => $lineNum,
                'context' => trim($line),
                'pattern' => 'Variable'
            ];
        }
        // Private class property host patterns
        if (preg_match('/private\s+\$host\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
            $credentials['db_host'][] = [
                'value' => $matches[1],
                'line' => $lineNum,
                'context' => trim($line),
                'pattern' => 'Private Property'
            ];
        }
        
        // Database username patterns
        if (preg_match('/\$(?:DB_user|username)\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
            $credentials['db_username'][] = [
                'value' => $matches[1],
                'line' => $lineNum,
                'context' => trim($line),
                'pattern' => 'Variable'
            ];
        }
        // Private class property username patterns
        if (preg_match('/private\s+\$username\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
            $credentials['db_username'][] = [
                'value' => $matches[1],
                'line' => $lineNum,
                'context' => trim($line),
                'pattern' => 'Private Property'
            ];
        }
        
        // Database password patterns
        if (preg_match('/\$(?:DB_pass|password)\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
            $credentials['db_password'][] = [
                'value' => $matches[1],
                'line' => $lineNum,
                'context' => trim($line),
                'pattern' => 'Variable'
            ];
        }
        // Private class property password patterns
        if (preg_match('/private\s+\$password\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
            $credentials['db_password'][] = [
                'value' => $matches[1],
                'line' => $lineNum,
                'context' => trim($line),
                'pattern' => 'Private Property'
            ];
        }
        
        // Database name patterns
        if (preg_match('/\$(?:DB_name|database)\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
            $credentials['db_name'][] = [
                'value' => $matches[1],
                'line' => $lineNum,
                'context' => trim($line),
                'pattern' => 'Variable'
            ];
        }
        // Private class property database name patterns
        if (preg_match('/private\s+\$db_name\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
            $credentials['db_name'][] = [
                'value' => $matches[1],
                'line' => $lineNum,
                'context' => trim($line),
                'pattern' => 'Private Property'
            ];
        }
    }
    
    return $credentials;
}

$detected = testExtractCredentials($test_content);

?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🧪 Pattern Recognition Test Results</h5>
            </div>
            <div class="card-body">
                
                <?php if (!empty($detected)): ?>
                    <?php foreach ($detected as $type => $credentials): ?>
                        <h6 class="text-primary"><?php echo ucfirst(str_replace('_', ' ', $type)); ?></h6>
                        <div class="table-responsive mb-4">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Line</th>
                                        <th>Pattern Type</th>
                                        <th>Value</th>
                                        <th>Context</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($credentials as $cred): ?>
                                        <tr>
                                            <td><?php echo $cred['line']; ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $cred['pattern'] === 'Private Property' ? 'success' : 'primary'; ?>">
                                                    <?php echo $cred['pattern']; ?>
                                                </span>
                                            </td>
                                            <td><code><?php echo htmlspecialchars($cred['value']); ?></code></td>
                                            <td><code><?php echo htmlspecialchars($cred['context']); ?></code></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endforeach; ?>
                    
                    <div class="alert alert-success">
                        <h6>✅ Pattern Recognition Summary</h6>
                        <ul class="mb-0">
                            <li><strong>Database Host:</strong> <?php echo count($detected['db_host'] ?? []); ?> patterns detected</li>
                            <li><strong>Database Username:</strong> <?php echo count($detected['db_username'] ?? []); ?> patterns detected</li>
                            <li><strong>Database Password:</strong> <?php echo count($detected['db_password'] ?? []); ?> patterns detected</li>
                            <li><strong>Database Name:</strong> <?php echo count($detected['db_name'] ?? []); ?> patterns detected</li>
                        </ul>
                    </div>
                    
                <?php else: ?>
                    <div class="alert alert-warning">
                        <strong>⚠️ No patterns detected!</strong> This indicates an issue with the regex patterns.
                    </div>
                <?php endif; ?>
                
                <div class="alert alert-info">
                    <h6>🎯 What This Test Validates</h6>
                    <p>This test confirms that the database replacer can now detect:</p>
                    <ul class="mb-0">
                        <li><strong>Private class properties:</strong> <code>private $db_name = "value"</code></li>
                        <li><strong>Regular variables:</strong> <code>$DB_name = "value"</code></li>
                        <li><strong>Both patterns</strong> that exist in <code>connectdb.php</code></li>
                    </ul>
                </div>
                
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="database.php" class="btn btn-primary">
            <i class="fas fa-database"></i> Test Database Replacement
        </a>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<?php echo getPageFooter(); ?>
