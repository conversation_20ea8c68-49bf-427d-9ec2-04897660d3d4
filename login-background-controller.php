<?php
/**
 * Login Background Controller
 * Control login page background, captcha, and styling
 */

require_once 'includes/auth.php';
requireAuth();

class LoginBackgroundController {
    
    private $path_manager;
    private $config_file;
    private $backgrounds_dir;
    
    public function __construct() {
        $this->path_manager = getPathManager();
        $this->config_file = __DIR__ . '/includes/login-config.json';
        $this->backgrounds_dir = __DIR__ . '/backgrounds/';

        // Create backgrounds directory if it doesn't exist
        if (!is_dir($this->backgrounds_dir)) {
            mkdir($this->backgrounds_dir, 0755, true);
        }
    }
    
    /**
     * Get lorem image options
     */
    public function getLoremImageOptions() {
        return [
            'nature-1920x1080' => 'Nature Landscape (1920x1080)',
            'abstract-1920x1080' => 'Abstract Art (1920x1080)',
            'city-1920x1080' => 'City Skyline (1920x1080)',
            'tech-1920x1080' => 'Technology (1920x1080)',
            'space-1920x1080' => 'Space/Galaxy (1920x1080)',
            'ocean-1920x1080' => 'Ocean/Water (1920x1080)',
            'mountain-1920x1080' => 'Mountains (1920x1080)',
            'forest-1920x1080' => 'Forest/Trees (1920x1080)'
        ];
    }

    /**
     * Get lorem image URL
     */
    public function getLoremImageUrl($type) {
        $baseUrl = 'https://picsum.photos/1920/1080';
        $seeds = [
            'nature-1920x1080' => '?random=nature',
            'abstract-1920x1080' => '?random=abstract',
            'city-1920x1080' => '?random=city',
            'tech-1920x1080' => '?random=tech',
            'space-1920x1080' => '?random=space',
            'ocean-1920x1080' => '?random=ocean',
            'mountain-1920x1080' => '?random=mountain',
            'forest-1920x1080' => '?random=forest'
        ];

        return $baseUrl . ($seeds[$type] ?? '?random=1');
    }

    /**
     * Get current login configuration
     */
    public function getLoginConfig() {
        $defaultConfig = [
            'background_type' => 'gradient',
            'gradient_colors' => ['#667eea', '#764ba2', '#f093fb'],
            'background_image' => '',
            'lorem_image' => '',
            'blur_percentage' => 20,
            'main_background_blur' => 0,
            'captcha_enabled' => false,
            'captcha_type' => 'math',
            'security_notice_color' => '#ff0000'
        ];
        
        if (file_exists($this->config_file)) {
            $config = json_decode(file_get_contents($this->config_file), true);
            return array_merge($defaultConfig, $config ?: []);
        }
        
        return $defaultConfig;
    }
    
    /**
     * Save login configuration
     */
    public function saveLoginConfig($config) {
        $currentConfig = $this->getLoginConfig();
        $newConfig = array_merge($currentConfig, $config);
        
        $result = file_put_contents($this->config_file, json_encode($newConfig, JSON_PRETTY_PRINT));

        if ($result !== false) {
            return ['success' => true, 'message' => 'Login configuration saved successfully'];
        }

        return ['success' => false, 'message' => 'Failed to save configuration'];
    }
    
    /**
     * Upload background image
     */
    public function uploadBackgroundImage($uploadedFile) {
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
        if (!in_array($uploadedFile['type'], $allowedTypes)) {
            return ['success' => false, 'message' => 'Invalid file type. Only JPEG, PNG, GIF, WebP, and SVG are allowed.'];
        }
        
        // Validate file size (max 5MB)
        if ($uploadedFile['size'] > 5 * 1024 * 1024) {
            return ['success' => false, 'message' => 'File size too large. Maximum size is 5MB.'];
        }
        
        $filename = 'login-background-' . time() . '.' . pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
        $targetPath = $this->backgrounds_dir . $filename;
        
        if (move_uploaded_file($uploadedFile['tmp_name'], $targetPath)) {
            return [
                'success' => true,
                'message' => 'Background image uploaded successfully',
                'filename' => $filename
            ];
        }
        
        return ['success' => false, 'message' => 'Failed to upload image'];
    }
    
    /**
     * Get available background images
     */
    public function getBackgroundImages() {
        $images = [];
        
        if (is_dir($this->backgrounds_dir)) {
            $files = scandir($this->backgrounds_dir);
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..' && preg_match('/\.(jpg|jpeg|png|gif|webp|svg)$/i', $file)) {
                    $filepath = $this->backgrounds_dir . $file;
                    $images[] = [
                        'filename' => $file,
                        'size' => filesize($filepath),
                        'modified' => date('Y-m-d H:i:s', filemtime($filepath)),
                        'web_path' => 'backgrounds/' . $file
                    ];
                }
            }
        }
        
        return $images;
    }
    
    /**
     * Generate math captcha with better randomization
     */
    public function generateMathCaptcha() {
        // More varied number ranges for different operations
        $operations = ['+', '-', '*'];
        $operation = $operations[array_rand($operations)];

        switch ($operation) {
            case '+':
                // Addition: 1-15 + 1-15
                $num1 = rand(1, 15);
                $num2 = rand(1, 15);
                $answer = $num1 + $num2;
                break;
            case '-':
                // Subtraction: ensure positive result, larger range
                $num1 = rand(5, 20);
                $num2 = rand(1, $num1); // num2 always smaller than num1
                $answer = $num1 - $num2;
                break;
            case '*':
                // Multiplication: smaller numbers to keep answers reasonable
                $num1 = rand(2, 8);
                $num2 = rand(2, 8);
                $answer = $num1 * $num2;
                break;
        }

        return [
            'question' => "$num1 $operation $num2 = ?",
            'answer' => $answer,
            'operation' => $operation,
            'num1' => $num1,
            'num2' => $num2
        ];
    }

    /**
     * Delete background image
     */
    public function deleteBackgroundImage($filename) {
        // Validate filename to prevent directory traversal
        $filename = basename($filename);
        $filepath = $this->backgrounds_dir . $filename;

        if (!file_exists($filepath)) {
            return ['success' => false, 'message' => 'File not found.'];
        }

        // Check if this image is currently being used
        $currentConfig = $this->getLoginConfig();
        if ($currentConfig['background_image'] === $filename) {
            // Reset to gradient if deleting current background
            $currentConfig['background_type'] = 'gradient';
            $currentConfig['background_image'] = '';
            $this->saveLoginConfig($currentConfig);
        }

        if (unlink($filepath)) {
            return ['success' => true, 'message' => 'Background image deleted successfully.'];
        }

        return ['success' => false, 'message' => 'Failed to delete image.'];
    }


}

// Initialize controller
$loginController = new LoginBackgroundController();

// Handle form submissions
$message = '';
$messageType = '';

if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'save_config':
                $config = [
                    'background_type' => $_POST['background_type'] ?? 'gradient',
                    'gradient_colors' => [
                        $_POST['gradient_color1'] ?? '#667eea',
                        $_POST['gradient_color2'] ?? '#764ba2',
                        $_POST['gradient_color3'] ?? '#f093fb'
                    ],
                    'background_image' => $_POST['background_image'] ?? '',
                    'lorem_image' => $_POST['lorem_image'] ?? '',
                    'blur_percentage' => intval($_POST['blur_percentage'] ?? 20),
                    'main_background_blur' => intval($_POST['main_background_blur'] ?? 0),
                    'captcha_enabled' => isset($_POST['captcha_enabled']),
                    'captcha_type' => $_POST['captcha_type'] ?? 'math',
                    'security_notice_color' => $_POST['security_notice_color'] ?? '#ff0000'
                ];

                $result = $loginController->saveLoginConfig($config);
                $message = $result['message'];
                $messageType = $result['success'] ? 'success' : 'danger';
                break;
                
            case 'upload_background':
                if (isset($_FILES['background_file']) && $_FILES['background_file']['error'] === UPLOAD_ERR_OK) {
                    $result = $loginController->uploadBackgroundImage($_FILES['background_file']);
                    $message = $result['message'];
                    $messageType = $result['success'] ? 'success' : 'danger';

                    // If upload was successful, automatically configure it as the background
                    if ($result['success'] && isset($result['filename'])) {
                        $currentConfig = $loginController->getLoginConfig();
                        $currentConfig['background_type'] = 'image';
                        $currentConfig['background_image'] = $result['filename'];
                        $configResult = $loginController->saveLoginConfig($currentConfig);

                        if ($configResult['success']) {
                            $message .= ' Background automatically configured.';
                        } else {
                            $message .= ' Please manually select the uploaded image.';
                        }
                    }
                } else {
                    $message = 'Please select a file to upload.';
                    $messageType = 'danger';
                }
                break;

            case 'delete_background':
                if (isset($_POST['filename'])) {
                    $result = $loginController->deleteBackgroundImage($_POST['filename']);
                    $message = $result['message'];
                    $messageType = $result['success'] ? 'success' : 'danger';
                } else {
                    $message = 'No filename specified for deletion.';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Get current data
$currentConfig = $loginController->getLoginConfig();
$backgroundImages = $loginController->getBackgroundImages();
$mathCaptcha = $loginController->generateMathCaptcha();

// Display the page
echo getPageHeader('Login Background Controller', 'Customize login page background, captcha, and styling');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🎨 Login Background Controller</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Customize the installer login page background, captcha settings, and styling options.</p>
            </div>
        </div>
    </div>
</div>

<!-- Background Configuration -->
<div class="row mt-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>🖼️ Background Settings</h5>
            </div>
            <div class="card-body">
                <form method="post" action="">
                    <input type="hidden" name="action" value="save_config">

                    <!-- Background Type -->
                    <div class="mb-4">
                        <label class="form-label">Background Type</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="background_type" id="bg_gradient"
                                           value="gradient" <?php echo $currentConfig['background_type'] === 'gradient' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="bg_gradient">
                                        <strong>Gradient Background</strong><br>
                                        <small class="text-muted">Use custom gradient colors</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="background_type" id="bg_image"
                                           value="image" <?php echo $currentConfig['background_type'] === 'image' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="bg_image">
                                        <strong>Uploaded Image</strong><br>
                                        <small class="text-muted">Use your own image</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="background_type" id="bg_lorem"
                                           value="lorem" <?php echo $currentConfig['background_type'] === 'lorem' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="bg_lorem">
                                        <strong>Random Image</strong><br>
                                        <small class="text-muted">Lorem Picsum images</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Gradient Colors -->
                    <div id="gradient_section" class="mb-4">
                        <label class="form-label">Gradient Colors</label>
                        <div class="row">
                            <div class="col-md-4">
                                <label for="gradient_color1" class="form-label small">Color 1</label>
                                <input type="color" class="form-control form-control-color" id="gradient_color1"
                                       name="gradient_color1" value="<?php echo htmlspecialchars($currentConfig['gradient_colors'][0]); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="gradient_color2" class="form-label small">Color 2</label>
                                <input type="color" class="form-control form-control-color" id="gradient_color2"
                                       name="gradient_color2" value="<?php echo htmlspecialchars($currentConfig['gradient_colors'][1]); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="gradient_color3" class="form-label small">Color 3</label>
                                <input type="color" class="form-control form-control-color" id="gradient_color3"
                                       name="gradient_color3" value="<?php echo htmlspecialchars($currentConfig['gradient_colors'][2]); ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Background Image Selection -->
                    <div id="image_section" class="mb-4" style="display: none;">
                        <label for="background_image" class="form-label">Background Image</label>
                        <select class="form-control" id="background_image" name="background_image">
                            <option value="">Select an image...</option>
                            <?php foreach ($backgroundImages as $image): ?>
                                <option value="<?php echo htmlspecialchars($image['filename']); ?>"
                                        <?php echo $currentConfig['background_image'] === $image['filename'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($image['filename']); ?>
                                    (<?php echo number_format($image['size'] / 1024, 1); ?> KB)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Lorem Image Selection -->
                    <div id="lorem_section" class="mb-4" style="display: none;">
                        <label for="lorem_image" class="form-label">Random Image Type</label>
                        <select class="form-control" id="lorem_image" name="lorem_image">
                            <option value="">Select image type...</option>
                            <?php foreach ($loginController->getLoremImageOptions() as $key => $label): ?>
                                <option value="<?php echo htmlspecialchars($key); ?>"
                                        <?php echo $currentConfig['lorem_image'] === $key ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($label); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            Random high-quality images from Lorem Picsum. Images change each time you refresh.
                        </div>
                    </div>

                    <!-- Container Blur Percentage -->
                    <div class="mb-4">
                        <label for="blur_percentage" class="form-label">
                            Login Container Blur: <span id="blur_value"><?php echo $currentConfig['blur_percentage']; ?>px</span>
                        </label>
                        <input type="range" class="form-range" id="blur_percentage" name="blur_percentage"
                               min="0" max="50" value="<?php echo $currentConfig['blur_percentage']; ?>"
                               oninput="document.getElementById('blur_value').textContent = this.value + 'px'">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            Controls the blur effect on the login form container (glassmorphism effect)
                        </div>
                    </div>

                    <!-- Main Background Blur -->
                    <div class="mb-4">
                        <label for="main_background_blur" class="form-label">
                            Main Background Blur: <span id="main_blur_value"><?php echo $currentConfig['main_background_blur']; ?>px</span>
                        </label>
                        <input type="range" class="form-range" id="main_background_blur" name="main_background_blur"
                               min="0" max="20" value="<?php echo $currentConfig['main_background_blur']; ?>"
                               oninput="document.getElementById('main_blur_value').textContent = this.value + 'px'">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            Controls the blur effect on the main background image/gradient
                        </div>
                    </div>

                    <!-- Security Notice Color -->
                    <div class="mb-4">
                        <label for="security_notice_color" class="form-label">Security Notice Text Color</label>
                        <div class="row">
                            <div class="col-md-6">
                                <input type="color" class="form-control form-control-color" id="security_notice_color"
                                       name="security_notice_color" value="<?php echo htmlspecialchars($currentConfig['security_notice_color']); ?>">
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-warning small">
                                    <span style="color: <?php echo htmlspecialchars($currentConfig['security_notice_color']); ?>;">
                                        <strong>⚠️ Delete this installer folder after use!</strong>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Captcha Settings -->
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="captcha_enabled" name="captcha_enabled"
                                   <?php echo $currentConfig['captcha_enabled'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="captcha_enabled">
                                <strong>Enable Math Captcha</strong><br>
                                <small class="text-muted">Add simple math verification (no robot scanner)</small>
                            </label>
                        </div>
                    </div>

                    <!-- Captcha Preview -->
                    <div id="captcha_preview" class="mb-4" style="<?php echo $currentConfig['captcha_enabled'] ? '' : 'display: none;'; ?>">
                        <label class="form-label">Captcha Preview</label>
                        <div class="alert alert-info">
                            <strong>Math Question:</strong> <?php echo htmlspecialchars($mathCaptcha['question']); ?><br>
                            <small class="text-muted">Answer: <?php echo $mathCaptcha['answer']; ?> (for testing)</small>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Save Configuration
                    </button>
                    <a href="test-login-config.php" class="btn btn-info ms-2">
                        <i class="fas fa-bug"></i> Test Configuration
                    </a>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Upload Background Image -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>📤 Upload Background</h5>
            </div>
            <div class="card-body">
                <form method="post" action="" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="upload_background">

                    <div class="mb-3">
                        <label for="background_file" class="form-label">Background Image</label>
                        <input type="file" class="form-control" id="background_file" name="background_file"
                               accept="image/*" required>
                        <div class="form-text">Supported: JPEG, PNG, GIF, WebP (Max: 5MB)</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload Image
                    </button>
                </form>
            </div>
        </div>

        <!-- Current Preview -->
        <div class="card">
            <div class="card-header">
                <h5>👁️ Current Preview</h5>
            </div>
            <div class="card-body">
                <div class="preview-container" style="
                    height: 200px;
                    border-radius: 10px;
                    position: relative;
                    overflow: hidden;
                    <?php if ($currentConfig['background_type'] === 'image' && !empty($currentConfig['background_image'])): ?>
                        background: url('backgrounds/<?php echo htmlspecialchars($currentConfig['background_image']); ?>?v=<?php echo time(); ?>') center/cover no-repeat;
                    <?php elseif ($currentConfig['background_type'] === 'lorem' && !empty($currentConfig['lorem_image'])): ?>
                        background: url('<?php echo $loginController->getLoremImageUrl($currentConfig['lorem_image']); ?>') center/cover no-repeat;
                    <?php else: ?>
                        background: linear-gradient(135deg, <?php echo htmlspecialchars($currentConfig['gradient_colors'][0]); ?> 0%, <?php echo htmlspecialchars($currentConfig['gradient_colors'][1]); ?> 50%, <?php echo htmlspecialchars($currentConfig['gradient_colors'][2]); ?> 100%);
                    <?php endif; ?>
                ">
                    <div style="
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        backdrop-filter: blur(<?php echo $currentConfig['blur_percentage']; ?>px);
                        background: rgba(255, 255, 255, 0.25);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        text-align: center;
                        font-family: 'JetBrains Mono', monospace;
                    ">
                        <div>
                            <h6>🏦 System Installer</h6>
                            <small>Login Preview</small>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <small class="text-muted">
                        <strong>Type:</strong> <?php echo ucfirst($currentConfig['background_type']); ?><br>
                        <strong>Blur:</strong> <?php echo $currentConfig['blur_percentage']; ?>px<br>
                        <?php if ($currentConfig['captcha_enabled']): ?>
                            <strong>Captcha:</strong> Enabled
                        <?php endif; ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Available Background Images -->
<?php if (!empty($backgroundImages)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🖼️ Available Background Images</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($backgroundImages as $image): ?>
                        <div class="col-md-3 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div class="image-preview mb-2" style="
                                        height: 100px;
                                        background: url('backgrounds/<?php echo htmlspecialchars($image['filename']); ?>') center/cover no-repeat;
                                        border-radius: 5px;
                                        border: 1px solid #ddd;
                                    "></div>
                                    <h6 class="card-title small"><?php echo htmlspecialchars($image['filename']); ?></h6>
                                    <small class="text-muted">
                                        <?php echo number_format($image['size'] / 1024, 1); ?> KB<br>
                                        <?php echo $image['modified']; ?>
                                    </small>
                                    <div class="mt-2">
                                        <form method="post" style="display: inline;"
                                              onsubmit="return confirm('Are you sure you want to delete this background image?');">
                                            <input type="hidden" name="action" value="delete_background">
                                            <input type="hidden" name="filename" value="<?php echo htmlspecialchars($image['filename']); ?>">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Security Audit Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">🔒 Security Audit Information</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-shield-alt"></i> Current Security Measures</h6>
                    <ul class="mb-0">
                        <li><strong>Session-based authentication</strong> - Prevents unauthorized access</li>
                        <li><strong>Password protection</strong> - Single installer password required</li>
                        <li><strong>File path validation</strong> - Prevents directory traversal attacks</li>
                        <li><strong>Input sanitization</strong> - All user inputs are sanitized</li>
                        <li><strong>File type validation</strong> - Only allowed image types accepted</li>
                        <li><strong>File size limits</strong> - Prevents large file uploads</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Security Recommendations</h6>
                    <ul class="mb-0">
                        <li><strong style="color: #ff0000;">Delete installer folder after use</strong></li>
                        <li>Use strong installer password (current: check includes/auth.php)</li>
                        <li>Limit access to installer directory via .htaccess</li>
                        <li>Monitor server logs for unauthorized access attempts</li>
                        <li>Use HTTPS in production environments</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle background type sections
document.addEventListener('DOMContentLoaded', function() {
    const gradientRadio = document.getElementById('bg_gradient');
    const imageRadio = document.getElementById('bg_image');
    const loremRadio = document.getElementById('bg_lorem');
    const gradientSection = document.getElementById('gradient_section');
    const imageSection = document.getElementById('image_section');
    const loremSection = document.getElementById('lorem_section');
    const captchaCheckbox = document.getElementById('captcha_enabled');
    const captchaPreview = document.getElementById('captcha_preview');

    function toggleSections() {
        // Hide all sections first
        gradientSection.style.display = 'none';
        imageSection.style.display = 'none';
        loremSection.style.display = 'none';

        // Show the selected section
        if (gradientRadio.checked) {
            gradientSection.style.display = 'block';
        } else if (imageRadio.checked) {
            imageSection.style.display = 'block';
        } else if (loremRadio.checked) {
            loremSection.style.display = 'block';
        }
    }

    function toggleCaptcha() {
        captchaPreview.style.display = captchaCheckbox.checked ? 'block' : 'none';
    }

    gradientRadio.addEventListener('change', toggleSections);
    imageRadio.addEventListener('change', toggleSections);
    loremRadio.addEventListener('change', toggleSections);
    captchaCheckbox.addEventListener('change', toggleCaptcha);

    // Initial state
    toggleSections();
    toggleCaptcha();
});
</script>

<?php echo getPageFooter(); ?>
