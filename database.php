<?php
/**
 * Database Configuration Module
 * Configure database credentials across all system files
 */

require_once 'includes/auth.php';
requireAuth();

class DatabaseCredentialReplacer {

    public $files_to_scan;
    private $path_manager;

    public function __construct() {
        $this->path_manager = getPathManager();
        $this->initializeFilesToScan();
    }

    private function initializeFilesToScan() {
        $banking_path = $this->path_manager->getRelativePath($this->path_manager->getBankingDir());

        $this->files_to_scan = [
            $banking_path . '/mod/dbconnect.php',
            $banking_path . '/backend/connectdb.php',
            $banking_path . '/inter_transfer.php',
            $banking_path . '/inter_transfer_card.php',
            $banking_path . '/retrieve-client.php',
            $banking_path . '/backend/class.admin.php',
            $banking_path . '/backend/class.crud.php',
            $banking_path . '/mod/controller.php',
            $banking_path . '/mod/mailsms.php',
            $banking_path . '/btc-transfer.php'
        ];
    }
    
    /**
     * Detect current database credentials
     */
    public function getCurrentCredentials() {
        $credentials = [];
        
        foreach ($this->files_to_scan as $file) {
            if (file_exists($file) && is_readable($file)) {
                $content = file_get_contents($file);
                $fileCredentials = $this->extractCredentials($content, $file);
                if (!empty($fileCredentials)) {
                    $credentials[$file] = $fileCredentials;
                }
            }
        }
        
        return $credentials;
    }
    
    /**
     * Extract credentials from file content
     */
    private function extractCredentials($content, $filepath) {
        $credentials = [];
        $lines = explode("\n", $content);
        
        foreach ($lines as $lineNum => $line) {
            $lineNum++; // 1-based line numbers
            
            // Database host patterns
            if (preg_match('/\$(?:DB_host|host)\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_host'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            // Private class property host patterns
            if (preg_match('/private\s+\$host\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_host'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            // Database host define patterns
            if (preg_match('/define\s*\(\s*["\']DB_SERVER["\']\s*,\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_host'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            
            // Database username patterns
            if (preg_match('/\$(?:DB_user|username)\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_username'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            // Private class property username patterns
            if (preg_match('/private\s+\$username\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_username'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            // Database username define patterns
            if (preg_match('/define\s*\(\s*["\']DB_USERNAME["\']\s*,\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_username'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            
            // Database password patterns
            if (preg_match('/\$(?:DB_pass|password)\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_password'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            // Private class property password patterns
            if (preg_match('/private\s+\$password\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_password'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            // Database password define patterns
            if (preg_match('/define\s*\(\s*["\']DB_PASSWORD["\']\s*,\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_password'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            
            // Database name patterns
            if (preg_match('/\$(?:DB_name|database)\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_name'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            // Private class property database name patterns
            if (preg_match('/private\s+\$db_name\s*=\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_name'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
            // Database name define patterns
            if (preg_match('/define\s*\(\s*["\']DB_NAME["\']\s*,\s*["\']([^"\']+)["\']/', $line, $matches)) {
                $credentials['db_name'][] = [
                    'value' => $matches[1],
                    'line' => $lineNum,
                    'context' => trim($line)
                ];
            }
        }
        
        return $credentials;
    }
    
    /**
     * Replace database credentials
     */
    public function replaceCredentials($newCredentials) {
        $results = [];
        $backupTimestamp = date('Y-m-d-H-i-s');
        $totalChanges = 0;
        
        foreach ($this->files_to_scan as $file) {
            if (!file_exists($file) || !is_writable($file)) continue;
            
            $content = file_get_contents($file);
            $originalContent = $content;
            $fileChanges = 0;
            
            // Replace database host
            if (!empty($newCredentials['db_host'])) {
                // Pattern 1: Variables like $DB_host = 'value'
                $content = preg_replace(
                    '/(\$(?:DB_host|host)\s*=\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_host'] . '${2}',
                    $content,
                    -1,
                    $count1
                );

                // Pattern 2: Private class properties like private $host = 'value'
                $content = preg_replace(
                    '/(private\s+\$host\s*=\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_host'] . '${2}',
                    $content,
                    -1,
                    $count2
                );

                // Pattern 3: Define constants like define('DB_SERVER', 'value')
                $content = preg_replace(
                    '/(define\s*\(\s*["\']DB_SERVER["\']\s*,\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_host'] . '${2}',
                    $content,
                    -1,
                    $count3
                );

                $fileChanges += ($count1 + $count2 + $count3);
            }
            
            // Replace database username
            if (!empty($newCredentials['db_username'])) {
                // Pattern 1: Variables like $DB_user = 'value'
                $content = preg_replace(
                    '/(\$(?:DB_user|username)\s*=\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_username'] . '${2}',
                    $content,
                    -1,
                    $count1
                );

                // Pattern 2: Private class properties like private $username = 'value'
                $content = preg_replace(
                    '/(private\s+\$username\s*=\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_username'] . '${2}',
                    $content,
                    -1,
                    $count2
                );

                // Pattern 3: Define constants like define('DB_USERNAME', 'value')
                $content = preg_replace(
                    '/(define\s*\(\s*["\']DB_USERNAME["\']\s*,\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_username'] . '${2}',
                    $content,
                    -1,
                    $count3
                );

                $fileChanges += ($count1 + $count2 + $count3);
            }
            
            // Replace database password
            if (!empty($newCredentials['db_password'])) {
                // Pattern 1: Variables like $DB_pass = 'value'
                $content = preg_replace(
                    '/(\$(?:DB_pass|password)\s*=\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_password'] . '${2}',
                    $content,
                    -1,
                    $count1
                );

                // Pattern 2: Private class properties like private $password = 'value'
                $content = preg_replace(
                    '/(private\s+\$password\s*=\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_password'] . '${2}',
                    $content,
                    -1,
                    $count2
                );

                // Pattern 3: Define constants like define('DB_PASSWORD', 'value')
                $content = preg_replace(
                    '/(define\s*\(\s*["\']DB_PASSWORD["\']\s*,\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_password'] . '${2}',
                    $content,
                    -1,
                    $count3
                );

                $fileChanges += ($count1 + $count2 + $count3);
            }
            
            // Replace database name
            if (!empty($newCredentials['db_name'])) {
                // Pattern 1: Variables like $DB_name = 'value'
                $content = preg_replace(
                    '/(\$(?:DB_name|database)\s*=\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_name'] . '${2}',
                    $content,
                    -1,
                    $count1
                );

                // Pattern 2: Private class properties like private $db_name = 'value'
                $content = preg_replace(
                    '/(private\s+\$db_name\s*=\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_name'] . '${2}',
                    $content,
                    -1,
                    $count2
                );

                // Pattern 3: Define constants like define('DB_NAME', 'value')
                $content = preg_replace(
                    '/(define\s*\(\s*["\']DB_NAME["\']\s*,\s*["\'])[^"\']+(["\'])/',
                    '${1}' . $newCredentials['db_name'] . '${2}',
                    $content,
                    -1,
                    $count3
                );

                $fileChanges += ($count1 + $count2 + $count3);
            }
            
            if ($fileChanges > 0) {
                // Create backup
                $backupPath = createBackup($file);
                
                // Write new content
                file_put_contents($file, $content);
                
                $results[] = [
                    'file' => $file,
                    'changes' => $fileChanges,
                    'backup' => $backupPath,
                    'success' => true
                ];
                
                $totalChanges += $fileChanges;
            }
        }
        
        return [
            'results' => $results,
            'total_changes' => $totalChanges,
            'total_files' => count($results),
            'backup_timestamp' => $backupTimestamp
        ];
    }
    
    /**
     * Verify database credentials replacement
     */
    public function verifyReplacement($newCredentials) {
        $currentCredentials = $this->getCurrentCredentials();
        $verified = true;
        $details = [];
        
        foreach ($newCredentials as $credType => $newValue) {
            if (empty($newValue)) continue;
            
            $found = false;
            foreach ($currentCredentials as $file => $fileCredentials) {
                if (isset($fileCredentials[$credType])) {
                    foreach ($fileCredentials[$credType] as $cred) {
                        if ($cred['value'] === $newValue) {
                            $found = true;
                            $details[] = "✅ {$credType} verified in " . basename($file);
                            break 2;
                        }
                    }
                }
            }
            
            if (!$found) {
                $verified = false;
                $details[] = "❌ {$credType} not found with new value";
            }
        }
        
        return [
            'success' => $verified,
            'details' => $details,
            'message' => $verified ? 'All credentials verified successfully' : 'Some credentials could not be verified'
        ];
    }

    /**
     * Simple text replacement across all database files
     */
    public function simpleTextReplace($oldText, $newText) {
        $results = [
            'total_changes' => 0,
            'total_files' => 0,
            'file_results' => []
        ];

        foreach ($this->files_to_scan as $file) {
            if (file_exists($file) && is_readable($file) && is_writable($file)) {
                $content = file_get_contents($file);
                $originalContent = $content;

                // Count occurrences before replacement
                $occurrences = substr_count($content, $oldText);

                if ($occurrences > 0) {
                    // Create backup
                    $backupFile = $file . '.backup.' . date('Y-m-d_H-i-s');
                    file_put_contents($backupFile, $originalContent);

                    // Replace text
                    $content = str_replace($oldText, $newText, $content);

                    // Write updated content
                    if (file_put_contents($file, $content)) {
                        $results['total_changes'] += $occurrences;
                        $results['total_files']++;
                        $results['file_results'][] = [
                            'file' => basename($file),
                            'changes' => $occurrences,
                            'backup' => $backupFile
                        ];
                    }
                }
            }
        }

        return $results;
    }
}

// Initialize database replacer
$dbReplacer = new DatabaseCredentialReplacer();
$currentCredentials = $dbReplacer->getCurrentCredentials();

// Handle form submissions
$message = '';
$messageType = '';
$replaceResults = [];
$verificationResults = [];

if ($_POST) {
    if (isset($_POST['action']) && $_POST['action'] === 'replace_credentials') {
        $newCredentials = [
            'db_host' => sanitizeInput($_POST['db_host'] ?? ''),
            'db_username' => sanitizeInput($_POST['db_username'] ?? ''),
            'db_password' => $_POST['db_password'] ?? '', // Don't sanitize password
            'db_name' => sanitizeInput($_POST['db_name'] ?? '')
        ];

        // Remove empty values
        $newCredentials = array_filter($newCredentials, function($value) {
            return !empty(trim($value));
        });

        if (!empty($newCredentials)) {
            $replaceResults = $dbReplacer->replaceCredentials($newCredentials);
            $verificationResults = $dbReplacer->verifyReplacement($newCredentials);

            if ($verificationResults['success']) {
                $message = "✅ Database credentials updated successfully! Made {$replaceResults['total_changes']} changes in {$replaceResults['total_files']} files.";
                $messageType = 'success';
            } else {
                $message = "⚠️ Credentials updated but verification failed. Please check the results below.";
                $messageType = 'warning';
            }

            // Refresh current credentials
            $currentCredentials = $dbReplacer->getCurrentCredentials();

            logSecurityEvent('Database credentials updated via installer', 'INFO', [
                'total_changes' => $replaceResults['total_changes'],
                'files_modified' => $replaceResults['total_files']
            ]);
        } else {
            $message = "⚠️ Please fill in at least one credential field.";
            $messageType = 'warning';
        }
    }

    if (isset($_POST['action']) && $_POST['action'] === 'text_replace') {
        $oldText = $_POST['old_text'] ?? '';
        $newText = $_POST['new_text'] ?? '';

        if (!empty($oldText) && !empty($newText)) {
            $textReplaceResults = $dbReplacer->simpleTextReplace($oldText, $newText);

            if ($textReplaceResults['total_changes'] > 0) {
                $message = "✅ Text replacement completed! Made {$textReplaceResults['total_changes']} changes in {$textReplaceResults['total_files']} files.";
                $messageType = 'success';
            } else {
                $message = "⚠️ No occurrences of '{$oldText}' found in database files.";
                $messageType = 'warning';
            }

            // Refresh current credentials
            $currentCredentials = $dbReplacer->getCurrentCredentials();

            logSecurityEvent('Database text replacement via installer', 'INFO', [
                'old_text' => $oldText,
                'new_text' => $newText,
                'total_changes' => $textReplaceResults['total_changes']
            ]);
        } else {
            $message = "❌ Both 'Find Text' and 'Replace With' fields are required.";
            $messageType = 'danger';
        }
    }
}

// Get the most common values for pre-filling the form
$commonCredentials = [];
foreach (['db_host', 'db_username', 'db_password', 'db_name'] as $credType) {
    $values = [];
    foreach ($currentCredentials as $fileCredentials) {
        if (isset($fileCredentials[$credType])) {
            foreach ($fileCredentials[$credType] as $cred) {
                $values[] = $cred['value'];
            }
        }
    }
    // Get the most common value
    if (!empty($values)) {
        $valueCounts = array_count_values($values);
        arsort($valueCounts);
        $commonCredentials[$credType] = key($valueCounts);
    }
}

// Display the page
echo getPageHeader('Database Configuration', 'Configure database credentials across all system files');
echo displayMessage($message, $messageType);
?>

<!-- Database Management Tabs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🗄️ Database Credential Manager</h5>

                <!-- Tab Navigation -->
                <ul class="nav nav-tabs mt-3" id="databaseTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                            🔄 General Replace
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="individual-tab" data-bs-toggle="tab" data-bs-target="#individual" type="button" role="tab" aria-controls="individual" aria-selected="false">
                            📁 Individual Files
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="text-replace-tab" data-bs-toggle="tab" data-bs-target="#text-replace" type="button" role="tab" aria-controls="text-replace" aria-selected="false">
                            📝 Text Replace
                        </button>
                    </li>
                </ul>
            </div>

            <div class="card-body">
                <!-- Tab Content -->
                <div class="tab-content" id="databaseTabContent">

                    <!-- General Replace Tab -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                        <p class="text-muted mb-4">
                            Update database credentials across all system files. The form is pre-filled with currently detected values.
                            Only modify the fields you want to change.
                        </p>

                <form method="post">
                    <input type="hidden" name="action" value="replace_credentials">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="db_host" class="form-label">Database Host</label>
                                <input type="text" class="form-control" id="db_host" name="db_host"
                                       placeholder="e.g., localhost"
                                       value="<?php echo htmlspecialchars($_POST['db_host'] ?? $commonCredentials['db_host'] ?? ''); ?>">
                                <div class="form-text">Database server hostname or IP address</div>
                            </div>

                            <div class="mb-3">
                                <label for="db_username" class="form-label">Database Username</label>
                                <input type="text" class="form-control" id="db_username" name="db_username"
                                       placeholder="e.g., banking_user"
                                       value="<?php echo htmlspecialchars($_POST['db_username'] ?? $commonCredentials['db_username'] ?? ''); ?>">
                                <div class="form-text">Database user account name</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="db_password" class="form-label">Database Password</label>
                                <input type="password" class="form-control" id="db_password" name="db_password"
                                       placeholder="Enter database password"
                                       value="<?php echo htmlspecialchars($_POST['db_password'] ?? $commonCredentials['db_password'] ?? ''); ?>">
                                <div class="form-text">Database user password</div>
                            </div>

                            <div class="mb-3">
                                <label for="db_name" class="form-label">Database Name</label>
                                <input type="text" class="form-control" id="db_name" name="db_name"
                                       placeholder="e.g., banking_system"
                                       value="<?php echo htmlspecialchars($_POST['db_name'] ?? $commonCredentials['db_name'] ?? ''); ?>">
                                <div class="form-text">Name of the database to connect to</div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <strong>💡 Selective Updates:</strong> Only fill in the fields you want to change. Empty fields will be ignored.
                        All changes will be backed up automatically.
                    </div>

                        <button type="submit" class="btn btn-primary"
                                onclick="return confirm('This will update database credentials across multiple files. Backups will be created. Continue?')">
                            🔄 Update Database Credentials
                        </button>
                    </form>
                    </div>

                    <!-- Individual Files Tab -->
                    <div class="tab-pane fade" id="individual" role="tabpanel" aria-labelledby="individual-tab">
                        <p class="text-muted mb-4">
                            View and edit database credentials for each file individually. This helps identify which files are not being updated correctly.
                        </p>

                        <div class="accordion" id="individualFilesAccordion">
                            <?php foreach ($dbReplacer->files_to_scan as $index => $file): ?>
                                <?php
                                $fileName = basename($file);
                                $fileCredentials = $currentCredentials[$file] ?? [];
                                $accordionId = "file-" . $index;
                                $hasCredentials = !empty($fileCredentials);
                                ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading-<?php echo $accordionId; ?>">
                                        <button class="accordion-button <?php echo $hasCredentials ? '' : 'collapsed'; ?>" type="button"
                                                data-bs-toggle="collapse" data-bs-target="#collapse-<?php echo $accordionId; ?>"
                                                aria-expanded="<?php echo $hasCredentials ? 'true' : 'false'; ?>"
                                                aria-controls="collapse-<?php echo $accordionId; ?>">
                                            <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                                <div>
                                                    <strong>📁 <?php echo htmlspecialchars($fileName); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($file); ?></small>
                                                </div>
                                                <div>
                                                    <?php if ($hasCredentials): ?>
                                                        <span class="badge bg-success">✅ Detected</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">⚠️ No Credentials</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </button>
                                    </h2>
                                    <div id="collapse-<?php echo $accordionId; ?>"
                                         class="accordion-collapse collapse <?php echo $hasCredentials ? 'show' : ''; ?>"
                                         aria-labelledby="heading-<?php echo $accordionId; ?>"
                                         data-bs-parent="#individualFilesAccordion">
                                        <div class="accordion-body">
                                            <?php if (empty($fileCredentials)): ?>
                                                <div class="alert alert-warning">
                                                    <strong>⚠️ No credentials detected</strong> - File may not exist or use different patterns
                                                </div>
                                            <?php else: ?>
                                                <div class="row">
                                                    <?php foreach (['db_host' => 'Host', 'db_username' => 'Username', 'db_password' => 'Password', 'db_name' => 'Database'] as $credType => $label): ?>
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label"><strong><?php echo $label; ?></strong></label>
                                                            <?php if (isset($fileCredentials[$credType])): ?>
                                                                <?php foreach ($fileCredentials[$credType] as $cred): ?>
                                                                    <div class="input-group mb-2">
                                                                        <input type="text" class="form-control"
                                                                               value="<?php echo htmlspecialchars($cred['value']); ?>" readonly>
                                                                        <span class="input-group-text">Line <?php echo $cred['line']; ?></span>
                                                                    </div>
                                                                    <div class="form-text small">
                                                                        <code><?php echo htmlspecialchars($cred['context']); ?></code>
                                                                    </div>
                                                                <?php endforeach; ?>
                                                            <?php else: ?>
                                                                <div class="text-muted small">Not detected</div>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Text Replace Tab -->
                    <div class="tab-pane fade" id="text-replace" role="tabpanel" aria-labelledby="text-replace-tab">
                        <p class="text-muted mb-4">
                            Simple text replacement for database values. Use this when the pattern-based replacement doesn't work.
                        </p>

                        <form method="post">
                            <input type="hidden" name="action" value="text_replace">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="old_text" class="form-label">Find Text</label>
                                        <input type="text" class="form-control" id="old_text" name="old_text"
                                               placeholder="e.g., u883539600_seabridge" required>
                                        <div class="form-text">Enter the old database value to find</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="new_text" class="form-label">Replace With</label>
                                        <input type="text" class="form-control" id="new_text" name="new_text"
                                               placeholder="e.g., new_database_name" required>
                                        <div class="form-text">Enter the new database value</div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-warning">
                                <strong>⚠️ Warning:</strong> This will replace ALL occurrences of the text in database files.
                                Be very specific with your search text to avoid unintended replacements.
                            </div>

                            <button type="submit" class="btn btn-warning"
                                    onclick="return confirm('This will replace all occurrences of the specified text. Continue?')">
                                📝 Replace Text
                            </button>
                        </form>
                    </div>

                </div> <!-- End tab-content -->
            </div>
        </div>
    </div>
</div>

<!-- Current Database Credentials -->
<?php if (!empty($currentCredentials)): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <button class="btn btn-link p-0 text-decoration-none text-dark w-100 text-start d-flex justify-content-between align-items-center"
                            type="button" data-bs-toggle="collapse" data-bs-target="#currentCredentialsCollapse"
                            aria-expanded="false" aria-controls="currentCredentialsCollapse">
                        <span>🔍 Current Database Credentials</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h5>
            </div>
            <div class="collapse" id="currentCredentialsCollapse">
                <div class="card-body">
                <?php
                $credTypes = [
                    'db_host' => 'Database Host',
                    'db_username' => 'Database Username',
                    'db_password' => 'Database Password',
                    'db_name' => 'Database Name'
                ];
                ?>

                <div class="accordion" id="credentialsAccordion">
                    <?php foreach ($credTypes as $credKey => $credLabel): ?>
                        <?php
                        $foundCreds = [];
                        foreach ($currentCredentials as $file => $fileCredentials) {
                            if (isset($fileCredentials[$credKey])) {
                                foreach ($fileCredentials[$credKey] as $cred) {
                                    $foundCreds[] = [
                                        'file' => $file,
                                        'cred' => $cred
                                    ];
                                }
                            }
                        }
                        ?>

                        <?php if (!empty($foundCreds)): ?>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading-cred-<?php echo $credKey; ?>">
                                <button class="accordion-button collapsed" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#collapse-cred-<?php echo $credKey; ?>"
                                        aria-expanded="false" aria-controls="collapse-cred-<?php echo $credKey; ?>">
                                    <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                        <strong><?php echo $credLabel; ?></strong>
                                        <span class="badge bg-info"><?php echo count($foundCreds); ?> found</span>
                                    </div>
                                </button>
                            </h2>
                            <div id="collapse-cred-<?php echo $credKey; ?>"
                                 class="accordion-collapse collapse"
                                 aria-labelledby="heading-cred-<?php echo $credKey; ?>"
                                 data-bs-parent="#credentialsAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <?php foreach ($foundCreds as $found): ?>
                                            <div class="col-md-6 mb-3">
                                                <div class="border rounded p-3">
                                                    <div class="mb-2">
                                                        <strong>Value:</strong>
                                                        <code class="bg-light p-1 rounded d-block">
                                                            <?php
                                                            if ($credKey === 'db_password') {
                                                                echo str_repeat('*', min(strlen($found['cred']['value']), 12));
                                                            } else {
                                                                echo htmlspecialchars($found['cred']['value']);
                                                            }
                                                            ?>
                                                        </code>
                                                    </div>
                                                    <div class="mb-2">
                                                        <small class="text-muted">
                                                            📁 <?php echo basename($found['file']); ?> (Line <?php echo $found['cred']['line']; ?>)
                                                        </small>
                                                    </div>
                                                    <div>
                                                        <small class="text-muted">Context:</small>
                                                        <code class="d-block bg-light p-2 rounded small">
                                                            <?php echo htmlspecialchars($found['cred']['context']); ?>
                                                        </code>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>



<!-- Replacement Results -->
<?php if (!empty($replaceResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>✅ Update Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <strong>Changes Applied:</strong> <?php echo $replaceResults['total_changes']; ?> changes in <?php echo $replaceResults['total_files']; ?> files
                </div>

                <div class="result-box">
                    <?php foreach ($replaceResults['results'] as $result): ?>
                        <div class="file-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6>📁 <?php echo basename($result['file']); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($result['file']); ?></small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success"><?php echo $result['changes']; ?> changes</span><br>
                                    <?php if ($result['backup']): ?>
                                        <small class="text-muted">Backup: <?php echo basename($result['backup']); ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Verification Results -->
<?php if (!empty($verificationResults)): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔍 Verification Results</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-<?php echo $verificationResults['success'] ? 'success' : 'warning'; ?>">
                    <strong><?php echo $verificationResults['message']; ?></strong>
                </div>

                <div class="result-box">
                    <?php foreach ($verificationResults['details'] as $detail): ?>
                        <div class="mb-1"><?php echo $detail; ?></div>
                    <?php endforeach; ?>
                </div>

                <div class="alert alert-info mt-3">
                    <strong>💡 Next Steps:</strong>
                    <ul class="mb-0">
                        <li>Test database connectivity with the new credentials</li>
                        <li>Verify that the banking system can connect to the database</li>
                        <li>Check that all database operations work correctly</li>
                        <li>Backup files are available for rollback if needed</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php echo getPageFooter(); ?>

<style>
/* Enhanced Collapse Arrow Styling */
.accordion-button .fas.fa-chevron-down {
    transition: transform 0.3s ease-in-out, color 0.2s ease;
    color: #007bff !important; /* Primary blue color for visibility */
    font-size: 14px;
    font-weight: bold;
}

.accordion-button:not(.collapsed) .fas.fa-chevron-down {
    transform: rotate(180deg);
    color: #28a745 !important; /* Green when expanded */
}

.accordion-button:hover .fas.fa-chevron-down {
    color: #0056b3 !important; /* Darker blue on hover */
}

.accordion-button:not(.collapsed):hover .fas.fa-chevron-down {
    color: #1e7e34 !important; /* Darker green when expanded and hovered */
}

/* Custom collapse button arrows */
[data-bs-toggle="collapse"] .fas.fa-chevron-down {
    transition: transform 0.3s ease-in-out, color 0.2s ease;
    color: #007bff !important; /* Primary blue color */
    font-size: 14px;
    font-weight: bold;
    margin-left: 8px;
}

[data-bs-toggle="collapse"]:not(.collapsed) .fas.fa-chevron-down,
[data-bs-toggle="collapse"][aria-expanded="true"] .fas.fa-chevron-down {
    transform: rotate(180deg);
    color: #28a745 !important; /* Green when expanded */
}

[data-bs-toggle="collapse"]:hover .fas.fa-chevron-down {
    color: #0056b3 !important; /* Darker blue on hover */
}

[data-bs-toggle="collapse"]:not(.collapsed):hover .fas.fa-chevron-down,
[data-bs-toggle="collapse"][aria-expanded="true"]:hover .fas.fa-chevron-down {
    color: #1e7e34 !important; /* Darker green when expanded and hovered */
}

/* Specific styling for Current Database Credentials button */
.btn-link .fas.fa-chevron-down {
    color: #6c757d !important; /* Secondary gray for main section */
    font-size: 16px;
    transition: transform 0.3s ease-in-out, color 0.2s ease;
}

.btn-link[aria-expanded="true"] .fas.fa-chevron-down {
    transform: rotate(180deg);
    color: #007bff !important; /* Blue when main section is expanded */
}

.btn-link:hover .fas.fa-chevron-down {
    color: #495057 !important; /* Darker gray on hover */
}

.btn-link[aria-expanded="true"]:hover .fas.fa-chevron-down {
    color: #0056b3 !important; /* Darker blue when expanded and hovered */
}

/* File item styling */
.file-item {
    border-left: 3px solid #007bff;
    padding-left: 15px;
}

.match-highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Fix for Bootstrap collapse conflicts */
.collapse:not(.show) {
    display: none;
}

.collapse.show {
    display: block;
}

.collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    transition: height 0.35s ease;
}

/* Additional visual enhancements for arrows */
.fas.fa-chevron-down {
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    filter: drop-shadow(0 1px 1px rgba(0,0,0,0.1));
}

/* Status-based arrow colors for Individual Files */
.accordion-item .accordion-button .fas.fa-chevron-down {
    color: #17a2b8 !important; /* Info blue for file sections */
}

.accordion-item .accordion-button:not(.collapsed) .fas.fa-chevron-down {
    color: #28a745 !important; /* Success green when file section expanded */
}

/* Warning state for files without credentials */
.accordion-item:has(.badge.bg-warning) .accordion-button .fas.fa-chevron-down {
    color: #ffc107 !important; /* Warning yellow for files without credentials */
}

.accordion-item:has(.badge.bg-warning) .accordion-button:not(.collapsed) .fas.fa-chevron-down {
    color: #fd7e14 !important; /* Orange when warning file expanded */
}
</style>

<script>
// Initialize Bootstrap 5 tabs and collapsible elements - AFTER Bootstrap loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, waiting for Bootstrap...');

    // Wait a bit for Bootstrap to fully initialize
    setTimeout(function() {
        console.log('Initializing tabs and collapsible elements...');

        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap is still not loaded!');
            return;
        }

        console.log('Bootstrap is ready, initializing components...');

        // Initialize tabs with proper Bootstrap 5 approach
        const tabTriggerList = document.querySelectorAll('#databaseTabs button[data-bs-toggle="tab"]');
        console.log('Found tab triggers:', tabTriggerList.length);

        if (tabTriggerList.length > 0) {
            tabTriggerList.forEach(function(tabTriggerEl) {
                console.log('Initializing tab:', tabTriggerEl.textContent.trim());

                // Let Bootstrap handle tab switching automatically
                tabTriggerEl.addEventListener('click', function(event) {
                    console.log('Tab clicked:', event.target.textContent.trim());
                });
            });
        }

        // Initialize collapsible elements properly
        const collapseButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
        console.log('Found collapse buttons:', collapseButtons.length);

        collapseButtons.forEach(function(button) {
            const targetId = button.getAttribute('data-bs-target');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                console.log('Setting up collapse for:', targetId);

                // Add event listeners for chevron rotation
                targetElement.addEventListener('show.bs.collapse', function() {
                    console.log('Showing collapse:', targetId);
                    const chevron = button.querySelector('.fa-chevron-down');
                    if (chevron) {
                        chevron.style.transform = 'rotate(180deg)';
                        chevron.style.transition = 'transform 0.3s ease';
                    }
                });

                targetElement.addEventListener('hide.bs.collapse', function() {
                    console.log('Hiding collapse:', targetId);
                    const chevron = button.querySelector('.fa-chevron-down');
                    if (chevron) {
                        chevron.style.transform = 'rotate(0deg)';
                        chevron.style.transition = 'transform 0.3s ease';
                    }
                });

                // Let Bootstrap handle the collapse functionality automatically
                button.addEventListener('click', function(event) {
                    console.log('Collapse button clicked:', targetId);
                });
            }
        });

        console.log('All components initialized successfully');
    }, 500); // Wait 500ms for Bootstrap to fully load
});
</script>

<?php include 'includes/footer.php'; ?>
