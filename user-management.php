<?php
/**
 * User Management Module
 * Manage installer credentials with encrypted JSON storage
 */

require_once 'includes/auth.php';
requireAuth();

class UserManager {
    
    private $credentials_file = 'includes/credentials.json';
    private $encryption_key;
    
    public function __construct() {
        // Generate encryption key based on server-specific data
        $this->encryption_key = hash('sha256', $_SERVER['SERVER_NAME'] . $_SERVER['DOCUMENT_ROOT'] . 'installer_key_2025');
    }
    
    /**
     * Get current installer credentials
     */
    public function getCurrentCredentials() {
        if (!file_exists($this->credentials_file)) {
            // Create default credentials file
            $default_credentials = [
                'password' => 'BankInstaller2025!',
                'created' => date('Y-m-d H:i:s'),
                'last_updated' => date('Y-m-d H:i:s'),
                'version' => '1.0'
            ];
            $this->saveCredentials($default_credentials);
            return $default_credentials;
        }
        
        $encrypted_data = file_get_contents($this->credentials_file);
        $decrypted_data = $this->decrypt($encrypted_data);
        
        if ($decrypted_data === false) {
            // If decryption fails, return default
            return [
                'password' => 'BankInstaller2025!',
                'created' => 'Unknown',
                'last_updated' => 'Unknown',
                'version' => '1.0'
            ];
        }
        
        return json_decode($decrypted_data, true);
    }
    
    /**
     * Update installer password
     */
    public function updatePassword($new_password) {
        $current_credentials = $this->getCurrentCredentials();
        
        $updated_credentials = [
            'password' => $new_password,
            'created' => $current_credentials['created'] ?? date('Y-m-d H:i:s'),
            'last_updated' => date('Y-m-d H:i:s'),
            'version' => '1.0'
        ];
        
        return $this->saveCredentials($updated_credentials);
    }
    
    /**
     * Save credentials to encrypted file
     */
    private function saveCredentials($credentials) {
        $json_data = json_encode($credentials, JSON_PRETTY_PRINT);
        $encrypted_data = $this->encrypt($json_data);
        
        return file_put_contents($this->credentials_file, $encrypted_data) !== false;
    }
    
    /**
     * Encrypt data
     */
    private function encrypt($data) {
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $this->encryption_key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Decrypt data
     */
    private function decrypt($encrypted_data) {
        $data = base64_decode($encrypted_data);
        if ($data === false) return false;
        
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, 'AES-256-CBC', $this->encryption_key, 0, $iv);
    }
    
    /**
     * Validate password strength
     */
    public function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = "Password must be at least 8 characters long";
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = "Password must contain at least one uppercase letter";
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = "Password must contain at least one lowercase letter";
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = "Password must contain at least one number";
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = "Password must contain at least one special character";
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * Get security information
     */
    public function getSecurityInfo() {
        $credentials = $this->getCurrentCredentials();
        
        return [
            'file_exists' => file_exists($this->credentials_file),
            'file_size' => file_exists($this->credentials_file) ? filesize($this->credentials_file) : 0,
            'file_permissions' => file_exists($this->credentials_file) ? substr(sprintf('%o', fileperms($this->credentials_file)), -4) : 'N/A',
            'created' => $credentials['created'] ?? 'Unknown',
            'last_updated' => $credentials['last_updated'] ?? 'Unknown',
            'encryption_method' => 'AES-256-CBC',
            'key_source' => 'Server-specific hash'
        ];
    }
}

// Initialize user manager
$userManager = new UserManager();
$currentCredentials = $userManager->getCurrentCredentials();
$securityInfo = $userManager->getSecurityInfo();

// Handle form submissions
$message = '';
$messageType = '';

if ($_POST) {
    if (isset($_POST['action']) && $_POST['action'] === 'update_password') {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Verify current password
        if ($current_password !== $currentCredentials['password']) {
            $message = "❌ Current password is incorrect.";
            $messageType = 'danger';
        }
        elseif ($new_password !== $confirm_password) {
            $message = "❌ New passwords do not match.";
            $messageType = 'danger';
        }
        else {
            // Validate new password
            $validation = $userManager->validatePassword($new_password);
            
            if (!$validation['valid']) {
                $message = "❌ Password validation failed:<br>• " . implode('<br>• ', $validation['errors']);
                $messageType = 'danger';
            }
            else {
                // Update password
                if ($userManager->updatePassword($new_password)) {
                    $message = "✅ Password updated successfully! Please use the new password for future logins.";
                    $messageType = 'success';
                    
                    // Refresh credentials
                    $currentCredentials = $userManager->getCurrentCredentials();
                    $securityInfo = $userManager->getSecurityInfo();
                    
                    logSecurityEvent('Installer password updated via user management', 'INFO', [
                        'timestamp' => date('Y-m-d H:i:s')
                    ]);
                } else {
                    $message = "❌ Failed to update password. Please check file permissions.";
                    $messageType = 'danger';
                }
            }
        }
    }
}

// Display the page
echo getPageHeader('User Management', 'Manage installer credentials and security settings');
echo displayMessage($message, $messageType);
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>👤 Installer User Management</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Manage installer authentication credentials with encrypted storage. All passwords are stored securely using AES-256-CBC encryption.</p>
            </div>
        </div>
    </div>
</div>

<!-- Current Credentials Information -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>🔐 Current Credentials</h5>
            </div>
            <div class="card-body">
                <div class="file-item">
                    <strong>Current Password:</strong><br>
                    <code><?php echo str_repeat('*', min(strlen($currentCredentials['password']), 20)); ?></code><br>
                    <small class="text-muted">Password is encrypted and masked for security</small>
                </div>
                
                <div class="file-item mt-3">
                    <strong>Password Details:</strong><br>
                    <small class="text-muted">
                        Length: <?php echo strlen($currentCredentials['password']); ?> characters<br>
                        Created: <?php echo htmlspecialchars($currentCredentials['created']); ?><br>
                        Last Updated: <?php echo htmlspecialchars($currentCredentials['last_updated']); ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>🛡️ Security Information</h5>
            </div>
            <div class="card-body">
                <div class="file-item">
                    <strong>Encryption:</strong> <?php echo $securityInfo['encryption_method']; ?><br>
                    <strong>Key Source:</strong> <?php echo $securityInfo['key_source']; ?><br>
                    <strong>File Size:</strong> <?php echo number_format($securityInfo['file_size']); ?> bytes<br>
                    <strong>Permissions:</strong> <?php echo $securityInfo['file_permissions']; ?><br>
                    <strong>File Status:</strong> 
                    <?php if ($securityInfo['file_exists']): ?>
                        <span class="badge bg-success">Exists</span>
                    <?php else: ?>
                        <span class="badge bg-warning">Missing</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Password Change Form -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5>🔑 Change Installer Password</h5>
            </div>
            <div class="card-body">
                <form method="POST" id="passwordForm">
                    <input type="hidden" name="action" value="update_password">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                                <div class="form-text">Enter your current installer password</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <div class="form-text">
                                    Password requirements:
                                    <ul class="small mt-1">
                                        <li>At least 8 characters long</li>
                                        <li>Contains uppercase and lowercase letters</li>
                                        <li>Contains at least one number</li>
                                        <li>Contains at least one special character</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <div class="form-text">Re-enter your new password</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <strong>⚠️ Important:</strong> After changing the password, you will need to use the new password for all future installer access. Make sure to remember or securely store your new password.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key"></i> Update Password
                            </button>
                            <a href="index.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;

    if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('New passwords do not match!');
        return false;
    }

    if (newPassword.length < 8) {
        e.preventDefault();
        alert('Password must be at least 8 characters long!');
        return false;
    }

    if (!confirm('Are you sure you want to change the installer password? You will need to use the new password for future access.')) {
        e.preventDefault();
        return false;
    }
});
</script>

<?php echo getPageFooter(); ?>

<?php include 'includes/footer.php'; ?>
