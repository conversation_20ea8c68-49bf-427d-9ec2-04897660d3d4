-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: Jul 23, 2025 at 11:50 AM
-- Server version: 10.11.10-MariaDB-log
-- PHP Version: 7.2.34

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `u731735874_seabridge`
--

-- --------------------------------------------------------

--
-- Table structure for table `account`
--

CREATE TABLE `account` (
  `id` int(10) NOT NULL,
  `cs_id` varchar(200) DEFAULT NULL,
  `cs_uname` varchar(100) DEFAULT NULL,
  `cs_pass` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `type` varchar(100) DEFAULT NULL,
  `fname` varchar(100) DEFAULT NULL,
  `lname` varchar(100) DEFAULT NULL,
  `addr` varchar(100) DEFAULT NULL,
  `work` varchar(100) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `dob` varchar(30) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `phone_verify` tinyint(4) DEFAULT 0,
  `tmp_otp` varchar(100) DEFAULT NULL,
  `reg_date` varchar(25) DEFAULT NULL,
  `marry` varchar(20) DEFAULT NULL,
  `t_bal` decimal(20,2) DEFAULT NULL,
  `a_bal` decimal(20,2) DEFAULT NULL,
  `btc_bal` decimal(16,8) NOT NULL,
  `vc_bal` decimal(20,2) NOT NULL,
  `logins` int(50) DEFAULT 0,
  `status` enum('ACTIVE','DORMANT/INACTIVE','DISABLED','CLOSED','SUSPEND','ON HOLD') DEFAULT 'ACTIVE',
  `currency` varchar(50) DEFAULT NULL,
  `auth_code_1` varchar(20) DEFAULT NULL,
  `auth_code_2` varchar(20) DEFAULT NULL,
  `auth_code_3` varchar(20) DEFAULT NULL,
  `auth_code_4` varchar(20) NOT NULL,
  `auth_code_text_1` varchar(225) NOT NULL,
  `auth_code_text_2` varchar(225) NOT NULL,
  `auth_code_text_3` varchar(225) NOT NULL,
  `auth_code_text_4` varchar(225) NOT NULL,
  `pin_auth` varchar(222) DEFAULT NULL,
  `verify` varchar(40) DEFAULT NULL,
  `resetComplete` varchar(4) NOT NULL DEFAULT 'no',
  `billing_code` tinyint(2) DEFAULT 1,
  `cardno` varchar(20) DEFAULT NULL,
  `expmonth` varchar(2) DEFAULT NULL,
  `expyear` varchar(2) DEFAULT NULL,
  `cvv` varchar(3) DEFAULT NULL,
  `btc_w` varchar(500) DEFAULT NULL,
  `btc_cwallet` varchar(225) DEFAULT NULL,
  `eth_cwallet` varchar(225) DEFAULT NULL,
  `usdt_cwallet` varchar(225) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `account`
--

INSERT INTO `account` (`id`, `cs_id`, `cs_uname`, `cs_pass`, `email`, `type`, `fname`, `lname`, `addr`, `work`, `sex`, `dob`, `phone`, `phone_verify`, `tmp_otp`, `reg_date`, `marry`, `t_bal`, `a_bal`, `btc_bal`, `vc_bal`, `logins`, `status`, `currency`, `auth_code_1`, `auth_code_2`, `auth_code_3`, `auth_code_4`, `auth_code_text_1`, `auth_code_text_2`, `auth_code_text_3`, `auth_code_text_4`, `pin_auth`, `verify`, `resetComplete`, `billing_code`, `cardno`, `expmonth`, `expyear`, `cvv`, `btc_w`, `btc_cwallet`, `eth_cwallet`, `usdt_cwallet`) VALUES
(1, '**********', 'Jamesbong01', '123456', '<EMAIL>', 'Savings', 'James', 'b', '112 kent Road', 'Manager', 'Male', '11/11/1965', '************', 1, '319253', '11/11/1911', 'Single', -956743.32, 43256.68, 5878.********, 100000.00, 0, 'ACTIVE', '$', '101001', '101001', '101001', '101001', 'IRS RESTRICTION', 'Custom Code1', 'Custom Code2', 'IRS RESTRICTION', '84522', 'Y', 'no', 0, '5555 0000 8888 6666', '11', '20', '222', '74774747474774747477474', 'wowoowoowowowowowowowowowowoowowow', '', ''),
(9, '8335446190', 'Novaumbral001', '348127', '<EMAIL>', 'Savings', 'Nova', 'Umbral', '11 Foever Road', 'Manager', 'Male', '11/11/1966', '1818161111', 2, '299527', '2024-10-15 19:24:17', 'Single', 0.00, 1000000.00, 1.00000000, 500000.00, 0, 'ACTIVE', '€', '28430', '77969', '75419', '86672', '', '', '', '', '4230', 'Y', 'no', 1, '4251 5862 2368 2416', '06', '29', '547', '****************************', NULL, NULL, NULL),
(10, '8886330705', 'victory1756', '892121', '<EMAIL>', 'Savings', 'Victory', 'Bolb', '11 Kent Road', 'Manager', 'Male', '11/11/1956', '1*********', 0, NULL, '2024-10-19 08:33:36', 'Single', 0.00, 0.00, 0.00000000, 0.00, 0, 'CLOSED', '$', '19234', '84680', '84528', '25690', 'COT', 'IMF', 'TAX', 'ATC', '2721', 'N', 'no', 1, NULL, NULL, NULL, NULL, '11 kent Road', NULL, NULL, NULL),
(11, '4496249739', 'brokenigga1', '123456', '<EMAIL>', 'Checking', 'broke', 'nigga', '11 kent road', '11 kent', 'Male', '11/11/1978', '11 kent road', 1, '177829', '11/11/1976', 'Single', 0.00, 0.00, 0.00000000, 0.00, 0, 'ACTIVE', '€', '', '', '', '', '', '', '', '', '89933', 'Y', 'no', 1, NULL, NULL, NULL, NULL, '', NULL, NULL, NULL),
(12, '8415444505', 'robertmathews4', 'robertmathews', '<EMAIL>', 'Savings', 'Robert', 'Mathews', '3008 E 103rd St, Tulsa, OK 74137', 'Engineering', 'Male', '05/05/1950', '5594097870', 2, '107764', '2019', 'Widowed', -66000.00, 24934000.00, 150001.00000000, 200000.00, 0, 'ACTIVE', '$', '', '', '', '', '', '', '', '', '89673', 'Y', 'no', 1, '4251 5862 2368 2416', '09', '20', '899', '', NULL, NULL, NULL),
(13, '**********', 'ch.sum8383', 'songsofpraise3@', '<EMAIL>', 'Checking', 'Summers', 'Arts&Antiques', '600 Bartlett St, Huntington, IN 46750', 'Self Employed', 'Female', '08-03-1985', '+1260345719', 2, '173502', '07-08-2024', 'Widowed', -2317052.51, 2073816.66, 5.04366800, 53576.08, 0, 'SUSPEND', '$', '33177', '10527', '33662', '50988', 'Transfer', 'Transfer', 'Transfer', 'Transfer', '36328', 'Y', 'no', 4, ' 5447684131792732 ', '06', '27', '323', '', NULL, NULL, NULL),
(14, '**********', 'Joanmur1', '123456', '<EMAIL>', 'Savings', 'Joan', 'Totti', '3911 Kilbourne Rd, Columbia, SC 29205', 'Interior decorator', 'Female', '15/11/1974', '+1850-631-4880', 2, '211713', '12/5/2015', 'Widowed', ********.00, ********.00, 0.00000000, 0.00, 0, 'ACTIVE', '$', '', '', '', '', '', '', '', '', '70307', 'Y', 'no', 1, '4662 6237 2233 8175', '05', '25', '649', '', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `admin`
--

CREATE TABLE `admin` (
  `id` int(10) NOT NULL,
  `cs_uname` varchar(40) NOT NULL,
  `cs_pass` varchar(40) NOT NULL,
  `email` varchar(100) NOT NULL,
  `verified_count` enum('Y','N') DEFAULT 'Y'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `admin`
--

INSERT INTO `admin` (`id`, `cs_uname`, `cs_pass`, `email`, `verified_count`) VALUES
(1, 'admin', '9d37bdf3a7b57cd5a8c575c1a1aeb0d2', '<EMAIL>', 'Y');

-- --------------------------------------------------------

--
-- Table structure for table `alerts`
--

CREATE TABLE `alerts` (
  `id` int(100) NOT NULL,
  `cs_uname` varchar(40) NOT NULL,
  `amount` decimal(20,2) NOT NULL,
  `sender_name` varchar(40) NOT NULL,
  `type` varchar(10) NOT NULL,
  `remarks` varchar(100) NOT NULL,
  `date` varchar(20) NOT NULL,
  `time` varchar(20) NOT NULL,
  `email` varchar(255) NOT NULL,
  `statz` varchar(200) DEFAULT 'Successful'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `alerts`
--

INSERT INTO `alerts` (`id`, `cs_uname`, `amount`, `sender_name`, `type`, `remarks`, `date`, `time`, `email`, `statz`) VALUES
(1, '**********', 50000.00, 'Duke PopCorn', 'Credit', 'Flight', '11/11/2022', '11:11:11', '', 'Successful'),
(3, '**********', 946657.00, 'Duncan Mighty', 'Credit', 'Music lecord', '11/11/1911', '11:11', '', 'Successful'),
(4, '**********', 98777.00, 'Legacy', 'Debit', 'Ahmefua Records', '11/11/1911', '11:11', '', 'Successful'),
(5, '**********', 50000.00, 'Miraj', 'Credit', 'Flight', '11/11/1911', '11:11', '', 'Successful'),
(6, '**********', 1.00, 'Stoneage', 'Debit', 'Stones', '09/11/2024', '11:11', '', 'Successful'),
(7, '**********', 1892879.00, 'Paul Loook', 'Debit', 'Flight Commition', '09/11/2023', '11:11', '', 'Successful'),
(8, '**********', 1892879.00, 'Solution Limited', 'Credit', 'Music Deal', '09/09/2024', '11:11:11', '', 'Successful'),
(9, '**********', 500000.00, 'Arlette M', 'Credit', 'Management Fee', '11/11/2024', '11:11', '', 'Successful'),
(10, '**********', 5000.00, '<EMAIL>/**********', 'Credit', 'f', '12 Sep 2024 05:05 PM', '', '', 'Successful'),
(11, '***********', 5000.00, '<EMAIL>/**********', 'Credit', 'carx', '14 Sep 2024 02:15 PM', '', '', 'Successful'),
(12, '**********', 6000.00, 'rrtrrrrrrrrrrrrrrrrrrrrrrrrrrrrr', 'Credit', 'credit bitcoin', '11/11/2024', '11:11', '', 'Successful'),
(13, '8415444505', 100000.00, 'Isabella', 'Credit', 'California Cove property', '19-01-2019', '10.19', '', 'Successful'),
(14, '8415444505', 150000.00, 'shaw', 'Credit', 'Alexigros Engergy CY', '19-10-2019', '09.28', '', 'Successful'),
(15, '8415444505', 77000.00, 'James', 'Debit', 'AGERA ENERGY', '19-12-2019', '13\'20', '', 'Successful'),
(16, '8415444505', 89000.00, 'Isabella', 'Debit', 'Alexigros Engergy CY', '30-12-2019', '11\'27', '', 'Successful'),
(17, '**********', 2800524.73, 'Inventory Purchase', 'Credit', 'Wire Transfer', '07-08-2024', '11:42', '', 'Successful'),
(18, '**********', 450782.37, 'European Antiques Lot', 'Credit', 'Sales Deposit', '07-15-2024', '10:20', '', 'Successful'),
(19, '**********', 15238.50, 'High-End Collectors', 'Debit', 'Marketing Campaign', '07-18-2024', '15:50', '', 'Successful'),
(20, '**********', 124532.40, 'Vintage Jewelry &amp; Art', 'Credit', 'Sales Deposit', '07-22-2024', '09:20', '', 'Successful'),
(21, '**********', 300689.73, 'Sculptures Set', 'Debit', 'Inventory Purchase', '07-25-2024', '10:33', '', 'Successful'),
(22, '**********', 224954.67, 'Gemstone Collection', 'Credit', 'Sales Deposit', '08-02-2024', '13:44', '', 'Successful'),
(23, '**********', 125413.99, 'Antique Furniture', 'Debit', 'Inventory Purchase', '08-10-2024', '09:34', '', 'Successful'),
(25, '**********', 20475.85, 'Auction', 'Debit', 'Auction Fee', '08-20-2024', '15:22', '', 'Successful'),
(26, '**********', 400742.15, 'Rare Artifact Lot', 'Credit', 'Sales Deposit', '08-25-2024', '09:56', '', 'Successful'),
(27, '**********', 200328.41, 'Exclusive Jewelry', 'Debit', 'Inventory Purchase', '09-05-2024', '12:02', '', 'Successful'),
(28, '**********', 174589.74, 'Private Collection', 'Credit', 'Sales Deposit', '09-12-2024', '12:36', '', 'Successful'),
(29, '**********', 49834.20, 'Antique Show', 'Debit', 'Event Sponsorship', '09-14-2024', '10:08', '', 'Successful'),
(30, '**********', 200168.53, 'High-End Antique Pieces', 'Credit', 'Sales Deposit', '09-20-2024', '09:28', '', 'Successful'),
(31, '**********', 149672.89, 'Gemstones', 'Debit', 'Inventory Purchase', '09-22-2024', '09:16', '', 'Successful'),
(32, '**********', 30512.12, 'Gallery Rental', 'Debit', 'Client Presentation', '09-30-2024', '14:27', '', 'Successful'),
(33, '**********', 299821.67, 'Curated Art Collection', 'Credit', 'Sales Deposit', '10-01-2024', '10:17', '', 'Successful'),
(34, '**********', 199756.48, 'Vintage Carvings', 'Debit', 'Inventory Purchase', '10-10-2024', '09:12', '', 'Successful'),
(35, '**********', 224455.36, 'Historical Antiques Set', 'Credit', 'Sales Deposit', '10-15-2024', '10:42', '', 'Successful'),
(36, '**********', 44213.79, 'Business Development', 'Debit', 'VIP Event', '02-25-2025', '15:32', '', 'Successful'),
(37, '**********', 9684.32, 'Account Maintenance', 'Debit', 'Bank Transaction Fee', '03-24-2025', '09:02', '', 'Successful'),
(39, '**********', 1040654.00, 'Turkish Auctioneers', 'Debit', 'Purchases', '10-30-2024', '13:27', '', 'Successful'),
(40, '**********', 20.00, 'Wire Transfer Fee', 'Debit', 'Wire Transfer Fee', '03-26-2025', '13:27', '', 'Successful'),
(41, '**********', ********.00, 'Steve Totti', 'Credit', 'Pension Plan Payment - November 2024 (SEABRIDGE) - (Electronic Transfer to  Savings Account,', '04/11/2024', '14:30:00', '', 'Successful');

-- --------------------------------------------------------

--
-- Table structure for table `bank_payment_settings`
--

CREATE TABLE `bank_payment_settings` (
  `id` int(11) NOT NULL,
  `bk_name` varchar(255) NOT NULL,
  `bk_address` varchar(255) NOT NULL,
  `bk_account` varchar(255) NOT NULL,
  `account_name` varchar(255) NOT NULL,
  `swift_code` varchar(255) NOT NULL,
  `routing_no` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `bank_payment_settings`
--

INSERT INTO `bank_payment_settings` (`id`, `bk_name`, `bk_address`, `bk_account`, `account_name`, `swift_code`, `routing_no`) VALUES
(1, 'Seabridge Credit Bank', 'Italy', '*************', 'Willimas Kelly', 'UPNBUS44', '9147219-3292');

-- --------------------------------------------------------

--
-- Table structure for table `bk_settings`
--

CREATE TABLE `bk_settings` (
  `id` int(255) NOT NULL,
  `bk_name` varchar(255) NOT NULL,
  `bk_domain` varchar(255) NOT NULL,
  `bk_email` varchar(255) NOT NULL,
  `bk_phone` varchar(255) NOT NULL,
  `bk_address` varchar(255) NOT NULL,
  `admin_btc_wallet` varchar(255) DEFAULT '' COMMENT 'Admin Bitcoin wallet address',
  `admin_eth_wallet` varchar(255) DEFAULT '' COMMENT 'Admin Ethereum wallet address',
  `admin_usdt_wallet` varchar(255) DEFAULT '' COMMENT 'Admin USDT wallet address'
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci ROW_FORMAT=FIXED;

--
-- Dumping data for table `bk_settings`
--

INSERT INTO `bk_settings` (`id`, `bk_name`, `bk_domain`, `bk_email`, `bk_phone`, `bk_address`, `admin_btc_wallet`, `admin_eth_wallet`, `admin_usdt_wallet`) VALUES
(1, 'Seabridge Credit Bank', 'https://seabridgecrditb.com', '<EMAIL>', '+*********', '19, 00-252 Warszawa, Poland', '**********************************', '******************************************', '******************************************');

-- --------------------------------------------------------

--
-- Table structure for table `cheque_deposits`
--

CREATE TABLE `cheque_deposits` (
  `id` int(11) NOT NULL,
  `cheque_number` varchar(50) NOT NULL,
  `deposit_date` date NOT NULL,
  `sender_name` varchar(100) NOT NULL,
  `payee_name` varchar(100) NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `currency` varchar(10) NOT NULL,
  `account_number` varchar(50) NOT NULL,
  `account_type` varchar(50) NOT NULL,
  `branch_code` varchar(50) NOT NULL,
  `id_passport_number` varchar(50) NOT NULL,
  `bank_name` varchar(100) NOT NULL,
  `cheque_image` varchar(255) NOT NULL,
  `clearance_status` enum('pending','cleared','returned') DEFAULT 'pending',
  `email` varchar(100) DEFAULT NULL,
  `fname` varchar(100) DEFAULT NULL,
  `lname` varchar(100) DEFAULT NULL,
  `cs_uname` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cheque_deposits`
--

INSERT INTO `cheque_deposits` (`id`, `cheque_number`, `deposit_date`, `sender_name`, `payee_name`, `amount`, `currency`, `account_number`, `account_type`, `branch_code`, `id_passport_number`, `bank_name`, `cheque_image`, `clearance_status`, `email`, `fname`, `lname`, `cs_uname`, `created_at`, `updated_at`) VALUES
(1, 'CK123456', '2024-10-17', 'Emily J. Wilson', 'David M. Lee', 50000.00, '€', '**********', 'Savings', '*********', 'PASS123456', 'Bank of New York', 'https://seabridgecrditb.com/banking/cheque_photo/670d1fb4c3ff5_cheque.jpeg', 'cleared', NULL, NULL, NULL, NULL, '2024-10-14 13:42:12', '2024-10-15 05:06:07'),
(2, 'CK123456', '2024-10-16', 'Emily J. Wilson', 'James b', 500000.00, '€', '**********', 'Savings', '81742', 'PASS123456', 'Bank of New York', 'https://seabridgecrditb.com/banking/cheque_photo/670d37e854291_cheque_pix.jpeg', 'pending', '<EMAIL>', 'James', 'b', 'Jamesbong01', '2024-10-14 15:25:28', '2024-10-14 15:25:28');

-- --------------------------------------------------------

--
-- Table structure for table `crypto`
--

CREATE TABLE `crypto` (
  `id` int(255) NOT NULL,
  `cs_id` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `rwallet` varchar(255) DEFAULT NULL,
  `swallet` varchar(255) DEFAULT NULL,
  `amt` varchar(255) DEFAULT NULL,
  `date` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `ref` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `crypto`
--

INSERT INTO `crypto` (`id`, `cs_id`, `email`, `rwallet`, `swallet`, `amt`, `date`, `status`, `ref`) VALUES
(1, '**********', '<EMAIL>', '74774747474774747477474', '2222222222222222222222222222222222', '1.1', '12:28 PM 13 Oct 2024', 'Pending', '********-**********'),
(2, '**********', '<EMAIL>', '74774747474774747477474', '18188181818818181', '1.1', '12:36 PM 13 Oct 2024', 'Pending', '1440371409-1529923725'),
(3, '**********', '<EMAIL>', '74774747474774747477474', '17177171771771', '1.1', '03:08 PM 13 Oct 2024', 'Pending', '1267991822-875225318'),
(4, '**********', '<EMAIL>', '74774747474774747477474', '65rfdcgjfvkjghlnoiuygftu7civkuyhj.', '0.18334546', '05:54 PM 22 Jan 2025', 'Pending', '894751862-309310054'),
(5, '**********', '<EMAIL>', '74774747474774747477474', 'rskkvnfam\'s;lriuilag\'ohenw', '123', '10:13 AM 23 Jan 2025', 'Pending', '863352601-*********');

-- --------------------------------------------------------

--
-- Table structure for table `forgot_pass`
--

CREATE TABLE `forgot_pass` (
  `email` mediumtext NOT NULL,
  `token` varchar(9999) NOT NULL,
  `expiry` varchar(8999) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `live_transfer`
--

CREATE TABLE `live_transfer` (
  `id` int(255) NOT NULL,
  `beneficiary_account_no` varchar(255) NOT NULL,
  `beneficiary_ifsc` varchar(255) NOT NULL,
  `amount` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `message`
--

CREATE TABLE `message` (
  `id` int(10) NOT NULL,
  `sender_name` varchar(40) NOT NULL,
  `reci_name` varchar(40) NOT NULL,
  `subject` varchar(100) NOT NULL,
  `msg` varchar(2000) NOT NULL,
  `read` enum('unread','opened') DEFAULT 'unread',
  `date` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `message`
--

INSERT INTO `message` (`id`, `sender_name`, `reci_name`, `subject`, `msg`, `read`, `date`) VALUES
(1, 'Customer Care', 'Jamesbong01', 'We heard', 'we got your message', 'unread', '2024-09-16 18:05:02');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_temp`
--

CREATE TABLE `password_reset_temp` (
  `email` varchar(250) NOT NULL,
  `key` varchar(250) NOT NULL,
  `expDate` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `temp_account`
--

CREATE TABLE `temp_account` (
  `id` int(10) NOT NULL,
  `cs_pass` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `type` varchar(100) NOT NULL,
  `fname` varchar(100) NOT NULL,
  `mname` varchar(100) NOT NULL,
  `lname` varchar(100) NOT NULL,
  `addr` varchar(100) NOT NULL,
  `work` varchar(100) NOT NULL,
  `sex` varchar(10) NOT NULL,
  `dob` varchar(30) NOT NULL,
  `ip` varchar(30) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `reg_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `marry` varchar(20) NOT NULL,
  `currency` varchar(5) NOT NULL,
  `code` varchar(6) NOT NULL,
  `verify` enum('Y','N') DEFAULT 'N'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `temp_transfer`
--

CREATE TABLE `temp_transfer` (
  `id` int(100) NOT NULL,
  `email` varchar(40) DEFAULT NULL,
  `amount` decimal(20,2) DEFAULT NULL,
  `bacc_id` varchar(300) DEFAULT NULL,
  `acc_name` varchar(60) DEFAULT NULL,
  `bk_name` varchar(40) DEFAULT NULL,
  `type` varchar(20) DEFAULT NULL,
  `swift` varchar(200) DEFAULT NULL,
  `routing` varchar(200) DEFAULT NULL,
  `remarks` text DEFAULT NULL,
  `cout` varchar(1000) DEFAULT NULL,
  `transtype` varchar(1000) DEFAULT NULL,
  `date` varchar(200) DEFAULT NULL,
  `ref` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `temp_transfer`
--

INSERT INTO `temp_transfer` (`id`, `email`, `amount`, `bacc_id`, `acc_name`, `bk_name`, `type`, `swift`, `routing`, `remarks`, `cout`, `transtype`, `date`, `ref`) VALUES
(1, '<EMAIL>', 5000.00, '**********', 'Kames Bong', 'Huj Luke', 'Checking', '742494', '***********01', 'Funds', 'Poland', 'Domestic Transfer', '11 Sep 2024 04:30 PM', 'ALB-0EEO34DRH5T090'),
(2, '<EMAIL>', 50000.00, '**********', 'Seabridge', 'Seabridge', 'Savings', '************', '**********', 'Funds Movies', 'Antigua &amp; Barbuda', 'Wire Transfer', '11 Sep 2024 04:50 PM', 'ALB-ER40ED9T0H50O3'),
(3, '<EMAIL>', 0.00, '222222', '222', '222', 'Savings', '378989', '22', 'f', 'Poland', 'Domestic Transfer', '12 Sep 2024 03:28 PM', 'ALB-3HD094E0E5TRO0'),
(4, '<EMAIL>', 566.00, '*********', 'fff', 'ff', 'Savings', '102031', 'fff', 'ff', 'Poland', 'Domestic Transfer', '12 Sep 2024 03:36 PM', 'ALB-R4E0530H0TED9O'),
(5, '<EMAIL>', 5000.00, '**********', 'Brown Miraje', 'Seabridge Credit Bank', 'Savings', '748814', '', 'ff', 'Poland', 'Inter-Bank Transfer', '12 Sep 2024 04:35 PM', 'ALB-D90T30R05OE4HE'),
(6, '<EMAIL>', 5000.00, '**********', 'Brown Miraje', 'Seabridge Credit Bank', 'Savings', '718502', '', 'f', 'Poland', 'Inter-Bank Transfer', '12 Sep 2024 04:49 PM', 'ALB-T0D4RE0HO3095E'),
(7, '<EMAIL>', 5000.00, '**********', 'Brown Miraje', 'Seabridge Credit Bank', 'Savings', '214567', '', 'f', 'Poland', 'Inter-Bank Transfer', '12 Sep 2024 05:05 PM', 'ALB-EE0H4ORT3050D9'),
(8, '<EMAIL>', 5000.00, '**********', 'Brown Miraje', 'Seabridge Credit Bank', 'Savings', '318973', '', 'f', 'Poland', 'Inter-Bank Transfer', '12 Sep 2024 05:47 PM', 'ALB-9ER0T00H5O4ED3'),
(9, '<EMAIL>', 5000.00, '**********', 'Brown Miraje', 'Seabridge Credit Bank', 'Savings', '793603', '', 'FFF', 'Poland', 'Inter-Bank Transfer', '12 Sep 2024 06:18 PM', 'ALB-DE4305H09RE0TO'),
(10, '<EMAIL>', 70098.00, '1*************', 'tHE wEEknd', 'Beer Beer Bank', 'Checking', '963409', '***********', 'Lecord Deal-Emeka Oil Business', 'Poland', 'Domestic Transfer', '13 Sep 2024 06:57 AM', 'ALB-D005E43TOHE90R'),
(11, '<EMAIL>', 87555.00, '***********', 'tHE Weeknd', 'Beer Beer bank', 'Checking', '929049', '***********', 'Lecord Musica', 'Poland', 'Domestic Transfer', '13 Sep 2024 07:13 AM', 'ALB-E0OTRED3H59400'),
(12, '<EMAIL>', 5000.00, '********', 'The Weekend', 'Beer Pong Bank', 'Checking', '691363', '***********01', 'Musica Records', 'Poland', 'Domestic Transfer', '13 Sep 2024 07:43 AM', 'ALB-DR94E00035HEOT'),
(13, '<EMAIL>', 22.00, '222', '22', '22', 'Savings', '446548', '22', '22', 'Poland', 'Domestic Transfer', '13 Sep 2024 08:10 AM', 'ALB-E5HO04T3E09R0D'),
(14, '<EMAIL>', 455.00, '**********', '***********', '**********', 'Savings', '806785', '5555', '5555', 'Poland', 'Domestic Transfer', '13 Sep 2024 08:22 AM', 'ALB-30ET5H40DE9OR0'),
(15, '<EMAIL>', 5000.00, '***********', 'BakLuks', '101001', 'Savings', '265758', '11111', 'carx', 'Poland', 'Domestic Transfer', '14 Sep 2024 02:15 PM', 'ALB-RT405DO0390EEH'),
(16, '<EMAIL>', 5999.00, '20********', 'Nick Ma', 'Maze Bank', 'Savings', '299491', '********01', 'Funds', 'Poland', 'Domestic Transfer', '14 Sep 2024 02:38 PM', 'ALB-RO500E93HDE4T0'),
(17, '<EMAIL>', 2000.00, '*********', '********', '1010101', 'Savings', '1010101', '1101001', 'Fanx', 'Belize', 'Wire Transfer', '14 Sep 2024 04:12 PM', 'ALB-H4O0RE005T3E9D'),
(18, '<EMAIL>', 5000.00, '********', 'Mike Chucks', 'BOA', 'Savings', '', '********', 'Help', 'Belarus', 'Wire Transfer', '16 Sep 2024 05:03 PM', 'ALB-4TH539EOD00E0R'),
(19, '<EMAIL>', 1040654.00, '**********', 'Turkish Auctioneers', 'Finansback', 'Checking', 'Fnnbtris', '8309', 'Purchased', 'Turkey', 'Wire Transfer', '30 Oct 2024 06:25 PM', 'ALB-0E49EDH0ROT350'),
(20, '<EMAIL>', 1040654.00, '**********', 'Turkish Auctioneers', 'Finansbank', 'Checking', 'FNNBTRIS', '8309', 'Purchases', 'Turkey', 'Wire Transfer', '30 Oct 2024 06:50 PM', 'ALB-H59REE3DO00T40'),
(21, '<EMAIL>', 100.00, '**********', 'Min Of Trade', 'AKBANK', 'Checking', 'AKBKTRIS', '2788', 'Payment', 'Turkey', 'Wire Transfer', '31 Oct 2024 11:01 AM', 'ALB-RT3005E40EDHO9'),
(22, '<EMAIL>', 100.00, '**********', 'Min of Trade', 'AKBANK', 'Checking', 'AKBKTRIS', '6288', 'Payment', 'Turkey', 'Wire Transfer', '31 Oct 2024 11:11 AM', 'ALB-O9T05E4H0DE30R'),
(23, '<EMAIL>', 100.00, '**********', 'Min of trade', 'AKBANK', 'Checking', 'AKBKTRIS', '6277', 'Payment', 'Turkey', 'Wire Transfer', '31 Oct 2024 01:23 PM', 'ALB-ROT3EE0950H04D'),
(24, '<EMAIL>', 10.00, '**********', 'Cyprus TX', 'USB Bank', 'Checking', '', 'UNVKCY2N', 'Tax', 'Cyprus', 'Wire Transfer', '07 Apr 2025 12:00 PM', 'ALB-0OE49HTD030ER5'),
(25, '<EMAIL>', 15287.00, '**********', 'Cyprus TX', 'USB Bank', 'Savings', '', 'UNVKCY2N', 'TX', 'Cyprus', 'Wire Transfer', '07 Apr 2025 12:10 PM', 'ALB-T0OE905H0R43DE'),
(26, '<EMAIL>', 23.00, '**********', 'Ministry of Fin', 'USB BANK PLC', 'Checking', 'UNVKCY2N', '8309', 'TP09910', 'Cyprus', 'Wire Transfer', '07 Apr 2025 08:29 PM', 'ALB-50T3R4E0EH0O9D'),
(27, '<EMAIL>', 23980.00, '**********', 'Ministry of Fin', 'USB BANK PLC', 'Checking', 'UNVKCY2N', '8309', 'TP09910', 'Cyprus', 'Wire Transfer', '07 Apr 2025 08:53 PM', 'ALB-H45R00EEODT390');

-- --------------------------------------------------------

--
-- Table structure for table `ticket`
--

CREATE TABLE `ticket` (
  `id` int(10) NOT NULL,
  `tc` int(10) NOT NULL,
  `sender_name` varchar(40) NOT NULL,
  `mail` varchar(40) DEFAULT NULL,
  `subject` varchar(100) NOT NULL,
  `msg` varchar(1000) NOT NULL,
  `status` enum('Pending','Replied') DEFAULT 'Pending',
  `date` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `ticket`
--

INSERT INTO `ticket` (`id`, `tc`, `sender_name`, `mail`, `subject`, `msg`, `status`, `date`) VALUES
(1, 23117, 'James b', '<EMAIL>', 'TESTING', 'testing testing', 'Pending', '2024-09-14 17:04:02'),
(2, 25208, 'James b', '<EMAIL>', 'hi', 'hi', 'Pending', '2024-09-14 17:27:36'),
(3, 67193, 'James b', '<EMAIL>', 'Ticket', 'hi', 'Pending', '2024-09-14 17:29:21'),
(4, 16236, 'James b', '<EMAIL>', 'hi', 'hi', 'Pending', '2024-09-14 17:30:22'),
(5, 79864, 'James b', '<EMAIL>', 'hi', 'hi', 'Pending', '2024-09-15 11:08:42');

-- --------------------------------------------------------

--
-- Table structure for table `transfer`
--

CREATE TABLE `transfer` (
  `id` int(10) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `amount` decimal(20,2) DEFAULT NULL,
  `bacc_id` varchar(40) DEFAULT NULL,
  `acc_name` varchar(100) DEFAULT NULL,
  `bk_name` varchar(40) DEFAULT NULL,
  `type` varchar(20) DEFAULT NULL,
  `swift` varchar(100) DEFAULT NULL,
  `routing` varchar(100) DEFAULT NULL,
  `remarks` varchar(500) DEFAULT NULL,
  `cout` varchar(1000) DEFAULT NULL,
  `transtype` varchar(1000) DEFAULT NULL,
  `date` varchar(200) DEFAULT NULL,
  `ref` varchar(255) DEFAULT NULL,
  `status` enum('Failed','Successful','Pending','Processing','Cancelled') DEFAULT 'Processing'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `transfer`
--

INSERT INTO `transfer` (`id`, `email`, `amount`, `bacc_id`, `acc_name`, `bk_name`, `type`, `swift`, `routing`, `remarks`, `cout`, `transtype`, `date`, `ref`, `status`) VALUES
(1, '<EMAIL>', 5000.00, '**********', 'Kames Bong', 'Huj Luke', 'Savings', '742494', '***********01', 'Funds', 'Poland', 'Domestic Transfer', '11 Sep 2024 04:30 PM ', 'ALB-0EEO34DRH5T090', 'Processing'),
(2, '<EMAIL>', 50000.00, '**********', 'Seabridge', 'Seabridge', 'Savings', '************', '**********', 'Funds Movies', 'Antigua & Barbuda', 'Wire Transfer', '11 Sep 2024 04:50 PM ', 'ALB-ER40ED9T0H50O3', 'Processing'),
(3, '<EMAIL>', 5000.00, '**********', 'Brown Miraje', 'Seabridge Credit Bank', 'Savings', '214567', '', 'f', 'Poland', 'Inter-Bank Transfer', '12 Sep 2024 05:05 PM ', 'ALB-EE0H4ORT3050D9', 'Cancelled'),
(4, '<EMAIL>', 5000.00, '***********', 'BakLuks', '101001', 'Savings', '265758', '11111', 'carx', 'Poland', 'Domestic Transfer', '14 Sep 2024 02:15 PM ', 'ALB-RT405DO0390EEH', 'Failed'),
(5, '<EMAIL>', 2000.00, '*********', '********', '1010101', 'Savings', '1010101', '1101001', 'Fanx', 'Belize', 'Wire Transfer', '14 Sep 2024 04:12 PM ', 'ALB-H4O0RE005T3E9D', 'Processing'),
(6, '<EMAIL>', 5000.00, '********', 'Mike Chucks', 'BOA', 'Savings', '', '********', 'Help', 'Belarus', 'Wire Transfer', '16 Sep 2024 05:03 PM ', 'ALB-4TH539EOD00E0R', 'Successful'),
(7, '<EMAIL>', 1040654.00, '**********', 'Cyprus Auctioneers', 'Finansbank', 'Savings', 'FNNBTRIS', '8309', 'Purchases', 'Turkey', 'Wire Transfer', '03-26-2025 01:26 PM ', 'ALB-H59REE3DO00T40', 'Successful'),
(10, '<EMAIL>', 23.00, '**********', 'Ministry of Fin', 'USB BANK PLC', 'Checking', 'UNVKCY2N', '8309', 'TP09910', 'Cyprus', 'Wire Transfer', '07 Apr 2025 08:29 PM ', 'ALB-50T3R4E0EH0O9D', 'Processing'),
(11, '<EMAIL>', 23980.00, '**********', 'Ministry of Fin', 'USB BANK PLC', 'Checking', 'UNVKCY2N', '8309', 'TP09910', 'Cyprus', 'Wire Transfer', '07 Apr 2025 08:53 PM ', 'ALB-H45R00EEODT390', 'Processing');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `account`
--
ALTER TABLE `account`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admin`
--
ALTER TABLE `admin`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `alerts`
--
ALTER TABLE `alerts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `bank_payment_settings`
--
ALTER TABLE `bank_payment_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `bk_settings`
--
ALTER TABLE `bk_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cheque_deposits`
--
ALTER TABLE `cheque_deposits`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_cheque_number` (`cheque_number`),
  ADD KEY `idx_account_number` (`account_number`),
  ADD KEY `idx_clearance_status` (`clearance_status`);

--
-- Indexes for table `crypto`
--
ALTER TABLE `crypto`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `live_transfer`
--
ALTER TABLE `live_transfer`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `message`
--
ALTER TABLE `message`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `temp_account`
--
ALTER TABLE `temp_account`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `temp_transfer`
--
ALTER TABLE `temp_transfer`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `ticket`
--
ALTER TABLE `ticket`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transfer`
--
ALTER TABLE `transfer`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `account`
--
ALTER TABLE `account`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `admin`
--
ALTER TABLE `admin`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `alerts`
--
ALTER TABLE `alerts`
  MODIFY `id` int(100) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=42;

--
-- AUTO_INCREMENT for table `bank_payment_settings`
--
ALTER TABLE `bank_payment_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `bk_settings`
--
ALTER TABLE `bk_settings`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `cheque_deposits`
--
ALTER TABLE `cheque_deposits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `crypto`
--
ALTER TABLE `crypto`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `live_transfer`
--
ALTER TABLE `live_transfer`
  MODIFY `id` int(255) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `message`
--
ALTER TABLE `message`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `temp_account`
--
ALTER TABLE `temp_account`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `temp_transfer`
--
ALTER TABLE `temp_transfer`
  MODIFY `id` int(100) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `ticket`
--
ALTER TABLE `ticket`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `transfer`
--
ALTER TABLE `transfer`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
