<?php
/**
 * Authentication and Security Functions
 * Shared authentication system for installer modules
 */

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include path manager for dynamic path handling
require_once __DIR__ . '/path-manager.php';

// Configuration
$session_timeout = 3600; // 1 hour
$file_check_interval = 300; // 5 minutes
$critical_files = [
    __DIR__ . '/../index.php',
    __DIR__ . '/../database.php',
    __DIR__ . '/../includes/auth.php',
    __DIR__ . '/../includes/credentials.json'
];

/**
 * Check for file modifications and unauthorized access
 */
function checkSecurityThreats() {
    global $session_timeout, $file_check_interval, $critical_files;

    // Check session timeout
    if (isset($_SESSION['installer_login_time'])) {
        $session_age = time() - $_SESSION['installer_login_time'];
        if ($session_age > $session_timeout) {
            logSecurityEvent('Session timeout exceeded', 'WARNING', [
                'session_age' => $session_age,
                'timeout_limit' => $session_timeout
            ]);
            forceLogout('Session timeout');
            return false;
        }
    }

    // Check file modifications (every 5 minutes)
    $last_file_check = $_SESSION['last_file_check'] ?? 0;
    if (time() - $last_file_check > $file_check_interval) {
        $file_hashes = $_SESSION['file_hashes'] ?? [];

        foreach ($critical_files as $file) {
            if (file_exists($file)) {
                $current_hash = md5_file($file);
                $stored_hash = $file_hashes[basename($file)] ?? null;

                if ($stored_hash && $stored_hash !== $current_hash) {
                    logSecurityEvent('Critical file modified', 'CRITICAL', [
                        'file' => basename($file),
                        'old_hash' => $stored_hash,
                        'new_hash' => $current_hash
                    ]);
                    forceLogout('File tampering detected');
                    return false;
                }

                $file_hashes[basename($file)] = $current_hash;
            }
        }

        $_SESSION['file_hashes'] = $file_hashes;
        $_SESSION['last_file_check'] = time();
    }

    // Check for suspicious activity
    $request_count = $_SESSION['request_count'] ?? 0;
    $request_window_start = $_SESSION['request_window_start'] ?? time();

    // Reset window every 5 minutes
    if (time() - $request_window_start > 300) {
        $_SESSION['request_count'] = 1;
        $_SESSION['request_window_start'] = time();
    } else {
        $_SESSION['request_count'] = $request_count + 1;

        // Too many requests (more than 100 in 5 minutes)
        if ($_SESSION['request_count'] > 100) {
            logSecurityEvent('Suspicious activity detected', 'WARNING', [
                'request_count' => $_SESSION['request_count'],
                'time_window' => time() - $request_window_start
            ]);
            forceLogout('Suspicious activity');
            return false;
        }
    }

    return true;
}

/**
 * Force logout with security message
 */
function forceLogout($reason = 'Security check failed') {
    session_destroy();

    // Set a temporary session for the logout message
    session_start();
    $_SESSION['security_logout'] = true;
    $_SESSION['logout_reason'] = $reason;
    $_SESSION['logout_time'] = time();

    header('Location: index.php?security_logout=1');
    exit;
}

/**
 * Initialize file hashes for monitoring
 */
function initializeFileMonitoring() {
    global $critical_files;

    if (!isset($_SESSION['file_hashes'])) {
        $file_hashes = [];
        foreach ($critical_files as $file) {
            if (file_exists($file)) {
                $file_hashes[basename($file)] = md5_file($file);
            }
        }
        $_SESSION['file_hashes'] = $file_hashes;
        $_SESSION['last_file_check'] = time();
    }
}

/**
 * Log security events
 */
function logSecurityEvent($event, $level = 'INFO', $details = []) {
    $log_file = __DIR__ . '/security.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    $log_entry = [
        'timestamp' => $timestamp,
        'level' => $level,
        'event' => $event,
        'ip' => $ip,
        'user_agent' => $user_agent,
        'session_id' => session_id(),
        'details' => $details
    ];

    $log_line = json_encode($log_entry) . "\n";

    // Append to log file
    file_put_contents($log_file, $log_line, FILE_APPEND | LOCK_EX);

    // Keep only last 1000 lines
    $lines = file($log_file);
    if (count($lines) > 1000) {
        $lines = array_slice($lines, -1000);
        file_put_contents($log_file, implode('', $lines), LOCK_EX);
    }
}

/**
 * Get current installer password from encrypted storage
 */
function getInstallerPassword() {
    $credentials_file = __DIR__ . '/../includes/credentials.json';
    $encryption_key = hash('sha256', $_SERVER['SERVER_NAME'] . $_SERVER['DOCUMENT_ROOT'] . 'installer_key_2025');

    if (!file_exists($credentials_file)) {
        // Return default password if no encrypted file exists
        return 'BankInstaller2025!';
    }

    $encrypted_data = file_get_contents($credentials_file);
    $data = base64_decode($encrypted_data);

    if ($data === false) {
        return 'BankInstaller2025!';
    }

    $iv = substr($data, 0, 16);
    $encrypted = substr($data, 16);

    $decrypted_data = openssl_decrypt($encrypted, 'AES-256-CBC', $encryption_key, 0, $iv);

    if ($decrypted_data === false) {
        return 'BankInstaller2025!';
    }

    $credentials = json_decode($decrypted_data, true);

    return $credentials['password'] ?? 'BankInstaller2025!';
}

/**
 * Check if user is authenticated
 */
function isAuthenticated() {
    // Simplified authentication - just check if authenticated flag is set
    return isset($_SESSION['installer_authenticated']) &&
           $_SESSION['installer_authenticated'] === true;
}

/**
 * Require authentication or redirect to login
 */
function requireAuth() {
    if (!isAuthenticated()) {
        header('Location: index.php');
        exit;
    }

    // Perform security checks for authenticated users
    if (!checkSecurityThreats()) {
        // checkSecurityThreats() handles logout if threats detected
        return;
    }

    // Initialize file monitoring on first authenticated request
    initializeFileMonitoring();

    // Update last activity time
    $_SESSION['last_activity'] = time();
}

/**
 * Validate and sanitize input
 */
function sanitizeInput($input, $maxLength = 255) {
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return substr($input, 0, $maxLength);
}



/**
 * Create backup of a file
 */
function createBackup($filepath) {
    if (!file_exists($filepath)) {
        return false;
    }
    
    $backupDir = __DIR__ . '/backups/' . date('Y-m-d');
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
    }
    
    $filename = basename($filepath);
    $backupPath = $backupDir . '/' . $filename . '.backup.' . date('H-i-s');
    
    return copy($filepath, $backupPath) ? $backupPath : false;
}

/**
 * Validate file permissions
 */
function checkFilePermissions($files) {
    $results = [];
    foreach ($files as $file) {
        $results[$file] = [
            'exists' => file_exists($file),
            'readable' => is_readable($file),
            'writable' => is_writable($file),
            'size' => file_exists($file) ? filesize($file) : 0
        ];
    }
    return $results;
}

/**
 * Get common page header
 */
function getPageHeader($title, $description = '') {
    return '
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($title) . ' - Banking System Installer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>

        :root {
            --bg-primary: #fafafa;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f5f5f5;
            --text-primary: #1a1a1a;
            --text-secondary: #666666;
            --text-muted: #999999;
            --border-color: #e0e0e0;
            --accent-color: #2563eb;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
            --shadow-md: 0 4px 12px rgba(0,0,0,0.08);
            --shadow-lg: 0 8px 25px rgba(0,0,0,0.12);
        }

        * {
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
            font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
        }

        .navbar {
            background: var(--bg-secondary) !important;
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .navbar-brand {
            font-family: "JetBrains Mono", monospace;
            font-weight: 600;
            color: var(--text-primary) !important;
        }

        .nav-link {
            font-family: "JetBrains Mono", monospace;
            font-weight: 400;
            color: var(--text-secondary) !important;
            transition: color 0.2s ease;
        }

        .nav-link:hover {
            color: var(--accent-color) !important;
        }

        .nav-item-group {
            align-items: center;
        }

        .nav-icon-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: rgba(255, 255, 255, 0.8) !important;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-family: "JetBrains Mono", monospace;
            font-size: 0.85rem;
        }

        .nav-icon-link:hover {
            color: #fff !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            text-decoration: none;
        }

        .nav-icon-link i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .nav-icon-link span {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .main-container {
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .card-header {
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-color);
            border-radius: 12px 12px 0 0 !important;
            padding: 1.25rem;
        }

        .card-header h5, .card-header h6 {
            font-family: "JetBrains Mono", monospace;
            font-weight: 500;
            margin: 0;
            color: var(--text-primary);
        }

        .result-box {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: "JetBrains Mono", monospace;
            font-size: 0.9rem;
        }

        .file-item {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 0.75rem 0;
            transition: all 0.2s ease;
        }

        .file-item:hover {
            border-color: var(--accent-color);
            box-shadow: var(--shadow-sm);
        }

        .match-highlight {
            background: #fef3c7;
            color: #92400e;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: "JetBrains Mono", monospace;
            font-weight: 500;
        }

        code {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: "JetBrains Mono", monospace;
            font-size: 0.9em;
            border: 1px solid var(--border-color);
        }

        .btn {
            font-family: "JetBrains Mono", monospace;
            font-weight: 500;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            transition: all 0.2s ease;
            border: none;
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-info {
            background: #0891b2;
            color: white;
        }

        .btn-outline-primary {
            border: 2px solid var(--accent-color);
            color: var(--accent-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--accent-color);
            color: white;
        }

        .form-control {
            font-family: "JetBrains Mono", monospace;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 0.75rem;
            transition: all 0.2s ease;
            background: var(--bg-secondary);
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }

        .form-label {
            font-family: "JetBrains Mono", monospace;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .form-text {
            font-family: "JetBrains Mono", monospace;
            font-size: 0.85rem;
            color: var(--text-muted);
        }

        .alert {
            border: 1px solid;
            border-radius: 8px;
            padding: 1rem;
            font-family: "JetBrains Mono", monospace;
        }

        .alert-info {
            background: #eff6ff;
            color: #1e40af;
            border-color: #bfdbfe;
        }

        .alert-success {
            background: #f0fdf4;
            color: #166534;
            border-color: #bbf7d0;
        }

        .alert-warning {
            background: #fffbeb;
            color: #92400e;
            border-color: #fed7aa;
        }

        .alert-danger {
            background: #fef2f2;
            color: #991b1b;
            border-color: #fecaca;
        }

        .badge {
            font-family: "JetBrains Mono", monospace;
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-size: 0.8rem;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: "JetBrains Mono", monospace;
            font-weight: 600;
            color: var(--text-primary);
        }

        .text-muted {
            color: var(--text-muted) !important;
        }

        small {
            font-family: "JetBrains Mono", monospace;
            font-size: 0.85rem;
        }

        /* Custom scrollbar */
        .result-box::-webkit-scrollbar {
            width: 8px;
        }

        .result-box::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
            border-radius: 4px;
        }

        .result-box::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        .result-box::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* Animation for cards */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            animation: fadeInUp 0.5s ease-out;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">🏦 Banking System Installer</a>

            <!-- Advanced Module Icons -->
            <div class="navbar-nav mx-auto">
                <div class="nav-item-group d-flex gap-3">
                    <a class="nav-icon-link" href="user-management.php" title="User Management">
                        <i class="fas fa-user-shield"></i>
                        <span>Users</span>
                    </a>
                    <a class="nav-icon-link" href="config-settings.php" title="Configuration Settings">
                        <i class="fas fa-cogs"></i>
                        <span>Config</span>
                    </a>
                    <a class="nav-icon-link" href="database-settings.php" title="Database Settings">
                        <i class="fas fa-server"></i>
                        <span>DB Settings</span>
                    </a>
                </div>
            </div>

            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">🏠 Dashboard</a>
                <a class="nav-link" href="cache-manager.php" title="Cache Manager">🧹 Cache</a>
                <a class="nav-link" href="index.php?logout=1">🚪 Logout</a>
            </div>
        </div>
    </nav>
    
    <div class="container main-container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>' . htmlspecialchars($title) . '</h2>
                        ' . ($description ? '<p class="text-muted mb-0">' . htmlspecialchars($description) . '</p>' : '') . '
                    </div>
                    <a href="index.php" class="btn btn-outline-primary">← Back to Dashboard</a>
                </div>
            </div>
        </div>';
}

/**
 * Get common page footer
 */
function getPageFooter() {
    return '
        <div class="row mt-5">
            <div class="col-12">
                <div class="alert alert-warning">
                    <strong>⚠️ Remember:</strong> Delete this installer folder immediately after use for security!
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll(".alert-dismissible");
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>';
}

/**
 * Display message alert
 */
function displayMessage($message, $type = 'info') {
    if (empty($message)) return '';
    
    return '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">
                ' . htmlspecialchars($message) . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>';
}
?>
